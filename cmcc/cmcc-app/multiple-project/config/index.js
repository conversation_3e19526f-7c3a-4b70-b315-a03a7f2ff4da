let projectname = 'checkin-new'
const argvs = process.argv[3]
if (argvs) {
  if (argvs.indexOf('=') > -1) {
    projectname = argvs.split('=')[1]
  } else {
    projectname = argvs
  }
}
const glob = require('glob')
module.exports = {
  getEntry() {
    const entries = {}
    if (process.env.NODE_ENV === 'production') {
      entries[projectname] = {
        // page的入口
        entry: `src/projects/${projectname}/main.js`,
        // 模板来源
        template: 'public/index.html',
        // 在 dist/index.html 的输出
        filename: 'index.html',
        chunks: ['chunk-vendors', 'chunk-common', projectname]
      }
    } else {
      // 指定项目目录运行
      const items = glob.sync(`./src/projects/${projectname}/main.js`)
      // 所有项目目录运行
      // let items = glob.sync(`./src/projects/*/*.js`)
      // console.log('本地测试地址：')
      for (const i in items) {
        const filepath = items[i]
        const fileList = filepath.split('/')
        const fileName = fileList[fileList.length - 2]
        entries[fileName] = {
          entry: `src/projects/${fileName}/main.js`,
          // 模板来源
          template: 'public/index.html',
          // 在 dist/index.html 的输出
          filename: `${fileName}/index.html`,
          // 提取出来的通用 chunk 和 vendor chunk。
          chunks: ['chunk-vendors', 'chunk-common', fileName]
        }
        // console.log(`http://localhost:${this.getPort()}/${fileName}/index.html`)
      }
    }
    return entries
  },
  getProjectName() {
    return projectname
  },
  getPort() {
    const port = process.env.port || process.env.npm_config_port || 7080 // dev port
    return port
  }
}
