# 手厅业务侧活动、功能和快响项目

## 注意：电子发票需要先看这里
电子发票pdf解决中文不显示问题
// cMapUrl传不进去，中文会缺失，可以在 node_modules\pdfjs-dist\es5\build\pdf.js下设置cMapUrl和cMapPacked
// 在pdf.js文件内搜索cMapUrl，对应改为 baseUrl: location.origin + "/invoiceye/cmaps/",  isCompressed: true

## 项目运行打包说明

- 运行 npm run dev {projectName}
- 例：npm run dev buy-mobile

- {projectName}路径为 src/projects/\*
- 本地测试地址：http://localhost:7080/buy-mobile/index.html

* 打包 npm run build {projectName}
* 例：npm run build buy-mobile
* EIP 流程打包 npm run build，可以设置配置文件 config/index.js, 打包对应的版本项目

## 项目目录说明

- 迁移说明：https://docs.qq.com/sheet/DYldaekhpenNNdkln?tab=maqq2k
- 未迁移说明： 项目目录名为空
- 状态：上线，下线，未上线，demo

### 业务侧（APPStatic)

| 序号 | 项目名称      | 状态 |  项目目录名       |  现网地址
| ---- | ------------- | ---------- | --------------- | -------------------------------------------------- |
| 1    | 充值记录      | 上线       | recharge-record | https://mobilebj.cn/app/recharge_record/index.html |
| 2    | 手厅-电子发票 | 上线       | invoiceye-st    | https://www.mobilebj.cn/invoiceye/invoice.html     |
| 3    | 订单查询      | 上线       | check-order     | https://mobilebj.cn/app/check-order/index.html     |
| 4    | 号卡专区      | 上线       | app-card-zone   | https://mobilebj.cn/app/card-zone/index.html       |
| 5    | 微信号卡专区  | 开发       | app-card-zone   | https://sc.bj.chinamobile.com/vact/card-zone/index.html       |
| 6    | 新版签到      | 开发       | checkin   | https://mobilebj.cn/app/checkin/index.html       |
| 7    | 终端消费      | 开发       | terminal-purchase   | https://mobilebj.cn/app/terminal-purchase/index.html       |

##(录音 demo) http://localhost:7080/sound-recording/index.html
##(调试用) http://localhost:7080/debug-demo/index.html

## 业务侧设置 token 接口

- 接口地址：https://www.mobilebj.cn:7443/app/setToken?token=13911084365&misdn=13911084365&bizType=0

## 注意

分享集合页 shareCollection 测试，使用 GRAYACTStatic 地址测试回跳能力（直跳能力只支持 sc 域名），测试助力使用普通带端口地址（当前仅仅做了静态资源现网指向灰度，平台接口未做指向修改，所以 GRAYACTStatic 调的接口为现网接口，无法正常测试灰度，可否配置跨域？），微信能力与功能请区分提测

灰度代码需要现网环境验证访问方式
如果使用现网域名测试灰度代码请使用一下方式http://sc.bj.chinamobile.com/GRAYACTStatic/XXXXXXXXXXXXXXXXXXXXXXXX是你代码地址
举例：
原灰度地址：
http://st.bj.chinamobile.com:7080/activity/2020/act72/index.html
使用现网域名访问地址：
http://sc.bj.chinamobile.com/GRAYACTStatic/activity/2020/act72/index.html

订阅日历 通用接口：
日历查询： url: `/app/queryCalendarRemind`, params:{ token: '', keyword: '' }
日历添加： url: `/app/addCalendarRemind`,params:{ token: '',calendarDate: '', keyword: ''}
注意：calendarDate 为要订阅日期的拼接字符串，例如：calendarDate = '2021 年 9 月 26 日'+','+'2021 年 9 月 27 日'

## 清数据（签到）：http://st.bj.chinamobile.com:7080/actSignin/clean/all/actSignin/15810542816

## 清数据（签到）：http://st.bj.chinamobile.com:7080/actSignin/delTodaySigninInfo/15810542816

## 清数据（福利社-通用-更改 keyword 即可）：http://st.bj.chinamobile.com:7080/ActLuckyThursday/clean/all/FuXingGaoZhao/17810276593

## 技术栈

- vue
- vue-router
- axios
- UI 框架 vant-ui(https://youzan.github.io/vant/#/zh-CN/intro)
- scss
- es6+
- amfe-flexible
- postcss-loader

## 目录

```
├── build                       // 构建相关
├── public                      // 第三方不打包资源
│   ├── favicon.ico             // favicon图标
│   └── index.html              // html模板
├── src                         // 源代码
│   ├── api                    // 所有请求
│   ├── assets                 // 主题 字体等静态资源
│   ├── components             // 全局公用组件
│   ├── filters                // 全局 filter
│   ├── mixins                 // 混入
│   ├── projects               // 多目录项目
│   ├── styles                 // 全局样式
│   ├── utils                  // 全局公用方法
│   └── setting                // 打包标题配置
├── .babelrc                    // babel-loader 配置
├── eslintrc.js                 // eslint 配置项
├── .gitignore                  // git 忽略项
├── .npmrc                      // 卓望私有npm库配置
└── package.json                // package.json
```

#mixins 目录说明 ####可直接引入 index.js 获得所有 mixins 功能，也可单独引入 modules 下的某个功能，具体使用方法请查看 views/demo.vue

- double11Adaptor.js(适配活动一屏)
- dialog.js(活动常用弹窗)
- login.js(所有登录流程)
- preloading.js(图文未加载完前 loading)
- prize.js(中奖记录页常用)
- share.js(分享功能常用)

#utils 目录说明

- app（app 中的常用的能力封装，登录、页面扭转、分享等等，入口 js--main.js 中引入）
- config （项目全局参数，项目名、访问地址、分享配置,活动文案，活动规则、使用规则等）
- request（接口拦截器）
- share（微信分享，入口 js--main.js 中引入）
- utils（公共能力，入口 js--main.js 中引入）
- webtrends（插码，入口 js--main.js 中引入）

```bash
# 项目首页svn地址

# webpack 反向代理配置

```

## 规范

---

#### 编码规范

- 样式使用 [BEM](http://211.139.191.230:12630/android/fe/pages/standard/css-name.html) 命名规范

#### git 管理规范

dev 为开发分支，dev-[name] 对应开发人员私有分支，建议在各自分支开发

代码提交前请务必确保冲突已解决，确保无任何编译 error（包括 eslint 校验提示）

## 移动端自适应配置

```bash
# 基于amfe-flexible和postcss-loader

# vue-loader.conf.js配置，添加

postcss: [
    require('autoprefixer')({
        browsers: ['last 2 versions']
    }),
    require('postcss-plugin-px2rem')({
        rootValue: 75, // 设计稿宽度除以10
        unitPrecision: 8,
        propWhiteList: [],
        propBlackList: [],
        selectorBlackList: [],
        ignoreIdentifier: false,
        replace: true,
        mediaQuery: false,
        minPixelValue: 2
    })
]

```
