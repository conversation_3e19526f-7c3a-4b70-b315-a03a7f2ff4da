<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <script
      type="text/javascript"
      src="https://sc.bj.chinamobile.com/js/jquery.min.js"
    ></script>
    <script
      type="text/javascript"
      src="https://sc.bj.chinamobile.com/activityUnifyLogin/check/check-url.js"
    ></script>
    <title></title>
  </head>

  <body>
    <script>
      // 中转页的标题，加载慢的时候用，显示微信中的标题
      var shareName = decodeURIComponent(getQueryString("shareName")) || "";

      // 访问微信授权地址返回的code和state, 用于获取微信openid
      var code = getQueryString("code");
      var state = getQueryString("state");

      // 如果有code,表示曾经访问过微信授权地址，通过redirect_uri返回回来
      if (code) {
        // wxSharePath：fenxiang的地址，base64加密
        var wxSharePath = getQueryString("wxSharePath");
        var base = new Base64();
        wxSharePath = base.decode(wxSharePath);

        // 判断fenxiang的链接是vue项目链接，还是普通的链接
        var urlArr = wxSharePath.split("#/");
        // 普通fenxiang链接，直接在链接上加上code和state参数
        if (urlArr.length === 1) {
          wxSharePath = updateQueryStringParameter(wxSharePath, "code", code);
          wxSharePath = updateQueryStringParameter(wxSharePath, "state", state);
        } else {
          // vue项目链接，即可以通过location里的search获取参数，也可以通过vue里面的路由获取参数
          urlArr[0] = updateQueryStringParameter(urlArr[0], "code", code);
          urlArr[0] = updateQueryStringParameter(urlArr[0], "state", state);

          urlArr[1] = updateQueryStringParameter(urlArr[1], "code", code);
          urlArr[1] = updateQueryStringParameter(urlArr[1], "state", state);

          wxSharePath = urlArr.join("#/");
        }
        window.bjappCheckUrl(wxSharePath, function () {
          window['loca' + 'tion'].href = wxSharePath;
        });
      } else {
        // 第一次访问中转页
        var scope = getQueryString("scope");
        if (
          scope === "undefined" ||
          scope === "" ||
          scope === "null" ||
          scope === null
        ) {
          scope = "snsapi_base";
        }
        // scope = 'snsapi_base'          // 授权作用域，返回的code和state只能获取openid，这个openid不能获取用户信息
        // scope = 'snsapi_userinfo'     // 授权作用域，返回的code和state可以获取openid，这个openid可以通过后续操作获取用户信息，需要授权
        // scope参数现在暂时没啥作用，没有对应接口

        var redirectUrl = location.href; // 授权后返回中转页，中转页解析需要fenxiang的地址
        var url =
          "https://open.weixin.qq.com/connect/oauth2/authorize?appid=wx871c085b8af60631&redirect_uri=" +
          redirectUrl +
          "&response_type=code&scope=" +
          scope +
          "&state=STATE#wechat_redirect";
        window['loca' + 'tion'].href = url;
      }

      // 设置中转页fenxiang标题
      setTitle();

      // 兼容ios返回不刷新
      var isPageHide = false;
      window.addEventListener("pageshow", function () {
        var u = navigator.userAgent;
        if (isPageHide) {
          if (u.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/)) {
            window.location.reload();
          }
        }
      });
      window.addEventListener("pagehide", function () {
        isPageHide = true;
      });

      function getQueryString(name) {
        var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)", "i");
        var r = window.location.search.substr(1).match(reg);
        if (r != null) return r[2];
        return null;
      }

      function setTitle() {
        var shareName = "";
        var getShareName = decodeURIComponent(
          getQueryString("shareName") || ""
        );
        if (
          getShareName !== "null" ||
          getShareName !== null ||
          getShareName !== ""
        ) {
          shareName = getShareName;
        }
        document.title = shareName;
        var ua = navigator.userAgent;
        if (
          /\bMicroMessenger\/([\d.]+)/.test(ua) &&
          /ip(hone|od|ad)/i.test(ua)
        ) {
          var i = document.createElement("iframe");
          i.src = "/favicon.ico";
          i.style.display = "none";
          i.onload = function () {
            setTimeout(function () {
              i.remove();
            }, 9);
          };
          document.body.appendChild(i);
        }
      }

      function updateQueryStringParameter(uri, key, value) {
        if (!value) {
          return uri;
        }
        var re = new RegExp("([?&])" + key + "=.*?(&|$)", "i");
        var separator = uri.indexOf("?") !== -1 ? "&" : "?";
        if (uri.match(re)) {
          return uri.replace(re, "$1" + key + "=" + value + "$2");
        } else {
          return uri + separator + key + "=" + value;
        }
      }

      function Base64() {
        // private property
        var _keyStr =
          "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/="; // public method for encoding

        this.encode = function (input) {
          var output = "";
          var chr1, chr2, chr3, enc1, enc2, enc3, enc4;
          var i = 0;
          input = _urf8Encode(input);

          while (i < input.length) {
            chr1 = input.charCodeAt(i++);
            chr2 = input.charCodeAt(i++);
            chr3 = input.charCodeAt(i++);
            enc1 = chr1 >> 2;
            enc2 = ((chr1 & 3) << 4) | (chr2 >> 4);
            enc3 = ((chr2 & 15) << 2) | (chr3 >> 6);
            enc4 = chr3 & 63;

            if (isNaN(chr2)) {
              enc3 = enc4 = 64;
            } else if (isNaN(chr3)) {
              enc4 = 64;
            }

            output =
              output +
              _keyStr.charAt(enc1) +
              _keyStr.charAt(enc2) +
              _keyStr.charAt(enc3) +
              _keyStr.charAt(enc4);
          }

          return output;
        }; // public method for decoding

        this.decode = function (input) {
          var output = "";
          var chr1, chr2, chr3;
          var enc1, enc2, enc3, enc4;
          var i = 0;
          input = input.replace(/[^A-Za-z0-9+/=]/g, "");

          while (i < input.length) {
            enc1 = _keyStr.indexOf(input.charAt(i++));
            enc2 = _keyStr.indexOf(input.charAt(i++));
            enc3 = _keyStr.indexOf(input.charAt(i++));
            enc4 = _keyStr.indexOf(input.charAt(i++));
            chr1 = (enc1 << 2) | (enc2 >> 4);
            chr2 = ((enc2 & 15) << 4) | (enc3 >> 2);
            chr3 = ((enc3 & 3) << 6) | enc4;
            output = output + String.fromCharCode(chr1);

            if (enc3 !== 64) {
              output = output + String.fromCharCode(chr2);
            }

            if (enc4 !== 64) {
              output = output + String.fromCharCode(chr3);
            }
          }

          output = _utf8Decode(output);
          return output;
        }; // private method for UTF-8 encoding

        var _urf8Encode = function _urf8Encode(string) {
          string = string.replace(/\r\n/g, "\n");
          var utftext = "";

          for (var n = 0; n < string.length; n++) {
            var c = string.charCodeAt(n);

            if (c < 128) {
              utftext += String.fromCharCode(c);
            } else if (c > 127 && c < 2048) {
              utftext += String.fromCharCode((c >> 6) | 192);
              utftext += String.fromCharCode((c & 63) | 128);
            } else {
              utftext += String.fromCharCode((c >> 12) | 224);
              utftext += String.fromCharCode(((c >> 6) & 63) | 128);
              utftext += String.fromCharCode((c & 63) | 128);
            }
          }

          return utftext;
        }; // private method for UTF-8 decoding

        var _utf8Decode = function _utf8Decode(utftext) {
          var string = "";
          var i = 0;
          var c = 0;
          var c1 = 0;
          var c2 = 0;
          var c3 = 0;

          while (i < utftext.length) {
            c = utftext.charCodeAt(i);

            if (c < 128) {
              string += String.fromCharCode(c);
              i++;
            } else if (c > 191 && c < 224) {
              c2 = utftext.charCodeAt(i + 1);
              string += String.fromCharCode(((c & 31) << 6) | (c2 & 63));
              i += 2;
            } else {
              c2 = utftext.charCodeAt(i + 1);
              c3 = utftext.charCodeAt(i + 2);
              string += String.fromCharCode(
                ((c & 15) << 12) | ((c2 & 63) << 6) | (c3 & 63)
              );
              i += 3;
            }
          }

          return string;
        };
      }
    </script>
  </body>
</html>
