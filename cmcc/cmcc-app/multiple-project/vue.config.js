'use strict'
const path = require('path')
const pageConfig = require('./config')

function resolve(dir) {
  return path.join(__dirname, dir)
}

// If your port is set to 80,
// use administrator privileges to execute the command line.
// For example, Mac: sudo npm run
// You can change the port by the following methods:
// port = 9527 npm run dev OR npm run dev --port = 9528
const port = pageConfig.getPort() // dev port

// All configuration item explanations can be find in https://cli.vuejs.org/config/
const pages = pageConfig.getEntry()
const projectName = pageConfig.getProjectName()
const defaultConfig = require(`./src/projects/${projectName}/settings`)
// 设置 项目名称
const name = defaultConfig.title
module.exports = {
  publicPath: process.env.NODE_ENV === 'production' ? './' : '/',
  outputDir: 'dist/' + projectName,
  pages: pages,
  assetsDir: 'static',
  /**
   * You will need to set publicPath if you plan to deploy your site under a sub path,
   * for example GitHub Pages. If you plan to deploy your site to https://foo.github.io/bar/,
   * then publicPath should be set to "/bar/".
   * In most cases please use '/' !!!
   * Detail: https://cli.vuejs.org/config/#publicpath
   */
  lintOnSave: true,
  productionSourceMap: false,
  devServer: {
    port: port,
    open: false,
    overlay: {
      warnings: false,
      errors: true
    },
    proxy: {
      '': {
        target: 'https://sc.bj.chinamobile.com',
        // target: 'https://st.bj.chinamobile.com:7443',
        // target: 'http://10.12.70.196:10080',
        changeOrigin: true
      },
      '/app': {
        target: 'https://www.mobilebj.cn:7443',
        changeOrigin: true
      }
    }
    // before: require('./mock/mock-request.js')
  },
  configureWebpack: {
    // provide the projects's title in webpack's name field, so that
    // it can be accessed in index.html to inject the correct title.
    name: name,
    resolve: {
      alias: {
        '@': resolve('src')
      }
    },
    devtool: 'source-map',
    optimization: {
      splitChunks: {
        cacheGroups: {
          common: {
              name: 'chunk-common',
              minChunks: 3,
              priority: 2,
              chunks: 'initial',
              reuseExistingChunk: true//如果当前块包含已经从主包中分离出来的模块，那么该模块将被重用，而不是生成新的模块
          }
        },
      },
    },
  },
  css: {
    loaderOptions: {
      postcss: {
        plugins: [
          require('postcss-plugin-px2rem')({
            rootValue: 75,
            unitPrecision: 8,
            propWhiteList: [],
            propBlackList: [],
            selectorBlackList: [],
            ignoreIdentifier: false,
            replace: true,
            mediaQuery: false,
            minPixelValue: 2,
            exclude: /(node_module)/
          })
        ]
      }
    }
  },
  chainWebpack(config) {
    config.plugins.delete('preload-checkin-new') // TODO: need test
    config.plugins.delete('prefetch-checkin-new') // TODO: need test

    // set svg-sprite-loader
    config.module.rule('svg').exclude.add(resolve('src/icons')).end()
    config.module
      .rule('icons')
      .test(/\.svg$/)
      .include.add(resolve('src/icons'))
      .end()
      .use('svg-sprite-loader')
      .loader('svg-sprite-loader')
      .options({
        symbolId: 'icon-[name]'
      })
      .end()

    // set preserveWhitespace
    config.module
      .rule('vue')
      .use('vue-loader')
      .loader('vue-loader')
      .tap((options) => {
        options.compilerOptions.preserveWhitespace = true
        return options
      })
      .end()

    config
      // https://webpack.js.org/configuration/devtool/#development
      .when(process.env.NODE_ENV === 'development', (config) => config.devtool('cheap-source-map'))
  }
}
