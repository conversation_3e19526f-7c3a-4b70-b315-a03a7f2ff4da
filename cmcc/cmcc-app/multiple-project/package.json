{"name": "aspire-mobile-template", "version": "2.0.0", "description": "A vue mobile project", "author": "fengxiqiu <<EMAIL>>", "license": "MIT", "scripts": {"dev": "node config/dev-server.js", "start": "vue-cli-service serve", "build": "vue-cli-service build", "lint": "vue-cli-service lint"}, "dependencies": {"@lucky-canvas/vue": "^0.1.10", "@tweenjs/tween.js": "^16.11.0", "@vant/area-data": "^1.2.3", "amfe-flexible": "^2.2.1", "animate.css": "^4.1.1", "axios": "0.18.1", "clipboard": "^2.0.8", "core-js": "^3.20.2", "crypto-js": "^4.1.1", "decimal.js": "^10.3.1", "echarts": "^5.2.2", "font-spider": "^1.3.5", "html2canvas": "^1.0.0-rc.7", "inobounce": "^0.2.0", "jquery": "^3.5.1", "js-base64": "^3.7.2", "js-pinyin": "^0.1.9", "jsencrypt": "^3.3.2", "lib-flexible": "^0.3.2", "lodash": "^4.17.21", "mockjs": "^1.1.0", "mockjs-async": "0.0.5", "nprogress": "^0.2.0", "postcss-plugin-px2rem": "^0.8.1", "sass-loader": "^7.1.0", "throttle-debounce": "^3.0.1", "vant": "^2.7.1", "vue": "2.6.10", "vue-awesome-swiper": "^4.1.1", "vue-echarts": "^6.0.0", "vue-loader": "^15.9.1", "vue-pdf": "^4.3.0", "vue-router": "3.0.6", "vue-virtual-collection": "^1.5.0", "vuex": "3.1.0", "whatwg-fetch": "^3.6.2"}, "devDependencies": {"@babel/core": "^7.0.0", "@babel/preset-env": "^7.10.4", "@babel/register": "7.0.0", "@vue/cli-plugin-babel": "^4.3.0", "@vue/cli-plugin-eslint": "^4.3.0", "@vue/cli-plugin-unit-jest": "3.6.3", "@vue/cli-service": "^4.3.0", "@vue/eslint-config-standard": "^5.1.2", "@vue/test-utils": "1.0.0-beta.29", "autoprefixer": "^9.5.1", "babel-core": "7.0.0-bridge.0", "babel-eslint": "^10.1.0", "babel-jest": "23.6.0", "babel-loader": "^8.1.0", "chalk": "2.4.2", "connect": "3.6.6", "eslint": "^6.7.2", "eslint-plugin-import": "^2.20.2", "eslint-plugin-node": "^11.1.0", "eslint-plugin-promise": "^4.2.1", "eslint-plugin-standard": "^4.0.0", "eslint-plugin-vue": "^6.2.2", "html-webpack-plugin": "3.2.0", "node-sass": "^4.9.0", "postcss-import": "^11.0.0", "postcss-loader": "^2.0.8", "postcss-px2rem": "^0.3.0", "prettier-eslint": "13.0.0", "qrcodejs2": "0.0.2", "runjs": "^4.3.2", "sass-loader": "^7.1.0", "script-ext-html-webpack-plugin": "2.1.3", "script-loader": "0.7.2", "serve-static": "^1.13.2", "svg-sprite-loader": "4.1.3", "svgo": "1.2.2", "swiper": "^4.5.1", "video.js": "^7.14.3", "vue-style-loader": "^3.0.1", "vue-template-compiler": "2.6.10", "vue-waterfall-easy": "^2.4.4"}, "engines": {"node": ">=8.9", "npm": ">= 3.0.0"}, "browserslist": ["> 1%", "last 2 versions"]}