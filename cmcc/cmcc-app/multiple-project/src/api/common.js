import request from '@/utils/request'
import CHANNEL from '../../../../../bjapp-model/vue2/js/channel'
import { authBaseApi, getUserInfoApi, getUserInfoByOpenIdApi, updateUserInfoApi, getSetShareParamsApi } from '../../../../../bjapp-model/vue2/js/wx/api'

const isIos = CHANNEL.isIOS()
let wxRelate = 'WeiXinWeb'
if (location.port !== '' || location.hostname === 'st.bj.chinamobile.com') {
  wxRelate = 'WeiXinWebtmp'
}
const pathName = 'app'
/* 设置token  http://st.bj.chinamobile.com:7080/ZeroBargain/setRedisKey?token=tpl2&value=15810542816
过期http://st.bj.chinamobile.com:7080/ZeroBargain/setPrizeInfoTimes?endTime=2019-09-23%2023:59:59
设置首次抽奖升级优惠 http://st.bj.chinamobile.com:7080/ZeroBargain/setRedisKey?key=ACT:WHITEUSER:TCSD:15810542816&value=1
设置首次抽奖plus会员 http://st.bj.chinamobile.com:7080/ZeroBargain/setRedisKey?key=ACT:WHITEUSER:PLUS:15810542816&value=1
*/
// 清除数据 http://st.bj.chinamobile.com:7080/ActivityTemplateTwo/clean/all/关键字/手机号

/* 公共部分api，基本上所有活动都需要 */
/* 获取验证码 */
export function sendSMSValidateCode(params) {
  params.delToken = true
  return request({
    url: '/ActivityUnifyLogin/sendSMSValidateCode',
    method: 'get',
    params: params
  })
}

/* 提交验证码（登录） */
export function checkSMSValidateCode(params) {
  params.delToken = true
  return request({
    url: '/ActivityUnifyLogin/checkSMSValidateCode',
    method: 'get',
    params: params
  })
}

// 获取微信基本信息
export function getWxData(params) {
  return updateUserInfoApi(params)
}

// 查询用户基本信息 -- 北移api
export function getUserInfo(params) {
  return getUserInfoApi(params)
}

// 查询用户基本信息 -- 北移api
export function getUserInfoByOpenId(params) {
  // console.log(params, 'params')
  return getUserInfoByOpenIdApi(params)
}

// 更新微信用户信息到北移
export function updateUserInfo(params) {
  return updateUserInfoApi(params)
}

/* 获取微信分享设置参数 */
export function getSetShareParams() {
  const url = window.location.href.split('#')[0]
  const params = {
    url: url,
    delToken: true
  }
  return getSetShareParamsApi(params)
}

/* 微信取消绑定 */
export function wxUnBound(params) {
  return request({
    url: '/ActivityUnifyLogin/wxUnBound',
    method: 'get',
    params: params
  })
}

/* 图文侧登录 */
export function wxTuwen(params) {
  return request({
    url: '/ActivityUnifyLogin/wxTuwen',
    method: 'get',
    params: params
  })
}

/* 微信免登录 */
export function wxLogin(params) {
  return request({
    url: '/ActivityUnifyLogin/wxLogin',
    method: 'get',
    params: params
  })
}

/* 获取openid */
export function authBase(params) {
  return authBaseApi(params)
}

// 获取加密sign
export function getSign(params) {
  return request.get('/ActivityUnifyLogin/getSign', {
    params
  })
}

// 获取token
export function uniTokenValidate(params) {
  params.delToken = false
  return request.get('/ActivityUnifyLogin/uniTokenValidate', {
    params
  })
}

// 获取可配置的banner接口
export function operate_unifyH5(params) {
  return request({
    url: `/${pathName}/operate_unifyH5`,
    method: 'post',
    params: params
  })
}

// 腰封banner、置顶滚动、弹框
export function encoperate_unifyH5(params) {
  const ver = sessionStorage.getItem('version')
  params.behaviorCode = 10831
  params.ver = 'bjservice_ios_8.4.0'
  params.os = 'ios'
  if (!isIos && ver === '8.4.1') {
    params.ver = 'bjservice_and_8.4.1'
    params.os = 'and'
  }
  return request({
    url: '/app/encoperate_unifyH5',
    method: 'get',
    params: params
  })
}
