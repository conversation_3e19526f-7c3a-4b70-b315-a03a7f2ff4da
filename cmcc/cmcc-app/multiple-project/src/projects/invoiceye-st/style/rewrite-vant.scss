// 主页手风琴
.van-collapse{
    .van-cell__title{
        text-align: left;
    }
    .van-collapse-item__wrapper{
        .van-collapse-item__content{
            text-align: left;
        }
    }
    .van-cell::after{
        border: none;
    }
}

// 动作面板重新标题样式
.van-action-sheet{
    .van-action-sheet__header{
        border-bottom: 2px solid #e0e0e0;
        height: 80px;
        line-height: 80px;
        font-weight: bold;
        .van-action-sheet__close{
            background: url(../assets/base/icon-close.png) no-repeat center center;
            background-size: 30px 28px;
            right: 20px;
            &::before{
                content: ''
            }
        }
    }
}

// 表单label加粗
.van-field__label span{
    font-weight: bold;
}

div.van-switch{
    width: 80px;
    height: 36px;
    background: #F6F6F6;
    .van-switch__node{
        width: 40px;
        height: 36px;
    }
    &.van-switch--on{
        background: #1989fa;
        .van-switch__node{
            transform: translateX(40px) !important;
        }
    }
}

// 抬头页面，重写滑动单元格
.rise{
    .van-swipe-cell{
        background: #fff;
        height: 136px;
        width: 100%;
        border-bottom: 1px solid #ddd;
        &:last-child{
            border: none;
        }
        .van-swipe-cell__wrapper{
            height: 100%;
        }
        .van-swipe-cell__right{
            right: -1px;
        }
        .van-button{
            background: #FD6934;
            border: none;
            height: 100%;
            width: 200px;
        }
    }
}

// 开具页面-输入框-右侧图标
.van-field__value .van-field__body {
    position: relative;
    .van-field__right-icon{
        width: 32px;
        height: 28px;
        position: absolute;
        right: 0;
        top: 50%;
        transform: translateY(-50%);
        margin-right: 5px;
        background: url(../assets/invoice/icon-rise.png) no-repeat center center;
        background-size: 100% 100%;
        .van-icon{
            line-height: initial;
            &::before{
                content: '';
            }
        }
    }
}
