@mixin ellipsis($clamp:1){
    overflow: hidden;
    text-overflow: ellipsis;
    @if($clamp==1){
        white-space: nowrap;
    }@else{
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-line-clamp: $clamp;
        -webkit-box-orient: vertical;
    }
}

@mixin flex($x:flex-start,$y:flex-start,$direction:row,$wrap:nowrap){
    display: -webkit-box;
    display: box;
    display: flex;
    align-items: $y;
    justify-content: $x;
    flex-direction: $direction;
    flex-wrap:$wrap;
}

@mixin background($width,$height,$url, $sizeX: 100%, $sizeY: 100%) {
    width: $width;
    height:$height;
	background: url('~@/projects/invoiceye-st/assets/'+$url) no-repeat center center;
	background-size: $sizeX $sizeY;
	@if ($sizeY) {
		background-size: $sizeX $sizeY;
	} @else {
		background-size: $sizeX;
	}
}
@mixin box-text($font-weight: 'normal', $line-height:'40px', $font-size: '30px', $text-align: right) {
    font-size: $font-size;
    font-weight: $font-weight;
    font-stretch: normal;
    line-height: $line-height;
    letter-spacing: 0px;
    text-align: $text-align;
    color: #ffffff;
}

@mixin rule-title-text {
    font-size: 26px;
    font-weight: bold;
    font-stretch: normal;
    line-height: 70px;
    letter-spacing: 0px;
    color: #ffffff;
}
