.index-content {
    height: 500px;
    overflow-y: auto;
}
.card-item {
    width: 100%;
    height: 230px;
    background-color: rgba(252,233,206,.33);
    border-radius: 15px;
    border: solid 2px #fbbc6a;
    margin-top: 20px;
    padding: 20px 17px 0;
    display: flex;
    justify-content: flex-start;
    position: relative;
    &__left{
        width: 160px;
        .card-item__img{
            position: relative;
            width: 160px;
            height: 160px;
            background: #fff;
            margin-bottom: 16px;
            font-size: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
            img {
                width: 160px;
                // max-height: 160px;
            }
        }

    }
    &__right{
        text-align: left;
        margin-left: 15px;
        height: 100%;
        position: relative;
        width: calc(100% - 160px - 15px);
        z-index: 2;
    }
    &__rule{
        width: 103px;
        height: 20px;
        background: url(../../assets/index/icon-rule.png) no-repeat center center;
        background-size: 100% 100%;
        margin: 0 auto;
    }
    &__name{
        color: #f72e30;
        font-size: 36px;
        font-weight: bold;
        span{
            display: inline-block;
            width: 56px;
            height: 31px;
            background: url(../../assets/index/n-i.png) no-repeat center center;
            background-size: 100% 100%;
            vertical-align: top;
            margin-left: 5px;
        }
    }
    &__desc{
        width: 100%;
        color: #282828;
        font-size: 24px;
        margin-top: 30px;
        line-height: 36px;
    }
    &__count{
        color: #303030;
        font-size: 24px;
        height: 24px;
        line-height: 24px;
        position: absolute;
        left: 0;
        bottom: calc(100% - 160px - 20px - 13px);
        white-space: nowrap;
        span{
            color: #ff3f6f;
            }
        .bar{
            width: 290px;
            height: 24px;
            background-color: #ffcdda;
            border-radius: 12px;
            display: inline-block;
            margin-left: 10px;
        }
    }
    &__state{
        width: 172px;
        position: absolute;
        right: 17px;
        bottom: 78px;
        &.no-p{
            bottom: 78px !important;
        }
        .price{
            font-size: 41px;
            color: #ef2b5c;
            font-weight: bold;
            font-style: italic;
        }
        .tips{
            position: relative;
            z-index: 3;
            width: 172px;
            height: 62px;
            background: url(../../assets/index/b.png) no-repeat center center;
            background-size: 100% 100%;
            color: #fff528;
            font-size: 28px;
            font-weight: bold;
            text-align: center;
            line-height: 62px;
            font-style: italic;

        }
        .especially-btn {
            animation: fruit 1s linear alternate infinite;
            @keyframes fruit {
                0% {
                    transform: scale(.9)
                }
                100% {
                    transform: scale(1.1)
                }
            }
        }

    }
    .no{
        width: 187px;
        height: 148px;
        background: url(../../assets/index/m.png) no-repeat center center;
        background-size: 100% 100%;
        position: relative;
        left: -15px;
        top: 15px;
        opacity: .3;
        z-index: 1;
    }
}
