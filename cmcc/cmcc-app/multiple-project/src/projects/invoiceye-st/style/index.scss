.index{
    position: fixed;
    left: 0;
    top: 0;
    right: 0;
    bottom: 0;
    background: url(../assets/index/bg.png) no-repeat center center;
    background-size: 100% 100%;
    padding-top: 58px;
    overflow-y: auto;
    &-rule{
        position: absolute;
        left: 0;
        top: 30px;
        width: 160px;
        height: 45px;
        color: #faf8ec;
        font-size: 26px;
        text-align: center;
        line-height: 45px;
        background: #dc3e31;
        border-radius: 0 22.5px 22.5px 0;
        letter-spacing: 2px;
    }
    &-prize{
        position: absolute;
        right: 0;
        top: 30px;
        width: 160px;
        height: 45px;
        color: #faf8ec;
        font-size: 26px;
        text-align: center;
        line-height: 45px;
        background: #dc3e31;
        border-radius: 22.5px 0 0 22.5px;
        letter-spacing: 2px;
    }
    &-title{
        width: 670px;
        height: 322px;
        background: url(../assets/index/title.png) no-repeat center center;
        background-size: 100% 100%;
        margin: 0 auto;
    }
    &-main{
        width: 688px;
        margin: 17px auto 0;
        .header{
            width: 100%;
            display: flex;
            justify-content: space-between;
            &__text{
                width: 136px;
                height: 75px;
                background: url(../assets/index/t.png) no-repeat center center;
                background-size: 100% 100%;
                color: #ffffff;
                font-size: 30px;
                text-align: center;
                line-height: 70px;
                letter-spacing: 2px;
                &.heard__active{
                    background: url(../assets/index/a.png) no-repeat center center;
                    background-size: 100% 100%;
                    color: #e93477;
                    font-weight: bold;
                }
            }
        }
        .index-item{
            position: relative;
            width: 100%;
            // height: 610px;
            background-color: #ffffff;
            box-shadow: 0px 6px 4px 0px rgba(180, 22, 107, 0.79), inset 0px -6px 10px 0px rgba(248, 144, 81, 0.75);
            border-radius: 0px 0px 15px 15px;
            padding: 10px 25px 34px;
            .title {
                display: inline-block;
                margin: 0 auto;
                position: relative;
                line-height: 45px;
                // height: 40px;
                // vertical-align: middle;
                &__text {
                    position: relative;
                    z-index: 3;
                    font-size: 36px;
                    letter-spacing: 0px;
                    font-weight: 700;
                    color: #e21989;
                    display: flex;
                   justify-content: center;
                   align-items: center;
                   padding: 0 60px;
                }
                &__tip {
                    position: relative;
                    left: -10px;
                    top: -10px;
                    // width: 100%;
                    height: 17px;
                    background-image: linear-gradient(180deg,
                        #fffdfa 0%,
                        #feb8ca 100%);
                    border-radius: 9px;
                    z-index: 2;
                }
                &::before{
                    content: '';
                    width: 30px;
                    height: 23px;
                    background: url(../assets/index/icon-dec.png) no-repeat center center;
                    background-size: 100% 100%;
                    position: absolute;
                    top: 10px;
                    left: 0px;
                    z-index: 3;
                }
                &::after{
                    content: '';
                    width: 30px;
                    height: 23px;
                    background: url(../assets/index/icon-dec.png) no-repeat center center;
                    background-size: 100% 100%;
                    position: absolute;
                    top: 10px;
                    right: 0px;
                    z-index: 3;
                }
            }
            .card-item {
                width: 100%;
                // height: 230px;
                background-color: rgba(252,233,206,.33);
                border-radius: 15px;
                border: solid 2px #fbbc6a;
                margin-top: 20px;
                padding: 20px 17px 0;
                display: flex;
                justify-content: flex-start;
                position: relative;
                &__count{
                    color: #303030;
                    font-size: 24px;
                    height: 24px;
                    line-height: 24px;
                    position: absolute;
                    left: 0;
                    bottom: calc(100% - 160px - 20px - 13px);
                    white-space: nowrap;
                    span{
                        color: #ff3f6f;
                        }
                    .bar{
                        width: 290px;
                        height: 24px;
                        background-color: #ffcdda;
                        border-radius: 12px;
                        display: inline-block;
                        margin-left: 10px;
                    }
                }


            }
        }
        .index-item0 {
            height: 610px;
        }
        .index-item1 {
            height: 610px;
        }
        .index-item2 {
            height: 452px;
            .item-other__btn {
                position: absolute;
                right: 119px;
                bottom: 28px;
                width: 129px;
                height: 129px;
                background: url(../assets/index/content-wed-btn.png) no-repeat center center;
                background-size: 100% 100%;
                animation: fruit 1s linear alternate infinite;

                @keyframes fruit {
                    0% {
                        transform: scale(.9)
                    }
                    100% {
                        transform: scale(1.1)
                    }
                }
            }
        }
        .index-item3 {
            height: 452px;
            // .title{
            //     width: 545px;
            //     height: 41px;
            //     background: url(../assets/index/title-4.png) no-repeat center center;
            //     background-size: 100% 100%;
            // }
            .item-other__btn {
                position: absolute;
                right: 60px;
                bottom: 30px;
                width: 263px;
                height: 132px;
                background: url(../assets/index/content-thu-btn.png) no-repeat center center;
                background-size: 100% 100%;

                animation: fruit 1s linear alternate infinite;
                @keyframes fruit {
                    0% {
                        transform: scale(.9)
                    }
                    100% {
                        transform: scale(1.1)
                    }
                }
            }
        }
        .index-item4 {
            min-height: 452px;
            .index-content {
                // min-height: 352px;
                // overflow-y: auto;
                margin-top: 20px;
            }
            .card-item {
                width: 634px;
                min-height: 318px;
                background-color: rgba(252,233,206,.33);
                border-radius: 15px;
                border: solid 2px #fbbc6a;
                // margin-bottom: 20px;
                margin-top: 0;
                flex-direction: column;
                justify-content: flex-start;
                align-items: flex-start;
                color: #f72e30;
                &__top {
                    display: flex;
                    width: 100%;
                }
                &__img {
                    width: 200px;
                    height: 200px;
                    // background-color: #ffffff;
                    border-radius: 8px;
                    margin: 21px 25px 0 17px;
                    font-size: 16px;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    overflow: hidden;
                    img {
                        max-width: 200px;
                        max-height: 200px;
                    }
                }
                &__rule{
                    width: 103px;
                    height: 20px;
                    background: url(../assets/index/icon-rule.png) no-repeat center center;
                    background-size: 100% 100%;
                    margin: 21px 0 31px 0;
                }
                &__count{
                    // margin: 50px auto 36px;
                    width: 100%;
                    position: relative;
                    left: 0;
                    right: 0;
                    // margin-top: 20px;
                    margin: 40px 0 0;
                    padding:0 45px 0 7px;
                    bottom: 20px;
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                }
                .area {
                    flex: 1;
                    padding-right: 20px;

                }
                .area-right {
                    display: flex;
                    justify-content: space-between;
                    margin-top: 14px;
                    position: relative;
                }
                &__name {
                    max-width: 370px;
                    // min-height: 35px;
                    margin: 13px 0;
                    text-align: left;
                    font-size: 36px;
                    font-weight: bold;
                    font-stretch: normal;
                    line-height: 56px;
                    letter-spacing: 0px;
                    color: #f72e30;
                    white-space: nowrap;
                    overflow: hidden;
                    text-overflow: ellipsis;

                }
                &__desc {
                    font-size: 24px;
                    line-height: 37px;
                    letter-spacing: 0px;
                    color: #282828;
                    text-align: left;

                }
                &__btn{
                    position: relative;
                    z-index: 3;
                    width: 172px;
                    height: 62px;
                    background: url(../assets/index/b.png) no-repeat center center;
                    background-size: 100% 100%;
                    color: #fff528;
                    font-size: 28px;
                    font-weight: bold;
                    text-align: center;
                    line-height: 62px;
                    font-style: italic;
                }
                .especially-btn {
                    animation: fruit 1s linear alternate infinite;
                    @keyframes fruit {
                        0% {
                            transform: scale(.9)
                        }
                        100% {
                            transform: scale(1.1)
                        }
                    }
                }

            }
            .expect {
                color: #2c2c2c;
                height: 250px;
                line-height: 250px;
                font-size: 26px;
            }
        }
        /deep/.van-progress__portion {
            .van-progress__pivot {
                position: absolute;
                left: 0 !important;
                right: 0;
                height: 24px;
                font-size: 16px;
                font-weight: normal;
                font-stretch: normal;
                line-height: 24px;
                letter-spacing: 0px;
                color: #ffffff;
                background: rgba(255, 63, 111 ,0) !important;
            }
        }
        .no{
            width: 187px;
            height: 148px;
            background: url(../assets/index/m.png) no-repeat center center;
            background-size: 100% 100%;
            position: absolute;
            right: 0;
            top: -30px;
            opacity: .3;
            z-index: 1;
        }
        .item-other {
            width: 100%;
            background-color: rgba(252,233,206,.33);
            border-radius: 15px;
            // border: solid 2px #fbbc6a;
            margin-top: 20px;
            display: flex;
            justify-content: flex-start;
            position: relative;
            min-height: 320px;
            overflow: hidden;
            img {
                width: 100%;
                height: 100%;
            }
        }
        &__bottom2 {
            position: relative;
            top: -15px;
            width: 694px;
            height: 182px;
            background: url(../assets/banner/banner-wed.png) no-repeat center center;
            background-size: 100% 100%;
            border: 0 ;
            z-index: 88;

        }
        &__bottom3 {
            position: relative;
            top: -15px;
            width: 694px;
            height: 182px;
            background: url(../assets/banner/banner-th.png) no-repeat center center;
            background-size: 100% 100%;
            border: 0 ;
            z-index: 88;

        }
        &__bottom4 {
            position: relative;
            top: -15px;
            width: 696px;
            height: 183px;
            background: url(../assets/banner/banner-f.png) no-repeat center center;
            background-size: 100% 100%;
            border: 0 ;
            z-index: 88;

        }
        .wrap {
            position: relative;
        }
        .van-overlay {
            position: absolute;
            display: flex;
            justify-content: center;
            align-items: center;
            font-size: 26px;
            font-weight: normal;
            font-stretch: normal;
            line-height: 43px;
            letter-spacing: 0px;
            // color: #ffffff;
            color: #2c2c2c;
            z-index: 9;
            border-radius: 0px 0px 15px 15px;
            background-color: rgba(255,255,255,1);
            box-shadow: 0px 6px 4px 0px rgba(180, 22, 107, 0.79), inset 0px -6px 10px 0px rgba(248, 144, 81, 0.75);
	        // opacity: 0.77;
        }
    }
    &-subscribe{
        position: absolute;
        left: 0;
        right: 0;
        display: flex;
        justify-content: space-between;
        align-items: center;
        width: 689px;
        height: 72px;
        margin: 20px auto;
        background: rgba(0, 0, 0, .3);
        border-radius: 36px;
        color: #faf8ec;
        font-size: 28px;
        line-height: 72px;
        text-indent: 117px;
        text-align: left;
        &::after{
            content: '';
            width: 84px;
            height: 62px;
            background: url(../assets/index/s-1.png) no-repeat center center;
            background-size: 100% 100%;
            position: absolute;
            top: 7px;
            left: 6px;
        }
        span{
            &:nth-child(1) {
                width: 121px;
                height: 54px;
                background: url(../assets/index/s-2.png) no-repeat center center;
                background-size: 100% 100%;
                display: inline-block;
                margin: 0 25px;
            }
            &:nth-child(2) {
                width: 26px;
                height: 26px;
                background: url(../assets/index/s-3.png) no-repeat center center;
                background-size: 100% 100%;
                display: inline-block;
            }
        }
    }
    &-btn{
        width: 410px;
        height: 106px;
        background: url(../assets/index/btn.png) no-repeat center center;
        background-size: 100% 100%;
        margin: 110px auto 0;
    }

}
