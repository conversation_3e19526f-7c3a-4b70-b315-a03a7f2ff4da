<template>
  <div class="index">
    <card :params="MODULE_ONE_DETAIL" />
    <card :params="MODULE_TWO_DETAIL" />
    <div class="index-question">
      <div class="index-question__header">
        <div class="title">常见问题</div>
        <div class="more" @click="toMore()">更多</div>
      </div>
      <van-collapse v-model="activeName" @change="clickAnswer(activeName)">
        <van-collapse-item
          v-for="(item, index) in PROBLEM_LIST"
          :key="'qlist' + index"
          :title="item.TITLE"
          :name="index + 1"
        >
          <div class="index-answer" v-html="item.ANSWER"></div>
        </van-collapse-item>
      </van-collapse>
    </div>
    <card :params="MODULE_THREE_DETAIL" />
  </div>
</template>

<script>
import card from '../components/card'
import INDEX_DATA from '../config/data'
import Webtrends1 from '@/utils/webtrends'
import { encByToken } from '@/projects/invoiceye-st/api/api'
import { getStorageToken } from '@/projects/invoiceye-st/utils/utils'
export default {
  components: {
    card
  },
  data() {
    return {
      MODULE_ONE_DETAIL: INDEX_DATA.MODULE_ONE_DETAIL, // 发票开具卡片
      MODULE_TWO_DETAIL: INDEX_DATA.MODULE_TWO_DETAIL, // 发票服务卡片
      MODULE_THREE_DETAIL: INDEX_DATA.MODULE_THREE_DETAIL, // 您可能需要卡片
      PROBLEM_LIST: INDEX_DATA.PROBLEM_LIST.slice(0, 6), // 首页展示问题
      activeName: ['0'],
      preActiveName: []
    }
  },
  created() {
    sessionStorage.removeItem('invoiceDetail')
  },
  methods: {
    toMore() {
      this.jumpPage('Question')
      Webtrends1.multiTrack('211206_DZFP_JHY_CYWT_GD')
    },
    clickAnswer(activeName) {
      // console.log(activeName, this.preActiveName)
      Webtrends1.multiTrack('211206_DZFP_JHY_CYWT')
    },
    // 获取插码号码
    getMobile() {
      const params = {
        token: getStorageToken(),
        name: 'invoice'
      }
      encByToken(params)
        .then((res) => {
          // console.log(res)
          if (res.mobile) {
            Webtrends1.setUserId(res.mobile, res.jtEncMisdn)
          }
        })
        .catch((err) => {
          // console.log(err)
        })
    }
  }
}
</script>

<style lang="scss" scoped>
.index {
  min-height: 100vh;
  background: #f6f6f6;
  padding: 16px 24px 40px;
  overflow-x: hidden;
  &-question {
    width: 700px;
    margin: 0 auto 16px;
    &__header {
      width: 100%;
      height: 80px;
      background: #fff;
      border-radius: 20px 20px 0 0;
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 0 20px;
      margin-bottom: 8px;
      .title {
        color: #000;
        font-size: 28px;
        font-weight: bold;
      }
      .more {
        color: #8a8a8a;
        font-size: 24px;
        background: url(../assets/base/icon-right.png) no-repeat center right;
        background-size: 20px 20px;
        padding-right: 20px;
      }
    }
  }
  &-answer {
    padding-left: 20px;
  }
}
</style>
