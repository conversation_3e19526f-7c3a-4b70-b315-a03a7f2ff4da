<template>
  <div class="question">
    <van-collapse v-model="activeName">
      <van-collapse-item
        v-for="(item, index) in PROBLEM_LIST"
        :key="'qlist' + index"
        :title="item.TITLE"
        :name="index + 1"
      >
        <!-- {{ item.ANSWER }} -->
        <div class="question-answer" v-html="item.ANSWER"></div>
      </van-collapse-item>
    </van-collapse>
  </div>
</template>

<script>
import INDEX_DATA from '../config/data'
export default {
  data() {
    return {
      PROBLEM_LIST: INDEX_DATA.PROBLEM_LIST, // 问题列表
      activeName: ['0']
    }
  }
}
</script>

<style lang="scss" scoped>
.question {
  width: 100vw;
  min-height: 100vh;
  background: #fff;
  &-answer {
    padding-left: 20px;
  }
}
</style>
