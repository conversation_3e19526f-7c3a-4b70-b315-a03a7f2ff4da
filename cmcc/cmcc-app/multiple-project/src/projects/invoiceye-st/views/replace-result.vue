<template>
  <div class="result">
    <div :class="`result-icon ${status}`"></div>
    <p class="result-title">
      {{ type | getResultIconTxt(status) }}
    </p>
    <div class="result-tips">
      {{ type | getResultTipsTxt(status) }}
    </div>
    <div class="result-btn">
      <a class="firstBnt" @click="toIndex">关闭</a>
    </div>
  </div>
</template>

<script>
import Webtrends1 from '@/utils/webtrends'

export default {
  data() {
    return {
      status: this.$route.params.status,
      type: this.$route.params.type || 'send',
      acct_id: '',
      repalce: 'replace'
    }
  },
  created () {
    let invoiceItem = sessionStorage.getItem('invoiceDetail')
    if (invoiceItem) {
      invoiceItem = JSON.parse(invoiceItem)
      this.acct_id = invoiceItem.acct_id
    }
  },
  methods: {
    // 代开发票页面
    toIndex() {
      Webtrends1.multiTrack('211206_DZFP_JHY_JGY_FPSY')
      this.$router.push({
        path: '/replace',
        name: 'Replace',
        params: {
          replace: this.replace,
          acct_id: this.acct_id
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
@import './common/css/result.scss';
</style>
