<template>
  <div class="detail">
    <div class="detail-title">发票总数 1 张</div>
    <div class="detail-list">
      <div class="detail-list__item">
        <div class="item">服务内容</div>
        <div
          class="item item-list"
          v-for="item in invoiceItemList"
          :key="item.item_id"
        >
          <span class="item-list__name">{{ item.item_name }}</span>
          <span>{{ (item.unit_price / 100).toFixed(2) }}元</span>
        </div>
        <div class="item">
          <span>发票总额</span>
          <span>
            <a class="orange">{{ totlePrice }}</a>
            元
          </span>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
export default {
  data() {
    return {
      invoiceItemList: [], // 发票服务列表
      totlePrice: 0 // 总价
    }
  },
  created() {
    let invoiceForm = sessionStorage.getItem('invoiceForm')
    if (invoiceForm) {
      invoiceForm = JSON.parse(invoiceForm)
      this.invoiceItemList = invoiceForm.invc_item_list
      this.totlePrice = invoiceForm.invc_fee
    }
  }
}
</script>
<style lang="scss" scoped>
.detail {
  height: 100vh;
  background: #f6f6f6;
  &-title {
    width: 100%;
    height: 80px;
    text-align: center;
    line-height: 80px;
    color: #8a8a8a;
    font-size: 24px;
  }
  &-list {
    width: 700px;
    margin: 0 auto;
    &__item {
      width: 100%;
      height: 220px;
      background: #fff;
      border-radius: 10px;
      padding: 20px 20px;
      display: flex;
      align-items: center;
      flex-wrap: wrap;
      .item {
        width: 100%;
        display: flex;
        align-items: center;
        justify-content: space-between;
        font-size: 24px;
        color: #000000;
      }
      .item-list {
        text-indent: 40px;
        &__name {
          color: #8a8a8a;
        }
      }
      .orange {
        color: #fd6934;
      }
    }
  }
}
</style>
