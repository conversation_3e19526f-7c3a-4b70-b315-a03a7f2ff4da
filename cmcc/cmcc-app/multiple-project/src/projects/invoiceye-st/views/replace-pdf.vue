<template>
  <div class="pdf">
    <pdf ref="pdf" :rotate="pageRotate" :src="utl" />
    <!-- <canvas id="the-canvas-show"></canvas> -->
  </div>
</template>

<script>
import { einvoiceflByAcctid } from '../api/api'
import pdf from 'vue-pdf'

export default {
  components: {
    pdf
  },
  data() {
    return {
      utl: '',
      pageRotate: 90 // pdf旋转角度
    }
  },
  created() {
    let invoiceItem = sessionStorage.getItem('invoiceDetail')
    // 请求携带的参数
    let params = {}
    if (invoiceItem) {
      invoiceItem = JSON.parse(invoiceItem)
      params = {
        invc_sts: invoiceItem.invc_sts,
        invc_num_seq: invoiceItem.invc_num_seq,
        v_type: invoiceItem.v_type,
        c_time: invoiceItem.create_time
      }
    }
    this.einvoiceflByAcctid(params)
  },
  mounted() {},
  methods: {
    // 查看电子发票
    einvoiceflByAcctid(params) {
      einvoiceflByAcctid(params)
        .then((res) => {
          const { result, pfile } = res
          if (result === '0') {
            const image = new Uint8Array(
              window
                .atob(pfile)
                .split('')
                .map(function (char) {
                  return char.charCodeAt(0)
                })
            )
            this.utl = pdf.createLoadingTask(image, {
              // cMapUrl传不进去，中文会缺失，可以在 node_modules\pdfjs-dist\es5\build\pdf.js下设置cMapUrl和cMapPacked
              url: image,
              cMapUrl: 'https://unpkg.com/browse/pdfjs-dist@2.2.228/cmaps/',
              cMapPacked: true
            })
          }
        })
        .catch((err) => {
          // console.log(err)
        })
    }
  }
}
</script>

<style lang="scss" scoped>
@import './common/css/history-pdf.scss';
</style>
