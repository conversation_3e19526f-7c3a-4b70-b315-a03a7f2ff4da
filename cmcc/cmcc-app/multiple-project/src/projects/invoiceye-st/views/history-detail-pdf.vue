<template>
  <div class="pdf">
    <pdf ref="pdf" :rotate="pageRotate" :src="utl" />
  </div>
</template>

<script>
import { einvoicefl } from '../api/api'
import pdf from 'vue-pdf'
import { getStorageToken } from '@/projects/invoiceye-st/utils/utils'

export default {
  components: {
    pdf
  },
  data() {
    return {
      utl: '',
      pageRotate: 90 // pdf旋转角度
    }
  },
  created() {
    this.$loading.show()
    let invoiceItem = sessionStorage.getItem('invoiceDetail')
    let params = {}
    if (invoiceItem) {
      invoiceItem = JSON.parse(invoiceItem)
      // console.log('invoiceItem', invoiceItem)
      params = {
        invc_sts: invoiceItem.invc_sts,
        token: getStorageToken(),
        invc_num_seq: invoiceItem.invc_num_seq,
        v_type: invoiceItem.v_type,
        c_time: invoiceItem.create_time
      }
    }
    this.einvoicefl(params)
  },
  methods: {
    // 查看电子发票
    einvoicefl(params) {
      einvoicefl(params)
        .then((res) => {
          this.$loading.hide()
          const { result, pfile } = res
          if (result === '0') {
            const image = new Uint8Array(
              window
                .atob(pfile)
                .split('')
                .map(function (char) {
                  return char.charCodeAt(0)
                })
            )
            this.utl = pdf.createLoadingTask(image, {
              // cMapUrl传不进去，中文会缺失，可以在 node_modules\pdfjs-dist\es5\build\pdf.js下设置cMapUrl和cMapPacked
              // 在pdf.js文件内搜索cMapUrl，对应改为 baseUrl: location.origin + "/invoiceye/cmaps/",  isCompressed: true   即可
              //   url: image,
              cMapUrl: 'https://cdn.jsdelivr.net/npm/pdfjs-dist@2.5.207/cmaps/',
              cMapPacked: true
            })
          }
        })
        .catch((err) => {
          // console.log(err)
        })
    }
  }
}
</script>

<style lang="scss" scoped>
@import './common/css/history-pdf.scss';
</style>
