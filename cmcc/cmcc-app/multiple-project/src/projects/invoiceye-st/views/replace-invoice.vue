<template>
  <div class="invoice">
    <!-- <div class="invoice-form"> -->
    <van-form ref="invoiceForm" class="invoice-form" validate-first>
      <div class="invoice-form__title">发票信息</div>
      <div class="invoice-form__content scroll">
        <van-field name="radio" label="抬头类型" required class="invoice-item">
          <template #input>
            <van-radio-group
              v-model="form.riseType"
              direction="horizontal"
              @change="resetForm"
            >
              <van-radio name="1" checked-color="#FD6934"
                >个人/非企业单位</van-radio
              >
              <van-radio name="2" checked-color="#FD6934">企业单位</van-radio>
            </van-radio-group>
          </template>
        </van-field>
        <van-field
          v-if="!isCompany"
          v-model="form.customerName"
          ref="customerName"
          name="customerName"
          label="抬头名称"
          placeholder="请您设置抬头名称"
          maxlength="50"
          required
          :rules="[{ required: true }]"
          @blur="changeValue('customerName')"
          class="invoice-item invoice-rise-name"
          :disabled="isGroupUser ? 'disabled' : false"
        />

        <template v-if="isCompany">
          <van-field
            v-model="form.companyName"
            ref="companyName"
            name="companyName"
            label="抬头名称"
            placeholder="请您设置公司名称"
            maxlength="50"
            required
            :rules="[{ required: true }]"
            @blur="changeValue('companyName')"
            class="invoice-item invoice-rise-name"
            :disabled="isGroupUser ? 'disabled' : false"
          />
          <van-field
            v-model="form.nsrsbh"
            name="nsrsbh"
            label="公司税号"
            placeholder="请您输入纳税人识别号"
            maxlength="20"
            onkeyup="value=value.replace(/[^\w\.\/]/ig,'')"
            autocomplete="off"
            required
            :rules="[{ required: true }]"
            class="invoice-item"
          >
            <template #input></template>
          </van-field>
        </template>
        <van-field name="invc_fee" label="发票金额" class="invoice-item">
          <template #input>
            <div class="field-right">
              <div class="price">
                <span class="light">{{ form.invc_fee }}</span>
                元
              </div>
              <div class="detail" @click.stop="jumpPage('/invoice/detail')">
                查看详情
              </div>
            </div>
          </template>
        </van-field>
        <!-- 更多内容 -->
        <template v-if="isCompany">
          <div
            class="invoice-form__content__more"
            @click="moreFillShow = !moreFillShow"
          >
            <div class="p1">
              更多内容
              <img v-if="moreFillShow" src="../assets/base/icon-up.png" />
              <img v-else src="../assets/base/icon-down.png" />
            </div>
            <div class="p2">（非必填）</div>
          </div>
          <div v-if="moreFillShow">
            <van-field
              v-model="form.companyAddress"
              rows="2"
              autosize
              label="公司地址"
              type="textarea"
              maxlength="60"
              placeholder="请您填写公司注册地址"
              show-word-limit
              class="invoice-item"
            />
            <van-field
              v-model="form.companyTelephone"
              name="companyTelephone"
              label="公司电话"
              maxlength="20"
              placeholder="请您填写公司注册电话"
              class="invoice-item"
            />
            <van-field
              v-model="form.companyBank"
              rows="2"
              maxlength="30"
              autosize
              label="开户银行"
              type="textarea"
              placeholder="请您填写公司开户银行"
              show-word-limit
              class="invoice-item"
            />
            <van-field
              v-model="form.companyAccount"
              maxlength="30"
              name="companyAccount"
              label="银行账户"
              placeholder="请您填写公司开户银行账号"
              class="invoice-item"
            />
          </div>
        </template>
      </div>
      <div class="invoice-form__title">接受方式</div>
      <div class="invoice-form__content">
        <van-field
          v-model="form.email"
          name="email"
          label="邮箱"
          placeholder="请填写邮箱"
          autocomplete="off"
          required
          :rules="[{ required: true }]"
        />
      </div>
      <div class="invoice-form__tip">
        温馨提示：
        <br />
        电子发票开具后会发送至你的邮箱，请您确认信息正确。
      </div>
      <!-- </div> -->
    </van-form>
    <div class="invoice-btn" :class="{ 'black-color': isAllValidate }">
      <a @click.stop="submitInvoice">提交</a>
    </div>
    <!-- 二次确认开具发票 -->
    <van-action-sheet
      v-model="showActionSheet"
      :title="isComfig ? '开具电子发票' : '发票抬头'"
      class="invoice-sheet"
    >
      <div class="rise-sheet" v-if="!isComfig">
        <div v-if="riseList.length === 0" class="rise-sheet__empty">
          您尚未设置发票抬头
        </div>
        <div v-else class="rise-sheet__list">
          <div v-for="item in riseList" :key="item.id" class="rise-sheet-item">
            <div class="rise-sheet-item__text">
              <span
                class="title-text"
                :class="{
                  'title-text--default': !item.isdefault
                }"
              >
                {{ item.title }}
              </span>
              <a v-if="!item.isdefault" class="icon">默认</a>
            </div>
            <div v-if="item.nsrsbh" lass="rise-sheet-item__text">
              {{ item.nsrsbh }}
            </div>
          </div>
        </div>
      </div>
      <div v-else class="confirm-sheeh">
        <van-field
          name="抬头类型"
          label="抬头类型"
          disabled
          class="confirm-sheeh-item"
        >
          <template #input>
            {{ isCompany ? '企业' : '个人/非企业单位' }}
          </template>
        </van-field>
        <van-field
          v-if="!isCompany"
          v-model="form.customerName"
          name="抬头名称"
          label="抬头名称"
          disabled
          class="confirm-sheeh-item"
        />
        <van-field
          v-else
          v-model="form.companyName"
          name="抬头名称"
          label="抬头名称"
          disabled
          class="confirm-sheeh-item"
        />
        <van-field
          v-model="form.nsrsbh"
          v-if="isCompany"
          name="公司税号"
          label="公司税号"
          disabled
          class="confirm-sheeh-item"
        />
        <van-field
          name="服务内容"
          label="服务内容"
          disabled
          class="confirm-sheeh-item"
        >
          <template #input>
            <span
              :class="isCompany ? 'orange-color' : 'red-color'"
              @click="jumpPage('/invoice/detail')"
            >
              点击预览
            </span>
          </template>
        </van-field>
        <van-field
          v-model="form.email"
          name="电子邮箱"
          label="电子邮箱"
          disabled
          class="confirm-sheeh-item"
        />
        <div class="invoice-form__tip">
          电子发票开具后会发送至你的邮箱，请您确认信息正确。
        </div>
        <div class="rise-sheet__btn" @click="confirmInvoicecg">确认开票</div>
      </div>
    </van-action-sheet>
    <div v-if="showErrToast" class="invoice-toast">
      <p class="invoice-toast__title">申请失败</p>
      <p class="invoice-toast__content">系统繁忙，请您稍后再试~</p>
    </div>
  </div>
</template>

<script>
import {
  invoicecgByAcctid,
  replaceInvoiced
} from '@/projects/invoiceye-st/api/api'
// import Webtrends1 from '@/utils/webtrends'
import CONFIG_CODE from '@/projects/invoiceye-st/config/index.js'
import invoice from './mixin/invoice'
export default {
  mixins: [invoice],
  data() {
    return {
      failPagePath: '/replace-result/submit/fail', // 错误结果页
      successPagePath: '/replace-result/submit/success' // 成功结果页
    }
  },
  computed: {},
  created() {
    let invoiceForm = sessionStorage.getItem('invoiceForm')
    // 发票详情，发票抬头详情 -- 返回
    if (invoiceForm) {
      invoiceForm = JSON.parse(invoiceForm)
      this.form = invoiceForm
    } else {
      // 发票列表过来
      let invoiceItem = sessionStorage.getItem('invoiceDetail')
      if (invoiceItem) {
        invoiceItem = JSON.parse(invoiceItem)
        // 代开发票
        this.form.acct_id = invoiceItem.acct_id
        this.form.invc_num_seq = invoiceItem.invc_num_seq
        this.form.c_time = invoiceItem.create_time
        this.form.invc_fee = invoiceItem.invc_fee
        this.form.v_type = invoiceItem.v_type
        this.form.invc_sts = invoiceItem.invc_sts
        this.form.invc_item_list = invoiceItem.invc_item_list
        // 用户未设置过默认抬头
        this.form.customerName = invoiceItem.cust_name
      }
    }
  },
  methods: {
    // --------------------------------- 开具发票相关 ---------------------------------
    // 二次确认开具发票
    confirmInvoicecg() {
      // Webtrends1.multiTrack('211206_DZFP_JHY_KJFP_QRKJ')
      // 发送开具发票请求
      this.invoicecgByAcctid()
      this.$loading.show()
    },
    // 开具发票
    invoicecgByAcctid() {
      const form = this.form
      const params = {
        invc_num_seq: form.invc_num_seq,
        c_time: form.c_time,
        v_type: form.v_type,
        dtype: form.riseType,
        title: form.customerName,
        invc_fee: form.invc_fee,
        invc_sts: form.invc_sts
      }
      if (this.isCompany) {
        params.title = form.companyName
        params.nsrsbh = form.nsrsbh
        params.dz = form.companyAddress
        params.gddh = form.companyTelephone
        params.yhmc = form.companyBank
        params.yhzh = form.companyAccount
      }

      invoicecgByAcctid(params)
        .then((res) => {
          if (res.result === CONFIG_CODE.SUCCESS_CODE) {
            const loadingParams = {
              invc_num_seq: form.invc_num_seq,
              c_time: form.c_time,
              v_type: form.v_type,
              dtype: '2',
              dvalue: form.email,
              invc_sts: form.invc_sts
            }
            // 推送发票到邮箱
            this.invoiceDownLoad(loadingParams)
          } else {
            this.$loading.hide()
            if (this.successReInvoice) {
              this.$toast(
                '您的发票已调整新票，由于开具系统异常，您可尝试前往"发票开具"选择相应发票再次开具'
              )
            } else {
              // console.log(res)
            }
            this.jumpPage(this.failPagePath, 'replace')
          }
        })
        .catch((err) => {
          this.$loading.hide()
          if (this.successReInvoice) {
            this.$toast(
              '您的发票已调整新票，由于开具系统异常，您可尝试前往"发票开具"选择相应发票再次开具'
            )
          } else {
            // console.log(err)
          }
          this.jumpPage(this.failPagePath, 'replace')
        })
    },

    // 发送发票到邮箱
    invoiceDownLoad(params) {
      replaceInvoiced(params)
        .then((res) => {
          if (res.result === CONFIG_CODE.SUCCESS_CODE) {
            this.jumpPage(this.successPagePath, 'replace')
          } else {
            this.jumpPage(this.failPagePath, 'replace')
          }
        })
        .catch((err) => {
          // console.log(err)
          this.jumpPage(this.failPagePath, 'replace')
        })
        .finally(() => {
          this.$loading.hide()
        })
    }
  },

  // 路由跳转前操作
  beforeRouteLeave(to, from, next) {
    if (to.path === '/invoice/detail' || to.path === '/rise/detail/add') {
      sessionStorage['set' + 'Item']('invoiceForm', JSON.stringify(this.form))
    } else {
      sessionStorage.removeItem('invoiceForm')
    }
    next()
  }
}
</script>

<style lang="scss" scoped>
@import '@/projects/invoiceye-st/style/mixin.scss';
@import './common/css/invoice.scss';
</style>
