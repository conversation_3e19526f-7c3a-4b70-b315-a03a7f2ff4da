import { mapState } from 'vuex'
import Webtrends1 from '@/utils/webtrends'
const mixins = {
  data() {
    return {
      invoiceItem: {},
      radio: '1',
      fromPath: sessionStorage.getItem('fromPath') || '', // 上一个页面
      showEmailSheet: false, // 邮箱弹窗
      selectedInvoiceList: [], // 已选中发票
      showReInvoiceSheet: false, // 重开二次确定弹窗
      showErrToast: false // 重开 错误的提示
    }
  },
  computed: {
    // 是否高亮
    isLightBtn() {
      if (this.radio === '1') {
        if (this.form.title && this.form.nsrsbh) {
          return true
        }
      } else {
        if (this.form.title) {
          return true
        }
      }
      return false
    },
    ...mapState({
      PDStatus: (state) => state.PDStatus,
      comonParams: (state) => state.commonInformation
    })
  },
  watch: {
    showErrToast: {
      handler(val) {
        if (val) {
          let second = 3
          clearInterval(this.timer)
          this.timer = setInterval(() => {
            second--
            if (!second) {
              this.showErrToast = false
              clearInterval(this.timer)
            }
          }, 1000)
        }
      },
      immediate: true
    }
  },
  created() {
    // console.log('666666666666666666')
  },
  mounted() {
    // console.log('5555555555555555')
    let invoiceItem = sessionStorage.getItem('invoiceDetail')
    if (invoiceItem) {
      invoiceItem = JSON.parse(invoiceItem)
      this.invoiceItem = invoiceItem
      // console.log('this.invoiceItem', this.invoiceItem)
    }
  },
  methods: {
    // 重新发送
    reSend() {
      Webtrends1.multiTrack('211206_DZFP_JHY_FPXQ_CXFS')
      this.selectedInvoiceList[0] = this.invoiceItem
      // 显示弹窗
      this.showEmailSheet = true
    },
    // 隐藏邮箱弹窗
    dialogHide() {
      this.showEmailSheet = false
    }
  }
}

export default mixins
