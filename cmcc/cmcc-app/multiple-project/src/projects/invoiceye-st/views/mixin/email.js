import { Base64 } from '@/utils/base64'
import { mapState } from 'vuex'
const mixins = {
  props: {
    invoiceList: {
      type: Array,
      default: () => []
    },
    showEmailSheet: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      title: '发送到',
      telephone: '', // 账号号码
      email139: '', // 139邮箱
      otherEmail: '', // 其他邮箱
      emailSelectedStatus: false, // 是否选中邮箱
      emailSelectedType: '139', // 选择邮箱类型
      dialogStatus: false // 邮箱弹窗
    }
  },
  computed: {
    ...mapState({
      comonParams: (state) => state.commonInformation
    })
  },
  watch: {
    showEmailSheet: {
      handler(val) {
        this.dialogStatus = val
        if (val) {
          // 重置邮箱状态
          this.emailSelectedStatus = false
          this.title = '发送到'
          // 初始化邮箱地址
          if (this.comonParams) {
            const encMisdn = this.comonParams.encMisdn
            const emailAddress = this.comonParams.email_address
            if (encMisdn) {
              const base = new Base64()
              this.telephone = base.decode(encMisdn)
              this.email139 = this.telephone + '@139.com'
            }
            if (emailAddress) {
              this.otherEmail = emailAddress
            }
          }
        }
      },
      immediate: true
    }
  },
  methods: {
    dialogCloseCallBack() {
      this.$emit('dialogHide')
    },
    emailSelect(type) {
      this.emailSelectedStatus = true
      this.emailSelectedType = type
      this.title = '下载到我的邮箱'
    }
  }

}

export default mixins
