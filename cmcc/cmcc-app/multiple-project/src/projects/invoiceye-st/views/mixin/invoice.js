import { regexpFun } from '@/projects/invoiceye-st/utils/regexp-validate'
import CONFIG_CODE from '@/projects/invoiceye-st/config/index.js'
import Webtrends1 from '@/utils/webtrends'
const mixins = {
  data() {
    return {
      form: {
        acct_id: '',
        msidn: '1',
        riseType: CONFIG_CODE.RISE_TYPE.PERSONAL,
        customerName: '',
        companyName: '',
        nsrsbh: '',
        companyAddress: '',
        companyTelephone: '',
        companyBank: '',
        companyAccount: '',
        invc_fee: '',
        email: ''
      },
      moreFillShow: false,
      showActionSheet: false,
      showVerification: false, // 弹窗
      isComfig: false, // 二次确认弹窗
      backType: '',
      riseList: [],
      personalRiseList: [],
      enterpriseRiseList: [],
      isGroupUser: false, // 1（个人用户）；2（集团用户）
      toastTimer: '',
      successReInvoice: false, // 调取 冲红、调整 成功标志
      showErrToast: false // 重开 错误的提示
    }
  },
  computed: {
    // 判断是个人还是企业
    isCompany() {
      if (this.form.riseType === CONFIG_CODE.RISE_TYPE.ENTERPRISE) {
        return true
      } else {
        return false
      }
    },
    // 验证邮箱和抬头信息，改变按钮样式
    isAllValidate() {
      if (this.form.riseType === CONFIG_CODE.RISE_TYPE.PERSONAL) {
        if (this.form.email && this.form.customerName) {
          return true
        }
      }
      if (this.form.riseType === CONFIG_CODE.RISE_TYPE.ENTERPRISE) {
        if (this.form.email && this.form.companyName && this.form.nsrsbh) {
          return true
        }
      }
      return false
    }
  },
  watch: {
    // 重开 错误的提示
    showErrToast: {
      handler(val) {
        if (val) {
          let second = 3
          clearInterval(this.toastTimer)
          this.toastTimer = setInterval(() => {
            second--
            if (!second) {
              this.showErrToast = false
              clearInterval(this.toastTimer)
            }
          }, 1000)
        }
      },
      immediate: true
    },
    'form.riseType': {
      handler(val) {
        if (val === CONFIG_CODE.RISE_TYPE.PERSONAL) {
          this.showTostTip = true
        } else {
          this.showTostTip = false
        }
      },
      immediate: true,
      deep: true
    }
  },
  methods: {
    // 检查开具发票项
    submitInvoice() {
      if (this.isReInvoice) {
        // 重开
        Webtrends1.multiTrack('211206_DZFP_JHY_FPXQ_SQTPZK_TJ')
      } else {
        // 正常开票
        Webtrends1.multiTrack('211206_DZFP_JHY_KJFP_TJ_1')
      }
      this.$refs.invoiceForm
        .validate()
        .then((res) => {
          // console.log(res)
          if (this.isCompany) {
            if (!regexpFun.regFunLoudName(this.form.companyName)) {
              this.$toast('请正确填写公司名称')
              return false
            }
            if (!regexpFun.regFunTaxationNumber(this.form.nsrsbh)) {
              this.$toast('纳税人识别号错误，请核实后再提交')
              return false
            }
          } else {
            if (!regexpFun.regFunLoudName(this.form.customerName)) {
              this.$toast('请正确填写抬头名称')
              return false
            }
          }
          if (!regexpFun.regFunEmail(this.form.email)) {
            this.$toast('请填写正确的邮箱地址')
            return false
          }

          this.isComfig = true
          this.showActionSheet = true
        })
        .catch((e) => {
          // console.log(e)
        })
    },

    // 二次确认开具发票
    confirmInvoicecg() {
      Webtrends1.multiTrack('211206_DZFP_JHY_KJFP_QRKJ')
      if (this.isReInvoice) {
        // 重开
        this.reInvoicecg()
      } else {
        // 正常开票
        this.invoicecg()
      }
      this.$loading.show()
    },
    // 切换抬头类型
    resetForm() {
      this.$refs.invoiceForm.resetValidation()
    },
    // 失去焦点时 解决Vue中文本输入框v-model双向绑定后数据不显示的问题
    changeValue(key) {
      // // console.log(this.form[key])
      // this.$set(this.form, key, this.form[key])
    }
  },
  // 路由跳转前操作
  beforeRouteLeave(to, from, next) {
    if (to.path === '/invoice/detail' || to.path === '/rise/detail/add') {
      sessionStorage['set' + 'Item']('invoiceForm', JSON.stringify(this.form))
      // console.log('one')
    } else {
      // sessionStorage.removeItem('invoiceDetail')
      sessionStorage.removeItem('invoiceForm')
      sessionStorage.removeItem('isReInvoice')
      // console.log('two')
    }
    next()
  }
}

export default mixins
