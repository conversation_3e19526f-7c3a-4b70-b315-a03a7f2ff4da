<template>
  <div class="history">
    <div v-if="emptyShow && !isLoading" class="history-nolist">
      <div class="history-nolist__content">
        <img src="../assets/base/no-invoice.png" />
        <p>抱歉，您当前尚无可查看的电子发票</p>
      </div>
    </div>
    <template v-if="!emptyShow">
      <div class="history-title">12个自然月内的已开发票</div>
      <div class="history-list">
        <div
          v-for="item in listData"
          :key="item.invc_num"
          class="history-list__item"
          @click.stop="toInvoiceDetail(item)"
        >
          <div class="left">
            <div :class="`left-title type${item.invc_type}`">
              {{ String(item.invc_type) | typeDictionary }}
            </div>
          </div>
          <div class="middle">
            <p v-if="item.v_type === '1'">
              <span>发票时间：</span>
              {{ item.create_time }}
            </p>
            <p v-else>
              <span>开票时间：</span>
              {{ item.sk_create_time }}
            </p>
            <p>
              <span>其他说明：</span>
              {{ getExplain(item) }}
            </p>
            <p :class="item.invc_sts === '5' ? 'light' : ''">
              {{ item.invc_sts | statusDictionary }}
            </p>
          </div>
          <div class="right">
            <p class="right-arrow"></p>
            <p class="right-price">
              <span>{{ item.invc_fee }}</span>
              元
            </p>
          </div>
        </div>
      </div>
    </template>
  </div>
</template>

<script>
import { queryInvoicelt } from '@/projects/invoiceye-st/api/api'
import { cmccLeadeonSSo } from '../../../../../../../bjapp-model/vue2/js/login/jt-login'
import { queryPwdStatus } from '@/projects/invoiceye-st/utils/queryServicePwdStatus.js'
import {
  toastErr,
  dateFormat,
  getStorageToken
} from '@/projects/invoiceye-st/utils/utils'
import { Checkbox } from 'vant'
// import Webtrends1 from '@/utils/webtrends'
import CONFIG_CODE from '@/projects/invoiceye-st/config/index.js'
import Webtrends1 from '@/utils/webtrends'

export default {
  components: {
    [Checkbox.name]: Checkbox
  },
  data() {
    return {
      listData: [], // 历史发票数据
      isLoading: false // 正在加载
    }
  },
  computed: {
    emptyShow() {
      if (this.listData.length > 0) {
        return false
      } else {
        return true
      }
    }
  },
  mounted() {
    this.$loading.show()
    this.isLoading = true
    const token = sessionStorage.getItem('userToken')
    if (!token) {
      cmccLeadeonSSo((res) => {
        Webtrends1.setUserId(res.data.datas.mobile, res.data.datas.jtEncMisdn)
        queryPwdStatus()
        this.queryInvoicelt()
      })
    } else {
      queryPwdStatus()
      this.queryInvoicelt()
    }
  },
  methods: {
    // 去发票详情
    toInvoiceDetail(item) {
      sessionStorage['set' + 'Item']('invoiceDetail', JSON.stringify(item))
      this.$router.push('/detail')
    },
    // 其他说明：
    getExplain(item) {
      // 其他说明，月结发票显示账期,类型5充值发票显示年月日充值开具，类型1和2的固定显示：交话费开具 类型8固定显示：办业务开具，
      const type = item.invc_type
      switch (type) {
        case '1':
          return item.invc_sts === '5'
            ? this.getOtherExplainText(item)
            : '交话费开具'
        case '2':
          return '交话费开具'
        case '5':
          return item.invc_sts === '5'
            ? '充值发票'
            : `${item.create_time}充值开具`
        case '7':
          return item.invc_sts === '5'
            ? '月结发票'
            : `${dateFormat(item.create_time, 'yyyy.MM')}账期`
        case '8':
          return '办业务开具'
        default:
          break
      }
    },
    getOtherExplainText(item) {
      let resultText = '无'
      if (item.remark) {
        const textArr = item.remark.split('--')
        if (textArr.length === 2 && textArr[0] && textArr[1]) {
          resultText =
            '账期' +
            textArr[0].substring(textArr[0].length - 8, textArr[0].length) +
            ' - ' +
            textArr[1].substring(0, 8)
        }
      }
      return resultText
    },

    // ---------------- 查询发票历史列表 ------------------
    queryInvoicelt() {
      const params = {
        token: getStorageToken(),
        type: CONFIG_CODE.INVOICE_CODE.HISTORY, // 1:月结发票 2:充值发票 3:开票历史 多个逗号分割
        transactionid: new Date().getTime()
      }
      queryInvoicelt(params)
        .then((res) => {
          console.log('发票历史queryInvoicelt', res)
          if (res.result === CONFIG_CODE.SUCCESS_CODE) {
            this.listData = res.list
            if (res.encrypt) {
              res.common = res.encrypt
              this.$store.commit('UPDATE_COMMONINFORMATION', res.common)
            }
          }
          if (res.mobile) {
            Webtrends1.setUserId(res.mobile, res.jtEncMisdn)
          }
        })
        .catch((err) => {
          toastErr(err)
        })
        .finally(() => {
          this.$loading.hide()
          this.isLoading = false
        })
    }
  }
}
</script>

<style lang="scss" scoped>
// @import '../style/rewrite-vant.scss';
.history {
  width: 100vw;
  height: 100vh;
  background: #f6f6f6;
  &-nolist {
    &__content {
      padding-top: 100px;
      img {
        width: 234px;
        height: 250px;
        margin: 0 auto 60px;
      }
      p {
        color: #000;
        font-size: 28px;
        white-space: nowrap;
      }
    }
  }
  &-title {
    font-size: 24px;
    color: #8a8a8a;
    width: 100%;
    height: 80px;
    line-height: 80px;
    text-align: center;
    white-space: nowrap;
  }
  &-list {
    width: 700px;
    // height: calc(100% - 200px);
    height: calc(100% - 100px);
    overflow-y: scroll;
    margin: 0 auto;
    &__item {
      width: 700px;
      background: #fff;
      border-radius: 10px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding-right: 24px;
      position: relative;
      padding-top: 40px;
      padding-bottom: 32px;
      margin-bottom: 16px;
      .left {
        width: 128px;
        display: flex;
        align-items: center;
        justify-content: center;
        &-title {
          position: absolute;
          left: 0;
          top: 0;
          border-radius: 10px 0 10px 0;
          width: 128px;
          height: 36px;
          text-align: center;
          line-height: 36px;
          color: #007aff;
          font-size: 24px;
          background: #d6f5ff;
          font-weight: bold;
          &.type5 {
            background: #d8f9e2;
            color: #0d8a24;
          }
          &.type1,
          &.type2,
          &.type8 {
            background: #ededed;
            color: #6f6f6f;
          }
        }
        &-select {
          width: 32px;
          height: 32px;
          border-radius: 50%;
          border: 1px solid #333;
          &.selected {
            border-color: red;
            background: red;
          }
        }
      }
      .middle {
        width: calc(100% - 128px - 148px);
        font-size: 24px;
        color: #000;
        text-align: left;
        white-space: nowrap;
        line-height: 34px;
        p {
          span {
            color: #8a8a8a;
          }
          &:last-child {
            margin-top: 10px;
          }
          &.light {
            color: #fd6934;
          }
        }
      }
      .right {
        width: 128px;
        font-size: 20px;
        color: #000;
        white-space: nowrap;
        text-align: right;
        &-arrow {
          width: 20px;
          height: 20px;
          background: url(../assets/base/icon-right.png) no-repeat center center;
          background-size: 100% 100%;
          display: inline-block;
          position: absolute;
          right: 24px;
          top: 48px;
        }
        span {
          color: #fd6934;
          font-size: 28px;
          font-weight: bold;
        }
      }
    }
  }
  &-footer {
    width: 100%;
    height: 80px;
    background: #fff;
    position: fixed;
    left: 0;
    bottom: 0;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 32px;
    &__select {
      color: #000;
      font-size: 24px;
      position: relative;
      // padding-left: 54px;
      font-weight: bold;
      // &.selected {
      //     &::after {
      //         border-color: red;
      //         background: red;
      //     }
      // }
      // &::after {
      //     content: '';
      //     width: 32px;
      //     height: 32px;
      //     border-radius: 50%;
      //     border: 1px solid #333;
      //     position: absolute;
      //     left: 0;
      //     top: 50%;
      //     transform: translateY(-50%);
      // }
    }
    &__btn {
      width: 144px;
      height: 52px;
      background: #fd6934;
      border-radius: 8px;
      line-height: 52px;
      text-align: center;
      color: #fff;
      font-size: 28px;
      &.ash {
        background: #eee;
        color: #999;
      }
    }
  }
  &-dialog__title {
    width: 100%;
    height: 80px;
    line-height: 80px;
    text-align: center;
    color: #000000;
    font-size: 28px;
    border-bottom: 2px solid #e0e0e0;
    font-weight: bold;
    position: relative;
    span {
      width: 30px;
      height: 28px;
      background: url(../assets/base/icon-close.png) no-repeat center center;
      background-size: 100% 100%;
      position: absolute;
      right: 30px;
      top: 50%;
      transform: translateY(-50%);
    }
  }
}
</style>
