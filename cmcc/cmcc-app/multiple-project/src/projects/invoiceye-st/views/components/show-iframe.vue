<template>
  <section>
    <iframe v-for="item in iframeData" :key="item" :src="item" style="width: 1px; height: 1px; display: none"></iframe>
  </section>
</template>

<script>
import { operate_unifyH5 } from '../../api/api'
export default {
  data() {
    return {
      iframeData: []
    }
  },
  mounted() {
    this.operate_unifyH5()
  },
  methods: {
    operate_unifyH5() {
      const params = {
        trafficCategoryId: '1259',
        behaviorCode: '10831',
        explain: '',
        os: 'ios',
        ver: 'bjservice_ios_8.4.0'
      }
      operate_unifyH5(params).then((res) => {
        if (res.result === 0) {
          const url = res.region[0].block[0].url || ''
          if (url) {
            this.iframeData = res.region[0].block[0].url.split('###')
          }
        }
      }).catch(() => {})
    }
  }
}
</script>

<style lang="scss" scoped>
</style>