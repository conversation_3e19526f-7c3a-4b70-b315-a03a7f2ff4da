<template>
  <van-action-sheet
    v-model="dialogStatus"
    :title="title"
    @click-overlay="dialogCloseCallBack"
    @close="dialogCloseCallBack"
  >
    <div v-if="!emailSelectedStatus" class="email-sheet">
      <div class="email-sheet__item" @click="emailSelect('139')">139邮箱</div>
      <div class="email-sheet__item" @click="emailSelect('other')">
        其他邮箱
      </div>
    </div>
    <div v-else class="email-sheet">
      <div class="email-sheet__field">
        <span>电子邮箱</span>
        <input
          v-if="emailSelectedType === '139'"
          v-model="email139"
          class="field"
          maxlength="40"
          autocomplete="off"
        />
        <input
          v-else
          v-model="otherEmail"
          class="field"
          maxlength="40"
          autocomplete="off"
        />
      </div>
      <div class="email-sheet__tips">
        139邮箱推送成功率较高，建议您使用139邮箱接收
      </div>
      <div class="email-sheet__btn" @click="downloadInvoice()">确认发送</div>
    </div>
  </van-action-sheet>
</template>

<script>
import { replaceInvoiced } from '@/projects/invoiceye-st/api/api'
import { regexpFun } from '@/projects/invoiceye-st/utils/regexp-validate'
import Webtrends1 from '@/utils/webtrends'
import email from '../mixin/email'

// import $ from 'jquery'
export default {
  mixins: [email],
  name: 'EmailSheet',
  data() {
    return {
      failPagePath: '/replace-result/send/fail', // 失败结果页
      successPagePath: '/replace-result/send/success' // 成功结果页
    }
  },
  methods: {
    // 确认发送
    downloadInvoice() {
      if (this.$route.name === 'History') {
        Webtrends1.multiTrack('211206_DZFP_JHY_KPLS_QRFS')
      } else if (this.$route.name === 'HistoryDetail') {
        Webtrends1.multiTrack('211206_DZFP_JHY_FPXQ_CXFS_QRFS')
      }

      let email = ''
      if (this.emailSelectedType === '139') {
        email = this.email139
      } else {
        email = this.otherEmail
      }
      if (!email) {
        this.$toast('请填写邮箱地址')
        return false
      }
      if (!regexpFun.regFunEmail(email)) {
        this.$toast('请填写正确的邮箱地址')
        return false
      }
      if (!this.invoiceList.length) {
        this.$toast('请先选择发票')
        return
      }
      let params = {}
      if (this.invoiceList.length === 1) {
        const invoiceItem = this.invoiceList[0]
        params = {
          invc_num_seq: invoiceItem.invc_num_seq,
          c_time: invoiceItem.create_time,
          v_type: invoiceItem.v_type,
          dtype: '2',
          dvalue: email,
          invc_sts: invoiceItem.invc_sts
        }
      }
      // 调用
      this.invoiceDownLoad(params)
      // console.log(params)
    },
    invoiceDownLoad(params) {
      replaceInvoiced(params)
        .then((res) => {
          // console.log(res)
          if (res.result === '0') {
            this.toResultPage(this.successPagePath)
          } else {
            this.toResultPage(this.failPagePath)
          }
        })
        .catch((err) => {
          // console.log(err)
          this.toResultPage(this.failPagePath)
        })
    },
    // 去结果页
    toResultPage(page) {
      if (this.$route.name === 'HistoryDetail') {
        this.jumpPage(page, 'replace')
      } else {
        this.jumpPage(page)
      }
    }
  }
}
</script>

<style lang="scss" scoped>
@import '../common/css/email.scss';
</style>
