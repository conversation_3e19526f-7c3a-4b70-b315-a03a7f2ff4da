<template>
  <div class="invoice">
    <!-- <div class="invoice-form"> -->
    <van-form ref="invoiceForm" class="invoice-form" validate-first>
      <div>
        <div class="invoice-form__title">客户信息</div>
        <van-field
          v-model="form.msidn"
          name="msidn"
          label="手机号码"
          :placeholder="form.msidn"
          disabled
          class="invoice-item"
        >
          <template #input></template>
        </van-field>
      </div>
      <div class="invoice-form__title">发票信息</div>
      <div class="invoice-form__content scroll">
        <div class="tost-icon" v-if="showTostTip">如需报销建议选择企业单位</div>
        <van-field name="radio" label="抬头类型" required class="invoice-item">
          <template #input>
            <van-radio-group
              v-model="form.riseType"
              direction="horizontal"
              @change="resetForm"
            >
              <van-radio name="1" checked-color="#FD6934"
                >个人/非企业单位</van-radio
              >
              <van-radio name="2" checked-color="#FD6934">企业单位</van-radio>
            </van-radio-group>
          </template>
        </van-field>
        <van-field
          v-if="!isCompany"
          v-model="form.customerName"
          ref="customerName"
          name="customerName"
          label="抬头名称"
          placeholder="请您设置抬头名称"
          right-icon="warning-o"
          autocomplete="off"
          maxlength="50"
          required
          :rules="[{ required: true }]"
          @blur="changeValue('customerName')"
          @click-input="clickRiseName('name')"
          @click-right-icon="clickRiseName('icon')"
          class="invoice-item invoice-rise-name"
          :disabled="isGroupUser ? 'disabled' : false"
        />
        <!-- <template #input></template>
                </van-field> -->

        <template v-if="isCompany">
          <van-field
            v-model="form.companyName"
            ref="companyName"
            name="companyName"
            label="抬头名称"
            placeholder="请您设置公司名称"
            right-icon="warning-o"
            autocomplete="off"
            maxlength="50"
            required
            :rules="[{ required: true }]"
            @blur="changeValue('companyName')"
            @click-input="clickRiseName('name')"
            @click-right-icon="clickRiseName('icon')"
            class="invoice-item invoice-rise-name"
            :disabled="isGroupUser ? 'disabled' : false"
          />
          <!-- <template #input></template>
                    </van-field> -->
          <van-field
            v-model="form.nsrsbh"
            name="nsrsbh"
            label="公司税号"
            placeholder="请您输入纳税人识别号"
            maxlength="20"
            onkeyup="value=value.replace(/[^\w\.\/]/ig,'')"
            autocomplete="off"
            required
            :rules="[{ required: true }]"
            class="invoice-item"
          >
            <template #input></template>
          </van-field>
        </template>
        <van-field name="invc_fee" label="发票金额" class="invoice-item">
          <template #input>
            <div class="field-right">
              <div class="price">
                <span class="light">{{ form.invc_fee }}</span>
                元
              </div>
              <div class="detail" @click.stop="jumpPage('/invoice/detail')">
                查看详情
              </div>
            </div>
          </template>
        </van-field>
        <!-- 更多内容 -->
        <template v-if="isCompany">
          <div
            class="invoice-form__content__more"
            @click="moreFillShow = !moreFillShow"
          >
            <div class="p1">
              更多内容
              <img v-if="moreFillShow" src="../assets/base/icon-up.png" />
              <img v-else src="../assets/base/icon-down.png" />
            </div>
            <div class="p2">（非必填）</div>
          </div>
          <div v-if="moreFillShow">
            <van-field
              v-model="form.companyAddress"
              rows="2"
              autosize
              label="公司地址"
              type="textarea"
              maxlength="60"
              placeholder="请您填写公司注册地址"
              show-word-limit
              class="invoice-item"
            />
            <van-field
              v-model="form.companyTelephone"
              name="companyTelephone"
              label="公司电话"
              maxlength="20"
              placeholder="请您填写公司注册电话"
              class="invoice-item"
            />
            <van-field
              v-model="form.companyBank"
              rows="2"
              maxlength="30"
              autosize
              label="开户银行"
              type="textarea"
              placeholder="请您填写公司开户银行"
              show-word-limit
              class="invoice-item"
            />
            <van-field
              v-model="form.companyAccount"
              maxlength="30"
              name="companyAccount"
              label="银行账户"
              placeholder="请您填写公司开户银行账号"
              class="invoice-item"
            />
          </div>
        </template>
      </div>
      <div class="invoice-form__title">接受方式</div>
      <div class="invoice-form__content">
        <van-field
          v-model="form.email"
          name="email"
          label="邮箱"
          placeholder="请填写邮箱"
          autocomplete="off"
          required
          :rules="[{ required: true }]"
        />
      </div>
      <div class="invoice-form__tip">
        温馨提示：
        <br />
        电子发票开具后会发送至你的邮箱，请您确认信息正确。
      </div>
      <!-- </div> -->
    </van-form>
    <div class="invoice-btn" :class="{ 'black-color': isAllValidate }">
      <a @click.stop="submitInvoice">提交</a>
    </div>
    <!-- 二次确认开具发票 -->
    <van-action-sheet
      v-model="showActionSheet"
      :title="isComfig ? '开具电子发票' : '发票抬头'"
      class="invoice-sheet"
    >
      <div class="rise-sheet" v-if="!isComfig">
        <div v-if="riseList.length === 0" class="rise-sheet__empty">
          您尚未设置发票抬头
        </div>
        <div v-else class="rise-sheet__list">
          <div
            v-for="item in riseList"
            :key="item.id"
            class="rise-sheet-item"
            @click="selectedRise(item)"
          >
            <div class="rise-sheet-item__text">
              <span
                class="title-text"
                :class="{
                  'title-text--default': !item.isdefault
                }"
              >
                {{ item.title }}
              </span>
              <a v-if="!item.isdefault" class="icon">默认</a>
            </div>
            <div v-if="item.nsrsbh" lass="rise-sheet-item__text">
              {{ item.nsrsbh }}
            </div>
          </div>
        </div>
        <div class="rise-sheet-foot">
          <p class="rise-sheet__btn" @click="addRise">添加常用发票抬头</p>
        </div>
      </div>
      <div v-else class="confirm-sheeh">
        <!-- <van-field v-model="form.riseType" name="抬头类型" label="抬头类型" disabled class="confirm-sheeh-item" /> -->
        <van-field
          name="抬头类型"
          label="抬头类型"
          disabled
          class="confirm-sheeh-item"
        >
          <template #input>
            {{ isCompany ? '企业' : '个人/非企业单位' }}
          </template>
        </van-field>
        <van-field
          v-if="!isCompany"
          v-model="form.customerName"
          name="抬头名称"
          label="抬头名称"
          disabled
          class="confirm-sheeh-item"
        />
        <van-field
          v-else
          v-model="form.companyName"
          name="抬头名称"
          label="抬头名称"
          disabled
          class="confirm-sheeh-item"
        />
        <van-field
          v-model="form.nsrsbh"
          v-if="isCompany"
          name="公司税号"
          label="公司税号"
          disabled
          class="confirm-sheeh-item"
        />
        <van-field
          name="服务内容"
          label="服务内容"
          disabled
          class="confirm-sheeh-item"
        >
          <template #input>
            <span
              :class="isCompany ? 'orange-color' : 'red-color'"
              @click="jumpPage('/invoice/detail')"
            >
              点击预览
            </span>
          </template>
        </van-field>
        <van-field
          v-model="form.email"
          name="电子邮箱"
          label="电子邮箱"
          disabled
          class="confirm-sheeh-item"
        />
        <div class="invoice-form__tip">
          电子发票开具后会发送至你的邮箱，请您确认信息正确。
        </div>
        <div class="rise-sheet__btn" @click="confirmInvoicecg">确认开票</div>
      </div>
    </van-action-sheet>
    <!-- 服务密码验证 -->
    <password-sheet
      v-if="showVerification"
      :backType="backType"
      @closePasswordSheet="closePasswordSheet"
      @checkServicePwdSuccess="checkServicePwdSuccess"
    ></password-sheet>
    <!-- 自定义toast -->
    <div v-if="showErrToast" class="invoice-toast">
      <p class="invoice-toast__title">申请失败</p>
      <p class="invoice-toast__content">系统繁忙，请您稍后再试~</p>
    </div>
  </div>
</template>

<script>
import {
  queryInvoiceTitle,
  invoicecg,
  invoiceBatchDL,
  validateCustomerType,
  reInvoicecg
} from '@/projects/invoiceye-st/api/api'
import PasswordSheet from '@/projects/invoiceye-st/components/password/index.vue'
import { mapState } from 'vuex'
import {
  toastErr,
  getMisdnmask,
  getStorageToken
} from '@/projects/invoiceye-st/utils/utils'
import { Base64 } from '@/utils/base64'
import Webtrends1 from '@/utils/webtrends'
import CONFIG_CODE from '@/projects/invoiceye-st/config/index.js'
import invoice from './mixin/invoice'
export default {
  mixins: [invoice],
  components: {
    PasswordSheet
  },
  data() {
    return {
      showTostTip: false, // 个人抬头类型顶部悬浮提示
      reInvoiceItem: {}, // 保存从重开过来的发票数据
      isReInvoice: false, // 是重开流程
      failPagePath: '/result/submit/fail', // 错误结果页
      successPagePath: '/result/submit/success' // 成功结果页
    }
  },
  computed: {
    ...mapState({
      PDStatus: (state) => state.PDStatus,
      commonInformation: (state) => state.commonInformation
    })
  },
  created() {
    // 校验用户类型
    this.validateCustomerType()
    let invoiceForm = sessionStorage.getItem('invoiceForm')
    const riseSaveSuccess = sessionStorage.getItem('riseSaveSuccess')
    sessionStorage.removeItem('fromPath')

    // 发票详情，发票抬头详情 -- 返回
    if (invoiceForm) {
      invoiceForm = JSON.parse(invoiceForm)
      this.form = invoiceForm
    } else {
      // 发票列表过来
      // 重开页面过来
      let invoiceItem = sessionStorage.getItem('invoiceDetail')
      if (invoiceItem) {
        invoiceItem = JSON.parse(invoiceItem)
        // 代开发票
        this.form.acct_id = invoiceItem.acct_id
        this.form.invc_num_seq = invoiceItem.invc_num_seq
        this.form.c_time = invoiceItem.create_time
        this.form.invc_fee = invoiceItem.invc_fee
        this.form.v_type = invoiceItem.v_type
        this.form.invc_sts = invoiceItem.invc_sts
        this.form.invc_item_list = invoiceItem.invc_item_list
        // 用户未设置过默认抬头
        if (!this.form.customerName && String(invoiceItem.cust_name) !== '0') {
          this.form.customerName = invoiceItem.cust_name
        }
      }

      if (this.commonInformation) {
        const encMisdn = this.commonInformation.encMisdn
        let phone = ''
        // 电话号码
        if (encMisdn) {
          const base = new Base64()
          phone = base.decode(encMisdn)
          this.form.msidn = getMisdnmask(phone)
        }
        if (this.commonInformation.email_address) {
          this.form.email = this.commonInformation.email_address
        } else {
          this.form.email = phone + '@139.com'
        }
        // 手厅存储过用户的抬头信息
        if (this.commonInformation.title) {
          if (this.commonInformation.nsrsbh) {
            // 设置默认
            this.form.riseType = CONFIG_CODE.RISE_TYPE.ENTERPRISE
            this.form.companyName = this.commonInformation.title
            this.form.nsrsbh = this.commonInformation.nsrsbh
            this.form.companyAddress = this.commonInformation.dz
            this.form.companyTelephone = this.commonInformation.gddh
            this.form.companyBank = this.commonInformation.yhmc
            this.form.companyAccount = this.commonInformation.yhzh
          } else {
            this.form.riseType = CONFIG_CODE.RISE_TYPE.PERSONAL
            this.form.customerName = this.commonInformation.title
          }
        }
      }
    }
    // 获取发票抬头
    this.queryInvoiceTitle()
    // 重开发票流程
    if (sessionStorage.getItem('isReInvoice') === 'true') {
      if (sessionStorage.getItem('invoiceDetail')) {
        this.reInvoiceItem = JSON.parse(sessionStorage.getItem('invoiceDetail'))
      }
      this.isReInvoice = true
    }
    // 添加常用发票抬头返回
    if (riseSaveSuccess === CONFIG_CODE.ADD_RISE_SUCCESS) {
      this.getRiseList()
      sessionStorage.removeItem('riseSaveSuccess')
    }
  },
  methods: {
    // --------------------------------- 开具发票相关 ---------------------------------
    // 开具发票
    invoicecg() {
      const form = this.form
      const token = getStorageToken()
      const params = {
        token,
        invc_num_seq: form.invc_num_seq,
        c_time: form.c_time,
        v_type: form.v_type,
        dtype: form.riseType,
        title: form.customerName,
        // cust_name: form.invc_num_seq,
        invc_fee: form.invc_fee,
        invc_sts: form.invc_sts
      }
      if (this.isCompany) {
        params.title = form.companyName
        params.nsrsbh = form.nsrsbh
        params.dz = form.companyAddress
        params.gddh = form.companyTelephone
        params.yhmc = form.companyBank
        params.yhzh = form.companyAccount
      }
      invoicecg(params)
        .then((res) => {
          if (res.result === CONFIG_CODE.SUCCESS_CODE) {
            const loadingParams = {
              invc_num_seq: form.invc_num_seq,
              c_time: form.c_time,
              v_type: form.v_type,
              dtype: '2',
              dvalue: form.email,
              invc_sts: form.invc_sts,
              token
            }
            this.invoiceDownLoad(loadingParams)
          } else {
            this.$loading.hide()
            if (this.successReInvoice) {
              this.$toast(
                '您的发票已调整新票，由于开具系统异常，您可尝试前往"发票开具"选择相应发票再次开具'
              )
            } else {
              // console.log(res)
            }
            this.jumpPage(this.failPagePath, 'replace')
          }
        })
        .catch((err) => {
          this.$loading.hide()
          if (this.successReInvoice) {
            this.$toast(
              '您的发票已调整新票，由于开具系统异常，您可尝试前往"发票开具"选择相应发票再次开具'
            )
          } else {
            // console.log(err)
          }
          this.jumpPage(this.failPagePath, 'replace')
        })
    },
    // 重开接口
    reInvoicecg() {
      const item = this.reInvoiceItem
      const params = {
        token: getStorageToken(),
        invc_num_seq: item.invc_num_seq,
        c_time: item.create_time,
        v_type: item.v_type,
        invc_sts: item.invc_sts,
        transactionid: new Date().getTime()
      }
      reInvoicecg(params)
        .then((res) => {
          if (res.result === CONFIG_CODE.SUCCESS_CODE) {
            this.isReInvoice = false
            this.successReInvoice = true
            sessionStorage.removeItem('isReInvoice')
            if (res.data) {
              const data = res.data
              this.form.invc_num_seq = data.invc_num_seq
              this.form.c_time = data.create_time
              this.form.invc_fee = data.invc_fee
              this.form.v_type = data.v_type
              this.form.invc_sts = data.invc_sts
            }

            this.invoicecg()
          } else {
            this.$loading.hide()
            this.jumpPage(this.failPagePath, 'replace')
          }
        })
        .catch((err) => {
          // console.log('reInvoicecg', err)
          this.$loading.hide()
          this.jumpPage(this.failPagePath, 'replace')
        })
    },
    // 发送发票
    invoiceDownLoad(params) {
      const url = '/app/invoicedl'
      invoiceBatchDL(url, params)
        .then((res) => {
          if (res.result === CONFIG_CODE.SUCCESS_CODE) {
            this.jumpPage(this.successPagePath, 'replace')
          } else {
            this.jumpPage(this.failPagePath, 'replace')
          }
        })
        .catch((err) => {
          // console.log(err)
          this.jumpPage(this.failPagePath, 'replace')
        })
        .finally(() => {
          this.$loading.hide()
        })
    },
    // ---------------------------- 抬头相关 ------------------------------------------
    // 聚焦抬头名字 点击右边图标 服务密码验证
    clickRiseName(type) {
      if (this.isGroupUser) {
        return
      }
      if (!this.PDStatus) {
        this.showVerification = true
        this.backType = type
        return
      }
      if (type === 'name') {
        if (this.isCompany) {
          this.$refs.companyName.focus()
        } else {
          this.$refs.customerName.focus()
        }
      } else if (type === 'icon') {
        this.getRiseList()
      }
    },
    // 关闭服务密码验证
    closePasswordSheet() {
      this.showVerification = false
    },
    // 验证成功后
    checkServicePwdSuccess(type) {
      // 聚焦抬头名字 验证成功后，可在输入框修改抬头，
      // 点击右边图标 验证成功后，显示常用抬头弹窗列表
      this.showVerification = false
      this.clickRiseName(type)
    },
    // 获取抬头列表
    getRiseList() {
      this.isComfig = false
      this.showActionSheet = true
      if (this.isCompany) {
        this.riseList = this.enterpriseRiseList
      } else {
        this.riseList = this.personalRiseList
      }
    },
    // 选择抬头
    selectedRise(item) {
      if (item.nsrsbh) {
        this.form.companyName = item.title
        this.form.nsrsbh = item.nsrsbh
        this.form.companyAddress = item.dz
        this.form.companyTelephone = item.gddh
        this.form.companyBank = item.yhmc
        this.form.companyAccount = item.yhzh
      } else {
        this.form.customerName = item.title
      }
      this.showActionSheet = false
    },
    // 添加抬头
    addRise() {
      Webtrends1.multiTrack('211206_DZFP_JHY_KJFP_TJCYFPTT')
      sessionStorage['set' + 'Item']('fromPath', '/invoice')
      this.jumpPage('/rise/detail/add')
    },
    // 检验用户类型
    validateCustomerType() {
      const params = { token: getStorageToken() }
      validateCustomerType(params)
        .then((res) => {
          if (String(res.result) === CONFIG_CODE.SUCCESS_CODE) {
            if (res.type === CONFIG_CODE.CUSTOMER_TYPE.GROUP_USER) {
              this.isGroupUser = true
            } else {
              this.isGroupUser = false
            }
          }
        })
        .catch((err) => {
          // console.log(err)
        })
    },
    // 查询抬头数据
    queryInvoiceTitle() {
      const params = {
        token: getStorageToken()
      }
      this.personalRiseList = []
      this.enterpriseRiseList = []
      queryInvoiceTitle(params)
        .then((res) => {
          // console.log(res)
          if (res.result === CONFIG_CODE.SUCCESS_CODE) {
            // this.riseList = res.list

            res.list.forEach((item) => {
              if (item.nsrsbh) {
                this.enterpriseRiseList.push(item)
              } else {
                this.personalRiseList.push(item)
              }
              if (!sessionStorage.getItem('invoiceForm')) {
                if (!item.isdefault) {
                  // 设置默认
                  if (item.nsrsbh) {
                    this.form.riseType = CONFIG_CODE.RISE_TYPE.ENTERPRISE
                    this.form.nsrsbh = item.nsrsbh
                    this.form.companyName = item.title
                    this.form.companyAddress = item.dz
                    this.form.companyTelephone = item.gddh
                    this.form.companyBank = item.yhmc
                    this.form.companyAccount = item.yhzh
                  } else {
                    this.form.riseType = CONFIG_CODE.RISE_TYPE.PERSONAL
                    this.form.customerName = item.title
                  }
                  this.$forceUpdate()
                }
              }
            })
          }
        })
        .catch((err) => {
          toastErr(err)
        })
    }
  }
}
</script>

<style lang="scss" scoped>
@import '@/projects/invoiceye-st/style/mixin.scss';
@import './common/css/invoice.scss';
</style>
