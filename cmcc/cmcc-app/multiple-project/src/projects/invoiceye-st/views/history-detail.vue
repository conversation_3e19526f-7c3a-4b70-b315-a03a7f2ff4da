<template>
  <div class="detail">
    <div class="detail-title">发票状态</div>
    <div class="detail-content">
      <div class="detail-content__item">
        <div class="status">
          {{ invoiceItem.invc_sts | statusDictionary }}
        </div>
        <a class="seePDF" @click="toInvoicePDF">查看电子发票</a>
      </div>
    </div>
    <div class="detail-title">发票信息</div>
    <div class="detail-content">
      <div class="detail-content__item center-left">
        <template v-if="invoiceItem.v_type !== '1'">
          <div class="left">开具时间</div>
          <a class="right">
            {{
              invoiceItem.sk_create_time
                ? dateFormat(invoiceItem.sk_create_time, 'yyyy.MM.dd hh:mm:ss')
                : ''
            }}
          </a>
        </template>
        <template v-else>
          <div class="left">发票时间</div>
          <a class="right">
            {{
              invoiceItem.create_time
                ? dateFormat(invoiceItem.create_time, 'yyyy.MM.dd')
                : ''
            }}
          </a>
        </template>
      </div>
      <div class="detail-content__item center-left">
        <div class="left">发票金额</div>
        <a class="right">
          <i>{{ invoiceItem.invc_fee }}</i>
          元
        </a>
      </div>
    </div>
    <div class="detail-footer">
      <a class="detail-footer__btn" @click="reInvoicecg">申请退票重开</a>
    </div>
    <password
      v-if="PDShow"
      @closePasswordSheet="closePasswordSheet"
      @checkServicePwdSuccess="checkServicePwdSuccess"
    />
    <email-sheet
      :show-email-sheet="showEmailSheet"
      :invoice-list="selectedInvoiceList"
      @dialogHide="dialogHide"
    />
    <van-action-sheet
      v-model="showReInvoiceSheet"
      title="申请退票重开"
      class="history-sheet"
    >
      <div class="history-sheet-main">
        <p>
          重开发票不能更改发票服务名称下的内容（发票科目），仅能更改本张发票抬头，申请退票重开后，您的本张发票将会退回注销，请您确认是否需要退票重开。
        </p>
        <div class="history-sheet__btn" @click="confirmReInvoice()">
          确认申请
        </div>
      </div>
    </van-action-sheet>
    <!-- 自定义toast -->
    <div v-if="showErrToast" class="history-detail-toast">
      <p class="history-detail-toast__title">开具失败</p>
      <p class="history-detail-toast__content">系统繁忙，请您稍后再试~</p>
    </div>
  </div>
</template>
<script>
import password from '../components/password'
import { dateFormat } from '@/projects/invoiceye-st/utils/utils'
import EmailSheet from './components/email-sheet.vue'
import Webtrends1 from '@/utils/webtrends'
import historyDetail from './mixin/history-detail'
export default {
  mixins: [historyDetail],
  components: {
    password,
    EmailSheet
  },
  data() {
    return {
      PDShow: false
    }
  },
  methods: {
    dateFormat: dateFormat,
    toInvoicePDF() {
      this.$router.push('/pdf')
    },
    // 确认 申请退票重开
    confirmReInvoice() {
      Webtrends1.multiTrack('P00000034040', '中国移动APP_电子发票_发票详情_申请退票重开_确认申请')
      sessionStorage['set' + 'Item']('invoiceDetail', JSON.stringify(this.invoiceItem))
      sessionStorage['set' + 'Item']('isReInvoice', 'true')
      this.jumpPage('./../invoice', 'replace')
    },
    // 申请退票重开
    reInvoicecg() {
      Webtrends1.multiTrack('P00000034038', '中国移动APP_电子发票_发票详情_申请退票重开')
      // 本次未输入过客服密码，要先输入
      if (!this.PDStatus) {
        this.PDShow = true
        return
      }
      this.showReInvoiceSheet = true
    },
    // 关闭服务密码验证
    closePasswordSheet() {
      this.PDShow = false
    },
    // 检验服务验证成功
    checkServicePwdSuccess() {
      Webtrends1.multiTrack('P00000034039', '中国移动APP_电子发票_发票详情_申请退票重开_提交')
      this.PDShow = false
      this.reInvoicecg()
    }
  }
}
</script>

<style lang="scss" scoped>
@import './common/css/history-detail.scss';
</style>
