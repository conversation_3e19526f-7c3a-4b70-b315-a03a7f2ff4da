<template>
  <div class="list">
    <template v-if="!emptyShow">
      <div
        class="list-title"
        :class="this.invoiceyeType === 'other' ? 'other-title' : ''"
      >
        {{ title }}
      </div>
      <div class="list-list">
        <div
          v-for="item in list"
          :key="item.invc_num_seq"
          class="list-list__item"
        >
          <div class="left">
            <p class="text-item">
              {{ pagePrarams.timeKey }}：
              <span>
                {{
                  invoiceyeType === 'yuejie'
                    ? dateFormat(item.create_time, 'yyyy年M月')
                    : dateFormat(item.create_time, 'yyyy.MM.dd')
                }}
              </span>
            </p>
            <p class="text-item">
              {{ pagePrarams.typeKey }}：
              <span>{{ String(item.invc_type) | otherTypeDictionary }}</span>
            </p>
            <p class="text-item">
              {{ pagePrarams.statusKey }}：
              <span>{{ pagePrarams.statusVal }}</span>
            </p>
          </div>
          <div class="right">
            <p>
              <a>{{ item.invc_fee }}</a>
              元
            </p>
            <div class="btn" @click.stop="toInvoice(item)">点击开票</div>
          </div>
        </div>
      </div>
    </template>
    <div v-if="emptyShow && !isLoading" class="list-nolist">
      <div class="list-nolist__content">
        <img src="../assets/base/no-invoice.png" />
        <p>抱歉，您当前尚无可查看的电子发票</p>
      </div>
    </div>
    <!-- <p class="list-title">超过12个月的发票请您联系就近营业厅开具</p> -->
  </div>
</template>

<script>
import { queryInvoicelt } from '@/projects/invoiceye-st/api/api'
import {
  dateFormat,
  toastErr,
  getStorageToken
} from '@/projects/invoiceye-st/utils/utils'
import Webtrends1 from '@/utils/webtrends'
import CONFIG_CODE from '@/projects/invoiceye-st/config/index.js'
import invoiceyeList from './mixin/invoiceye-list'
export default {
  mixins: [invoiceyeList],
  data() {
    return {
      title: '12个自然月内的每月消费后的月结发票'
    }
  },
  computed: {
    emptyShow() {
      if (this.list.length > 0) {
        return false
      } else {
        return true
      }
    }
  },
  created() {
    // console.log(this.$route.params.type)
    this.invoiceyeType = this.$route.params.type
    this.getPagePrarams()
    this.queryInvoicelt()
  },
  methods: {
    // --------------- 操作 ----------------------
    toInvoice(item) {
      if (this.invoiceyeType === 'chongzhi') {
        Webtrends1.multiTrack('211206_DZFP_JHY_CZFP_DJKP')
      } else if (this.invoiceyeType === 'yuejie') {
        Webtrends1.multiTrack('211206_DZFP_JHY_YJFP_DJKP')
      } else if (this.invoiceyeType === 'other') {
        Webtrends1.multiTrack('211206_DZFP_JHY_QTFP_DJKP')
      }
      sessionStorage['set' + 'Item']('invoiceDetail', JSON.stringify(item))
      this.jumpPage('./../invoice')
    },
    // ----------------- 查询数据 --------------------------
    queryInvoicelt() {
      const params = {
        token: getStorageToken(),
        type:
          this.invoiceyeType === 'yuejie'
            ? CONFIG_CODE.INVOICE_CODE.YUEJIE
            : this.invoiceyeType === 'chongzhi'
            ? CONFIG_CODE.INVOICE_CODE.CHONGZHI
            : CONFIG_CODE.INVOICE_CODE.OTHER, // 1:月结发票2:充值发票3:开票历史 4:其他发票 多个逗号分割
        transactionid: new Date().getTime()
      }
      this.$loading.show()
      this.isLoading = true
      queryInvoicelt(params)
        .then((res) => {
          console.log('queryInvoicelt', res)
          if (res.result === CONFIG_CODE.SUCCESS_CODE) {
            res.common = res.encrypt
            this.list = res.list
            this.$store.commit('UPDATE_COMMONINFORMATION', res.common)
          }
          if (res.mobile) {
            Webtrends1.setUserId(res.mobile, res.jtEncMisdn)
          }
        })
        .catch((err) => {
          toastErr(err)
        })
        .finally(() => {
          this.$loading.hide()
          this.isLoading = false
        })
    },

    // ----------------- 其他处理 --------------------------
    getPagePrarams() {
      if (this.invoiceyeType === 'chongzhi') {
        this.pagePrarams = {
          timeKey: '充值时间',
          typeKey: '发票类型',
          typeVal: '充值发票',
          statusKey: '发票状态',
          statusVal: '未开票'
        }
        this.title = '12个自然月内仅在中国移动北京APP充值时选开的充值发票'
      } else if (this.invoiceyeType === 'other') {
        this.pagePrarams = {
          timeKey: '发票生成时间',
          typeKey: '发票类型',
          statusKey: '发票状态',
          statusVal: '未开票'
        }
        this.title = ''
      }
    },
    dateFormat: dateFormat
  }
}
</script>

<style lang="scss" scoped>
@import './common/css/invoiceye-list.scss';
.list {
  padding: 0 24px;
}
</style>
