<template>
  <div class="list">
    <template>
      <van-tabs v-model="active" class="tab" line-width="140px" color="#999">
        <van-tab :title="`未开发票(${notInvoiceNum})`">
          <div v-if="notInvoiceNum > 0" class="list-list">
            <div
              v-for="item in notInvoiceList"
              :key="item.invc_num_seq"
              class="list-list__item"
            >
              <div class="left">
                <p class="text-item">
                  {{ pagePrarams.timeKey }}：
                  <span>
                    {{ dateFormat(item.create_time, 'yyyy.MM.dd') }}
                  </span>
                </p>
                <p class="text-item">
                  {{ pagePrarams.typeKey }}：
                  <span>{{
                    String(item.invc_type) | otherTypeDictionary
                  }}</span>
                </p>
                <p class="text-item">
                  {{ pagePrarams.statusKey }}：
                  <span>{{ pagePrarams.statusVal }}</span>
                </p>
              </div>
              <div class="right">
                <p>
                  <a>{{ item.invc_fee }}</a>
                  元
                </p>
                <div class="btn" @click.stop="toInvoice(item)">点击开票</div>
              </div>
            </div>
          </div>
          <div v-else class="list - nolist">
            <div class="list-nolist__content">
              <img src="../assets/base/no-invoice.png" />
              <p>抱歉，您当前尚无可查看的电子发票</p>
            </div>
          </div>
        </van-tab>

        <van-tab :title="`已开发票(${invoicedNum})`">
          <div v-if="invoicedNum > 0" class="list-list">
            <div
              v-for="item in invoicedList"
              :key="item.invc_num_seq"
              class="list-list__item"
            >
              <div class="left">
                <p class="text-item">
                  {{ pagePrarams.timeInvoiceye }}：
                  <span>
                    {{ dateFormat(item.sk_create_time, 'yyyy.MM.dd') }}
                  </span>
                </p>
                <p class="text-item">
                  {{ pagePrarams.typeKey }}：
                  <span>{{
                    String(item.invc_type) | otherTypeDictionary
                  }}</span>
                </p>
                <p class="text-item">
                  {{ pagePrarams.statusKey }}：
                  <span>{{ pagePrarams.statusOpen }}</span>
                </p>
              </div>
              <div class="right">
                <p>
                  <a>{{ item.invc_fee }}</a>
                  元
                </p>
                <div class="btn" @click.stop="toHistoryDetail(item)">
                  查看发票
                </div>
              </div>
            </div>
          </div>
          <div v-else class="list - nolist">
            <div class="list-nolist__content">
              <img src="../assets/base/no-invoice.png" />
              <p>抱歉，您当前尚无可查看的电子发票</p>
            </div>
          </div>
        </van-tab>
      </van-tabs>
    </template>
  </div>
</template>

<script>
import { invoiceltByAcctid } from '@/projects/invoiceye-st/api/api'
import { dateFormat, toastErr } from '@/projects/invoiceye-st/utils/utils'
// import Webtrends1 from '@/utils/webtrends'
import CONFIG_CODE from '@/projects/invoiceye-st/config/index.js'
import invoiceyeList from './mixin/invoiceye-list'
export default {
  mixins: [invoiceyeList],
  data() {
    return {
      acct_id: '', // 查询代充发票列表数据携带的参数
      active: 0, // tap栏展示 1表示未开票，2表示已开票
      notInvoiceList: [], // 未开发票列表
      invoicedList: [], // 已开发票列表
      notInvoiceNum: 0, // 未开发票数量
      invoicedNum: 0 // 已开发票数量
    }
  },
  computed: {},
  created() {
    this.invoiceyeType = this.$route.params.type
    this.acct_id = this.$route.params.acct_id
    //  查询代充发票数据列表
    this.invoiceltByAcctid()
  },
  methods: {
    // --------------- 操作 点击开票 ----------------------
    toInvoice(item) {
      // Webtrends1.multiTrack('211206_DZFP_JHY_QTFP_DJKP')
      // 将单个发票信息保存到sessionStorage，以json数据
      sessionStorage['set' + 'Item']('invoiceDetail', JSON.stringify(item))
      // 跳转到代充发票详细页面
      this.jumpPage('./../../replace-invoice')
    },
    // ----------------- 点击查看发票-----------------------
    toHistoryDetail(item) {
      // 将单个发票信息保存到sessionStorage，以json数据
      sessionStorage['set' + 'Item']('invoiceDetail', JSON.stringify(item))
      // 跳转到查看已开代充发票详细页面
      this.jumpPage('./../../replace-history')
    },

    // ----------------- 查询代充发票数据列表 --------------------------
    invoiceltByAcctid() {
      // 请求携带的参数
      const params = {
        acct_id: this.acct_id
      }
      this.$loading.show()
      this.isLoading = true
      // 发送请求
      invoiceltByAcctid(params)
        .then((res) => {
          // 请求数据成功
          if (res.result === CONFIG_CODE.SUCCESS_CODE) {
            // 将返回的数据赋值给data里的列表数组  d0内为未开发票列表 d1未已开发票列表
            // 未开票数据列表
            this.notInvoiceList = res.data.d0
            // 未开发票数量
            this.notInvoiceNum = this.notInvoiceList.length
            // 已开票数据列表
            this.invoicedList = res.data.d1
            // 已开发票数量
            this.invoicedNum = this.invoicedList.length
          }
        })
        .catch((err) => {
          toastErr(err)
        })
        .finally(() => {
          this.$loading.hide()
          this.isLoading = false
        })
    },
    dateFormat: dateFormat
  }
}
</script>

<style lang="scss" scoped>
@import './common/css/invoiceye-list.scss';
</style>
