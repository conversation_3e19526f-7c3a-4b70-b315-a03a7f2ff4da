<template>
  <div class="rise">
    <div v-if="emptyShow && !isLoading" class="rise-nolist">
      <div class="rise-nolist__content">
        <img src="../assets/rise/icon-empty.png" />
        <p>您还未设置发票抬头</p>
        <div class="btn" @click.stop="addRise('ljsz')">立即设置</div>
      </div>
    </div>
    <template v-if="!emptyShow">
      <div class="rise-list">
        <van-swipe-cell
          v-for="item in riseList"
          :key="item.id"
          :data-item="item"
          :before-close="beforeClose"
        >
          <div class="rise-list__item" @click.stop="toEdite(item)">
            <div class="title">
              <span
                class="title__text"
                :class="{ 'title__text--default': !item.isdefault }"
                >{{ item.title }}</span
              >
              <span v-if="!item.isdefault" class="icon">默认</span>
            </div>
            <div v-if="item.nsrsbh" class="ask">
              {{ item.nsrsbh }}
            </div>
          </div>
          <template #right>
            <van-button square type="danger" text="删除" />
          </template>
        </van-swipe-cell>
      </div>
      <div class="rise-btn">
        <a @click="addRise('tj')">添加发票抬头</a>
      </div>
    </template>

    <password
      v-if="PDShow"
      @closePasswordSheet="closePasswordSheet"
      @checkServicePwdSuccess="checkServicePwdSuccess"
    />
  </div>
</template>

<script>
import password from '../components/password'
import { mapState } from 'vuex'
import {
  queryInvoiceTitle,
  delInvoiceTitle
} from '@/projects/invoiceye-st/api/api'
import { toastErr, getStorageToken } from '@/projects/invoiceye-st/utils/utils'
import Webtrends1 from '@/utils/webtrends'
import CONFIG_CODE from '@/projects/invoiceye-st/config/index.js'

export default {
  components: {
    password
  },
  data() {
    return {
      riseList: [],
      PDShow: false,
      // 抬头上限
      MAX_LENGTH: 10,
      isLoading: false // 正在加载
    }
  },
  computed: {
    ...mapState({
      PDStatus: (state) => state.PDStatus
    }),
    emptyShow() {
      if (this.riseList.length > 0) {
        return false
      } else {
        return true
      }
    }
  },
  created() {
    this.queryInvoiceTitle()
  },
  methods: {
    // 添加常用发票抬头
    addRise(type) {
      if (type === 'ljsz') {
        Webtrends1.multiTrack('211206_DZFP_JHY_CYFPTT_LJSZ')
      } else if (type === 'tj') {
        Webtrends1.multiTrack('211206_DZFP_JHY_CYFPTT_TJFPTT')
      }

      // 本次未输入过客服密码，要先输入
      if (!this.PDStatus) {
        this.PDShow = true
        return
      }
      if (this.riseList.length >= this.MAX_LENGTH) {
        this.$toast('您的常用抬头已添加达到上限~')
        return
      }
      this.$router.push({
        path: '/rise/detail/add'
      })
    },
    // 关闭服务密码验证
    closePasswordSheet() {
      this.PDShow = false
    },
    // 检验服务验证
    checkServicePwdSuccess() {
      this.PDShow = false
      this.addRise()
    },
    // 删除二次确认
    beforeClose({ position, instance }) {
      const id = instance.$attrs['data-item'].id
      this.$dialog
        .confirm({
          getContainer: '.rise',
          messageAlign: 'center',
          message: '您确定要删除此发票抬头么？'
        })
        .then(() => {
          this.delInvoiceTitle(id)
          instance.close()
        })
        .catch(() => {
          instance.close()
        })
    },
    // 编辑抬头
    toEdite(item) {
      sessionStorage['set' + 'Item']('editRise', JSON.stringify(item))
      this.jumpPage('/rise/detail/edit')
    },
    // 查询抬头数据
    queryInvoiceTitle() {
      this.$loading.show()
      this.isLoading = true
      const params = {
        token: getStorageToken()
      }
      queryInvoiceTitle(params)
        .then((res) => {
          // console.log(res)
          if (String(res.result) === CONFIG_CODE.SUCCESS_CODE) {
            this.riseList = res.list
          }
        })
        .catch((err) => {
          toastErr(err)
        })
        .finally(() => {
          this.$loading.hide()
          this.isLoading = false
        })
    },

    // 删除抬头
    delInvoiceTitle(id) {
      this.$loading.show()
      const params = {
        token: getStorageToken(),
        id
      }
      this.$loading.show()
      delInvoiceTitle(params)
        .then((res) => {
          // console.log(res)
          this.queryInvoiceTitle()
        })
        .catch((err) => {
          toastErr(err)
        })
        .finally(() => {
          this.$loading.hide()
        })
    }
  }
}
</script>

<style lang="scss" scoped>
@import '@/projects/invoiceye-st/style/mixin.scss';
.rise {
  min-height: 100vh;
  background: #f6f6f6;
  &-nolist {
    &__content {
      padding-top: 100px;
      img {
        width: 518px;
        height: 286px;
        margin: 0 auto 60px;
      }
      p {
        color: #000;
        font-size: 28px;
        white-space: nowrap;
      }
      .btn {
        width: 360px;
        height: 74px;
        line-height: 74px;
        text-align: center;
        background: #ffffff;
        border: 2px solid #bababa;
        border-radius: 8px;
        color: #000000;
        font-size: 28px;
        margin: 40px auto 0;
      }
    }
  }
  &-list {
    height: 100%;
    overflow-y: auto;
    padding-bottom: 100px;

    &__item {
      background: url(../assets/base/icon-right.png) no-repeat 97% center;
      background-size: 24px 24px;
      height: 100%;
      text-align: left;
      padding: 24px 0 24px 32px;
      display: flex;
      align-items: center;
      justify-content: flex-start;
      flex-wrap: wrap;
      .title {
        color: #040404;
        font-size: 28px;
        font-weight: bold;
        width: 100%;
        display: flex;
        align-items: center;
        line-height: 40px;
        .icon {
          display: inline-block;
          width: 100px;
          // height: 40px;
          text-align: center;
          line-height: 40px;
          border: 1px solid #fd6934;
          border-radius: 8px;
          color: #fd6934;
          font-size: 24px;
          margin-left: 20px;
        }
        &__text {
          display: inline-block;
          max-width: 90%;
          @include ellipsis(2);
        }
        &__text--default {
          max-width: 70%;
        }
      }
      .ask {
        color: #949494;
        font-size: 28px;
        width: 100%;
      }
    }
  }
  &-btn {
    width: 100%;
    height: 100px;
    background: #fff;
    position: fixed;
    left: 0;
    bottom: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    a {
      display: block;
      width: 700px;
      height: 60px;
      background: #353333;
      border-radius: 8px;
      color: #ffffff;
      font-size: 28px;
      text-align: center;
      line-height: 60px;
    }
  }
  /deep/ {
    .van-dialog {
      .van-dialog__content--isolated {
        padding-top: 220px !important;
        background: url(../assets/rise/icon-error.png) no-repeat center 60px;
        background-size: 100px 100px;
        .van-dialog__message {
          padding-top: 0;
          padding-bottom: 40px;
        }
      }
      .van-hairline--top {
        &::after {
          border-color: #999;
        }
      }
      .van-hairline--left::after {
        border-color: #999;
      }
      .van-dialog__cancel,
      .van-dialog__confirm {
        height: 140px;
        color: #000;
      }
      .van-dialog__confirm {
        color: #fd6934;
      }
    }
  }
}
</style>
