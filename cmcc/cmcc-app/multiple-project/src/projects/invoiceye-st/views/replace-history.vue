<template>
  <div class="detail">
    <div class="detail-title">发票状态</div>
    <div class="detail-content">
      <div class="detail-content__item">
        <div class="status">
          {{ invoiceItem.invc_sts | statusDictionary }}
        </div>
        <a class="seePDF" @click="toInvoicePDF">查看电子发票</a>
      </div>
    </div>
    <div class="detail-title">发票信息</div>
    <div class="detail-content">
      <div class="detail-content__item center-left">
        <template v-if="invoiceItem.v_type !== '1'">
          <div class="left">开具时间</div>
          <a class="right">
            {{
              invoiceItem.sk_create_time
                ? dateFormat(invoiceItem.sk_create_time, 'yyyy.MM.dd hh:mm:ss')
                : ''
            }}
          </a>
        </template>
        <template v-else>
          <div class="left">发票时间</div>
          <a class="right">
            {{
              invoiceItem.create_time
                ? dateFormat(invoiceItem.create_time, 'yyyy.MM.dd')
                : ''
            }}
          </a>
        </template>
      </div>
      <div class="detail-content__item center-left">
        <div class="left">发票金额</div>
        <a class="right">
          <i>{{ invoiceItem.invc_fee }}</i>
          元
        </a>
      </div>
    </div>
    <div class="detail-footer">
      <a class="detail-footer__btn" @click="reSend">重新发送</a>
    </div>
    <replace-email
      :show-email-sheet="showReplaceEmail"
      :invoice-list="selectedInvoiceList"
      @dialogHide="dialogHide"
    />
    <div v-if="showErrToast" class="history-detail-toast">
      <p class="history-detail-toast__title">开具失败</p>
      <p class="history-detail-toast__content">系统繁忙，请您稍后再试~</p>
    </div>
  </div>
</template>
<script>
import { dateFormat } from '@/projects/invoiceye-st/utils/utils'
import ReplaceEmail from './components/replace-email.vue'
// import Webtrends1 from '@/utils/webtrends'
import historyDetail from './mixin/history-detail'
export default {
  mixins: [historyDetail],
  components: {
    ReplaceEmail
  },
  data() {
    return {
      showReplaceEmail: false // 邮箱弹窗
    }
  },
  methods: {
    dateFormat: dateFormat,
    toInvoicePDF() {
      this.jumpPage('/replace-pdf')
    },

    // 重新发送
    reSend() {
      // Webtrends1.multiTrack('211206_DZFP_JHY_FPXQ_CXFS')
      this.selectedInvoiceList[0] = this.invoiceItem
      // 显示弹窗
      this.showReplaceEmail = true
    },
    // 隐藏邮箱弹窗
    dialogHide() {
      this.showReplaceEmail = false
    }
  }
}
</script>

<style lang="scss" scoped>
@import './common/css/history-detail.scss';
</style>
