<template>
  <div class="rise-detail">
    <van-field name="radio" label="发票抬头">
      <template #input>
        <van-radio-group
          v-model="radio"
          direction="horizontal"
          @change="resetForm"
        >
          <van-radio name="2" checked-color="#FD6934">企业单位</van-radio>
          <van-radio name="1" checked-color="#FD6934"
            >个人/非企业单位</van-radio
          >
        </van-radio-group>
      </template>
    </van-field>
    <van-form ref="riseForm" validate-first>
      <template v-if="radio === '2'">
        <van-field
          v-model="form.title"
          name="公司名称"
          label="公司名称"
          placeholder="请您填写公司名称"
          maxlength="50"
          autocomplete="off"
          required
          :rules="[{ required: true }]"
        />
        <van-field
          v-model="form.nsrsbh"
          name="公司税号"
          label="公司税号"
          placeholder="请您填写纳税人识别号"
          maxlength="20"
          autocomplete="off"
          required
          :rules="[{ required: true }]"
        />
        <van-field
          v-model="form.dz"
          name="注册地址"
          label="注册地址"
          autocomplete="off"
          maxlength="60"
          placeholder="请您填写公司注册地址"
        />
        <van-field
          v-model="form.gddh"
          name="注册电话"
          label="注册电话"
          autocomplete="off"
          maxlength="20"
          placeholder="请您填写公司注册电话"
        />
        <van-field
          v-model="form.yhmc"
          name="开户银行"
          label="开户银行"
          autocomplete="off"
          maxlength="30"
          placeholder="请您填写公司开户银行"
        />
        <van-field
          v-model="form.yhzh"
          name="银行账户"
          label="银行账户"
          autocomplete="off"
          maxlength="30"
          placeholder="请您填写公司开户银行账号"
        />
      </template>
      <van-field
        v-else
        v-model="form.title"
        maxlength="50"
        name="抬头名称"
        label="抬头名称"
        autocomplete="off"
        required
        placeholder="请您填写您的名称"
        :rules="[{ required: true }]"
      />
      <div class="rise-detail-switch">
        <div>
          <p>设为默认抬头</p>
          <span>每次开票会默认填写该抬头信息</span>
        </div>
        <van-switch v-model="form.isdefault" active-color="#FD6934" />
      </div>
    </van-form>
    <div
      native-type="submit"
      class="rise-detail-btn"
      :class="`rise-detail-btn ${isLightBtn ? 'light' : ''}`"
      @click="saveInvoiceHeaderForm()"
    >
      <a>保存</a>
    </div>
    <password
      v-if="PDShow"
      @closePasswordSheet="closePasswordSheet"
      @checkServicePwdSuccess="checkServicePwdSuccess"
    />
  </div>
</template>

<script>
import { saveInvoiceTitle } from '@/projects/invoiceye-st/api/api'
import { regexpFun } from '@/projects/invoiceye-st/utils/regexp-validate'
import password from '../components/password'
import { mapState } from 'vuex'
import { toastErr, getStorageToken } from '@/projects/invoiceye-st/utils/utils'
import CONFIG_CODE from '@/projects/invoiceye-st/config/index.js'

export default {
  components: {
    password
  },
  data() {
    return {
      form: {
        nsrsbh: '',
        dz: '',
        gddh: '',
        yhmc: '',
        yhzh: '',
        title: '',
        isdefault: false
      },
      radio: CONFIG_CODE.RISE_TYPE.ENTERPRISE,
      fromPath: sessionStorage.getItem('fromPath') || '',
      PDShow: false
    }
  },
  computed: {
    isLightBtn() {
      if (this.radio === CONFIG_CODE.RISE_TYPE.ENTERPRISE) {
        if (this.form.title && this.form.nsrsbh) {
          return true
        }
      } else {
        if (this.form.title) {
          return true
        }
      }
      return false
    },
    ...mapState({
      PDStatus: (state) => state.PDStatus
    })
  },
  created() {
    let riseItem = sessionStorage.getItem('editRise')
    if (riseItem) {
      riseItem = JSON.parse(riseItem)
      this.form = riseItem
      if (riseItem.nsrsbh) {
        this.radio = CONFIG_CODE.RISE_TYPE.ENTERPRISE
      } else {
        this.radio = CONFIG_CODE.RISE_TYPE.PERSONAL
      }
      this.form.isdefault = !riseItem.isdefault
      sessionStorage.removeItem('editRise')
    }
  },
  methods: {
    saveSuccess() {
      if (this.fromPath) {
        sessionStorage['set' + 'Item']('riseSaveSuccess', CONFIG_CODE.ADD_RISE_SUCCESS)
        // this.jumpPage(this.fromPath, 'replace')
      }
      // else {
      this.$router.back()
      // }
    },
    resetForm() {
      this.$refs.riseForm.resetValidation()
    },
    // 关闭服务密码验证
    closePasswordSheet() {
      this.PDShow = false
    },
    // 检验服务验证
    checkServicePwdSuccess() {
      this.PDShow = false
      this.saveInvoiceHeaderForm()
    },
    // 验证抬头信息
    saveInvoiceHeaderForm() {
      this.$refs.riseForm
        .validate()
        .then((res) => {
          // 本次未输入过客服密码，要先输入
          if (!this.PDStatus) {
            this.PDShow = true
            return
          }
          if (!regexpFun.regFunLoudName(this.form.title)) {
            this.$toast('请正确填写名称')
            return false
          }
          if (this.radio === CONFIG_CODE.RISE_TYPE.ENTERPRISE) {
            if (!regexpFun.regFunTaxationNumber(this.form.nsrsbh)) {
              this.$toast('纳税人识别号错误，请核实后再提交')
              return false
            }
          }
          this.saveInvoiceTitle()
        })
        .catch(() => {})
    },
    //  保存抬头信息
    saveInvoiceTitle() {
      const form = this.form
      const params = {
        token: getStorageToken(),
        title: form.title
          .replace(/&/g, '&amp;')
          .replace(/</g, '&lt;')
          .replace(/>/g, '&gt;')
          .replace(/"/g, '&quot;'),
        isdefault: form.isdefault
          ? CONFIG_CODE.DEFAULT_CODE.IS_DEFAULT
          : CONFIG_CODE.DEFAULT_CODE.NOT_DEFAULT
      }
      // 企业
      if (this.radio === CONFIG_CODE.RISE_TYPE.ENTERPRISE) {
        params.nsrsbh = form.nsrsbh
        params.yhmc = form.yhmc
        params.yhzh = form.yhzh
        params.gddh = form.gddh
        params.dz = form.dz
      }
      if (form.id) {
        params.id = form.id
      }
      this.$loading.show()
      saveInvoiceTitle(params)
        .then((res) => {
          // console.log('saveInvoiceTitle', res)
          this.saveSuccess()
        })
        .catch((err) => {
          toastErr(err)
        })
        .finally(() => {
          this.$loading.hide()
        })
    }
  }
}
</script>

<style lang="scss" scoped>
.rise-detail {
  min-height: 100vh;
  background: #f6f6f6;
  /deep/ .van-field__label {
    margin-right: 0;
    color: #000;
    font-size: 28px;
  }
  /deep/.van-field__value {
    margin-right: -20px;
    font-size: 28px;
  }
  &-switch {
    background: #fff;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 20px 24px;
    text-align: left;
    p {
      font-size: 28px;
      font-weight: bold;
      display: block;
      margin-bottom: 20px;
    }
    span {
      display: block;
      font-size: 24px;
      color: #a3a3a3;
    }
  }
  &-btn {
    width: 100%;
    height: 100px;
    background: #fff;
    display: flex;
    align-items: center;
    justify-content: center;
    position: absolute;
    left: 0;
    bottom: 0;
    a {
      display: block;
      width: 700px;
      height: 60px;
      background: #c9c9c9;
      border-radius: 8px;
      color: #ffffff;
      font-size: 28px;
      text-align: center;
      line-height: 60px;
    }
    &.light {
      a {
        background: #353333;
        color: #fff;
      }
    }
  }
}
</style>
