<template>
  <div class="result">
    <div :class="`result-icon ${status}`"></div>
    <p class="result-title">
      {{ type | getResultIconTxt(status) }}
    </p>
    <div class="result-tips">
      {{ type | getResultTipsTxt(status) }}
    </div>
    <div class="result-btn">
      <a class="lastBtn" @click="toHistory">开票历史</a>
    </div>
  </div>
</template>

<script>
import Webtrends1 from '@/utils/webtrends'

export default {
  data() {
    return {
      status: this.$route.params.status,
      type: this.$route.params.type || 'send'
    }
  },
  computed: {},
  created() {
    // console.log(this.$route)
  },
  methods: {
    // 开票历史
    toHistory() {
      Webtrends1.multiTrack('P00000034042', '中国移动APP_电子发票_发票详情_申请退票重开_返回')
      this.$router.push({
        name: 'History'
      })
    }
  }
}
</script>

<style lang="scss" scoped>
@import './common/css/result.scss';
</style>
