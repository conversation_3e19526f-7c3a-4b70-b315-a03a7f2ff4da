.invoice {
    height: 100vh;
    background: #f6f6f6;
    overflow: hidden;
    &-form {
        height: calc(100vh - 100px);
        position: relative;
        overflow: auto;
        padding-bottom: 20px;
        &__title {
            width: 100%;
            height: 80px;
            text-align: left;
            line-height: 80px;
            color: #000000;
            font-size: 28px;
            text-indent: 24px;
            font-weight: bold;
        }
        &__content {
            // &.scroll {
            //     min-height: 288px;
            //     max-height: calc(100% - 600px);
            //     overflow-y: scroll;
            // }
            &__more {
                background: #fff;
                padding: 26px 0;
                border-bottom: 1px solid #ebedf0;
                .p1 {
                    color: #000000;
                    font-size: 28px;
                    img {
                        width: 20px;
                        height: 20px;
                        margin-left: 12px;
                    }
                }
                .p2 {
                    color: #888888;
                    font-size: 24px;
                    margin-top: 10px;
                }
            }
        }
        &__tip {
            color: #888888;
            font-size: 24px;
            text-align: left;
            margin: 20px auto 0;
            white-space: nowrap;
            padding: 0 24px;
            line-height: 30px;
        }
        .tost-icon {
            position: absolute;
            top: 215px;
            left: 190px;
            @include background(312px, 66px, 'invoice/tost-icon.png');
            font-size: 24px;
            font-weight: 400;
            text-align: center;
            color: #ffffff;
            line-height: 45px;
            z-index: 9;
        }
    }
    &-item {
        /deep/ .van-field__label {
            margin-right: 0;
            color: #000;
            font-size: 28px;
        }
        /deep/.van-field__value {
            margin-right: -20px;
            font-size: 28px;
        }
    }

    &-btn {
        width: 100%;
        height: 100px;
        background: #fff;
        position: absolute;
        left: 0;
        bottom: 0;
        display: flex;
        align-items: center;
        justify-content: center;
        a {
            display: block;
            width: 700px;
            height: 60px;
            text-align: center;
            line-height: 60px;
            background: #c9c9c9;
            color: #ffffff;
            font-size: 28px;
            border-radius: 8px;
        }
    }
    .black-color {
        a {
            background: #353333;
        }
    }
    .invoice-sheet {
        .van-action-sheet__header {
            font-size: 28px;
            text-align: center;
            color: #000000;
        }
        .rise-sheet {
            width: 100%;
            min-height: 500px;
            max-height: 600px;
            position: relative;
            overflow-y: auto;
            &__empty {
                color: #8d8d8d;
                font-size: 28px;
                text-align: center;
                margin-top: 40px;
            }
            &__list {
                padding-bottom: 100px;
                .rise-sheet-item {
                    width: 100%;
                    height: 128px;
                    background: #ffffff;
                    // line-height: 128px;
                    border-bottom: 1px solid #e0e0e0;
                    display: flex;
                    flex-direction: column;
                    align-items: left;
                    justify-content: center;
                    font-weight: 400;
                    text-align: left;
                    color: #000000;
                    font-size: 28px;
                    font-weight: bold;
                    padding-left: 32px;
                    &__text {
                        font-weight: 400;
                        text-align: left;
                        color: #000000;
                        font-size: 28px;
                        font-weight: bold;
                        line-height: 45px;
                        display: flex;
                        align-items: center;
                        justify-content: flex-start;
                        .title-text {
                            display: inline-block;
                            max-width: 95%;
                            @include ellipsis(2);
                        }
                        .title-text--default {
                            max-width: 75%;
                        }
                    }
                    .icon {
                        display: flex;
                        justify-content: center;
                        align-items: center;
                        color: #e91e1e;
                        font-size: 24px;
                        text-align: center;
                        width: 100px;
                        height: 40px;
                        border: 1px solid #ef3939;
                        border-radius: 8px;
                        line-height: 40px;
                        text-indent: 0;
                        margin-left: 40px;
                    }
                }
            }
            &-foot {
                width: 100%;
                height: 100px;
                position: fixed;
                left: 0;
                bottom: 0;
                background: #ffffff;
            }
            &__btn {
                width: 700px;
                height: 60px;
                line-height: 60px;
                position: absolute;
                left: 50%;
                bottom: 20px;
                transform: translateX(-50%);
                color: #ffffff;
                font-size: 28px;
                text-align: center;
                background: #353333;
                border-radius: 8px;
                letter-spacing: 2px;
            }
        }
        .confirm-sheeh {
            padding-bottom: 120px;
            &-item {
                width: 750px;
                height: 88px;
                background: #ffffff;
                border-bottom: 1px solid #e0e0e0;
                font-size: 28px;
                font-weight: 400;
                text-align: left;
                color: #8d8d8d;
                line-height: 40px;
            }
            .orange-color {
                color: #fd6934;
            }
            .red-color {
                color: #f02424;
            }
            .invoice-form__tip {
                margin: 20px auto 40px;
            }
        }
    }

    .field-right {
        display: flex;
        align-items: center;
        justify-content: space-between;
        width: 100%;
        .price {
            .light {
                color: #fd6934;
                font-weight: bold;
            }
        }
        .detail {
            color: #888888;
            font-size: 24px;
            background: url(../assets/base/icon-right.png) no-repeat center right;
            background-size: 20px 20px;
            padding-right: 30px;
        }
    }
    /deep/ {
        .van-cell {
            padding: 24px 28px;
            .van-field__label {
                font-size: 28px;
                font-weight: normal;
                // &.van-cell__title {
                //     color: #000000;
                // }
            }
            .van-field__control:disabled {
                color: #000;
                -webkit-text-fill-color: #000;
            }
        }
    }
    .invoice-rise-name {
        /deep/.van-field__control {
            width: 90%;
        }
    }
}
.invoice-toast {
    position: absolute;
    top: 32%;
    left: 0;
    right: 0;
    width: 558px;
    height: 200px;
    opacity: 0.7;
    background: rgba($color: #000000, $alpha: 0.7);
    border-radius: 20px;
    margin: 0 auto;
    z-index: 2055;
    opacity: 1;

    &__title {
        font-size: 40px;
        font-weight: 500;
        text-align: center;
        color: #ffffff;
        line-height: 56px;
        margin: 40px 0 20px;
    }
    &__content {
        font-size: 32px;
        font-weight: 400;
        text-align: center;
        color: #ffffff;
        line-height: 44px;
    }
}