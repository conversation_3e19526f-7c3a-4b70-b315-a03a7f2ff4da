.detail {
    height: 100vh;
    background: #f6f6f6;
    position: relative;
    &-title {
        width: 100%;
        height: 80px;
        text-align: left;
        line-height: 80px;
        padding: 0 24px;
        color: #000000;
        font-size: 28px;
        font-weight: bold;
    }
    &-content {
        width: 100%;
        &__item {
            padding: 0 24px;
            height: 80px;
            background: #fff;
            display: flex;
            align-items: center;
            justify-content: space-between;
            &.center-left {
                justify-content: flex-start;
            }
            .status {
                color: #fd6934;
                font-size: 28px;
            }
            .seePDF {
                color: #888888;
                font-size: 24px;
                white-space: nowrap;
                background: url(../assets/base/icon-right.png) no-repeat center right;
                background-size: 20px 20px;
                padding-right: 25px;
            }
            .left {
                color: #8a8a8a;
                font-size: 28px;
            }
            .right {
                color: #000000;
                font-size: 28px;
                margin-left: 60px;
                i {
                    font-style: normal;
                    color: #fd6934;
                }
            }
        }
    }
    &-footer {
        width: 100%;
        height: 100px;
        background: #fff;
        position: absolute;
        left: 0;
        bottom: 0;
        display: flex;
        align-items: center;
        justify-content: center;
        &__btn {
            width: 340px;
            height: 60px;
            background: #353333;
            border-radius: 8px;
            line-height: 60px;
            color: #ffffff;
            text-align: center;
            font-size: 28px;
            font-weight: 500;
            margin: 22px;
        }
    }
    .history-sheet {
        .van-action-sheet__header {
            font-size: 28px;
            text-align: center;
            color: #000000;
        }
        &-main {
            height: 300px;
            position: relative;
            width: 90%;
            font-size: 28px;
            font-weight: 400;
            text-align: left;
            color: #8d8d8d;
            line-height: 40px;
            margin: 0 auto;
            padding-top: 39px;
        }
        &__btn {
            width: 700px;
            height: 60px;
            line-height: 60px;
            position: absolute;
            left: 50%;
            bottom: 20px;
            transform: translateX(-50%);
            color: #ffffff;
            font-size: 28px;
            text-align: center;
            background: #353333;
            border-radius: 8px;
            letter-spacing: 2px;
        }
    }
}
.history-detail-toast {
    position: absolute;
    top: 32%;
    left: 0;
    right: 0;
    width: 558px;
    height: 200px;
    opacity: 0.7;
    background: rgba($color: #000000, $alpha: 0.7);
    border-radius: 20px;
    margin: 0 auto;
    z-index: 2055;
    opacity: 1;

    &__title {
        font-size: 40px;
        font-weight: 500;
        text-align: center;
        color: #ffffff;
        line-height: 56px;
        margin-top: 40px;
    }
    &__content {
        font-size: 32px;
        font-weight: 400;
        text-align: center;
        color: #ffffff;
        line-height: 44px;
    }
}