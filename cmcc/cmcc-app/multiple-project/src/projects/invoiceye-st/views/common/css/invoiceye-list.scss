.list {
    width: 100vw;
    min-height: 100vh;
    background: #f6f6f6;
    &-title {
        width: 100%;
        height: 80px;
        line-height: 80px;
        text-align: center;
        white-space: nowrap;
        color: #8a8a8a;
        font-size: 24px;
    }
    .other-title {
        height: 20px;
    }
    &-list {
        width: 700px;
        margin: 0 auto;
        &__item {
            width: 100%;
            background: #fff;
            border-radius: 10px;
            padding: 24px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 16px;
            .text-item {
                margin: 5px 0;
            }
            .left {
                color: #8a8a8a;
                font-size: 24px;
                line-height: 34px;
                text-align: left;
                span {
                    color: #000;
                }
            }
            .right {
                p {
                    font-size: 28px;
                    color: #000;
                    a {
                        color: #fd6934;
                        font-size: 32px;
                    }
                }
                .btn {
                    width: 134px;
                    height: 46px;
                    background: #ffffff;
                    border: 2px solid #7a7a7a;
                    border-radius: 8px;
                    color: #000;
                    font-size: 20px;
                    text-align: center;
                    line-height: 46px;
                    margin-top: 15px;
                }
            }
        }
    }
    &-nolist {
        &__content {
            padding-top: 100px;
            img {
                width: 234px;
                height: 250px;
                margin: 0 auto 60px;
            }
            p {
                color: #000;
                font-size: 28px;
                white-space: nowrap;
            }
        }
    }
}