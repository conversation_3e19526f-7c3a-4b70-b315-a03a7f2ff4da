import request from '@/utils/request'
import { getQueryString } from '@/utils/utils'

/* 可修改区域 */

// 清数据  http://st.bj.chinamobile.com:7080/actSignin/clean/all/actSignin/13717591272

//  展示电子发票
export function einvoicefl(params) {
  return request({
    url: '/app/einvoicefl',
    method: 'post',
    params: params
  })
}

//  展示代充电子发票
export function einvoiceflByAcctid(params) {
  return request({
    url: '/app/einvoiceflByAcctid',
    method: 'post',
    params: params
  })
}


/**
 * 电子发票列表
 * @param {*} params type 查询类型：1:月结发票2:充值发票3:开票历史 多个逗号分割
 * @returns
 */
export function queryInvoicelt(params) {
  return request({
    url: '/app/queryInvoicelt',
    method: 'post',
    params: params,
    encrypt: 2
  })
}

/**
 * 代充电子发票列表
 * @param {*} params d0：未开发票列表数据内容  d1：已开发票数据内容
 * @returns
 */
export function invoiceltByAcctid(params) {
  return request({
    url: '/app/invoiceltByAcctid',
    method: 'post',
    params: params
  })
}

/**
 * 2.4.9.退票重开接口
 * @param {*} params
 */
export function reInvoicecg(params) {
  return request({
    url: '/app/reInvoicecg',
    method: 'post',
    params: params
  })
}

/**
 * 2.4.10.查询是否需要校验服务密码接口
 * @param {*} params
 * @returns
 */
export function queryServicePwdStatus(params = {}) {
  params.channel = 'JT'
  return request({
    url: '/app/queryServicePwdStatus',
    method: 'post',
    params: params
  })
}

/**
 * 2.4.11.校验服务密码接口
 * @param {*} params
 * @returns
 */
export function checkServicePwd(params) {
  return request({
    url: '/app/checkServicePwd',
    method: 'post',
    params: params
  })
}

/**
 *2.4.12.查询发票企业信息和抬头信息接口
 * @param {*} params
 * @returns
 */
export function queryInvoiceTitle(params) {
  return request({
    url: '/app/queryInvoiceTitle',
    method: 'post',
    params: params
  })
}

/**
 *2.4.13.设置发票企业信息和抬头信息接口
 * @param {*} params
 * @returns
 */
export function saveInvoiceTitle(params) {
  return request({
    url: '/app/saveInvoiceTitle',
    method: 'post',
    params: params
  })
}

/**
 *2.4.14.删除发票企业信息和抬头信息接口
 * @param {*} params
 * @returns
 */
export function delInvoiceTitle(params) {
  return request({
    url: '/app/delInvoiceTitle',
    method: 'post',
    params: params
  })
}

/**
 *2.4.3.发票下载接口
 * @param {*} params
 * @returns
 */
export function invoicedl(params) {
  return request({
    url: '/app/invoicedl',
    method: 'post',
    params: params
  })
}

/**
 *2.4.26.代充发票下载接口
 * @param {*} params
 * @returns
 */
export function replaceInvoiced(params) {
  return request({
    url: '/app/invoicedlByAcctid',
    method: 'post',
    params: params
  })
}

/**
 *2.4.5.发票合并下载接口
 * @param {*} params
 * @returns
 */
export function invoiceBatchDL(url, params) {
  return request({
    url,
    method: 'post',
    params: params
  })
}

/**
 *2.4.2.发票开具接口
 * @param {*} params
 * @returns
 */
export function invoicecg(params) {
  const keyword = getQueryString('keyword')
  if (keyword) {
    params.keyword = keyword
  }
  return request({
    url: '/app/invoicecg',
    method: 'post',
    params: params
  })
}

/**
 *2.4.25.代充发票开具接口
 * @param {*} params
 * @returns
 */
export function invoicecgByAcctid(params) {
  return request({
    url: '/app/invoicecgByAcctid',
    method: 'post',
    params: params
  })
}

/**
 *2.4.6.校验用户类型接口 1（个人用户）；2（集团用户）
 * @param {*} params
 * @returns
 */
export function validateCustomerType(params) {
  return request({
    url: '/app/validateCustomerType',
    method: 'post',
    params: params
  })
}
/**
 *  查插码号码
 * @param {*} params
 * @returns
 */
export function encByToken(params) {
  return request({
    url: '/app/encByToken',
    method: 'post',
    params: params
  })
}
/**
 *  2.4.20.发送短验接口
 * @param {*} params
 * @returns
 */
export function sendInvoiceCode(params) {
  return request({
    url: '/app/sendInvoiceCode',
    method: 'post',
    params: params
  })
}
/**
 *  2.4.21.校验短验接口
 * @param {*} params
 * @returns
 */
export function checkInvoiceCode(params) {
  return request({
    url: '/app/checkInvoiceCode',
    method: 'post',
    params: params
  })
}
/**
 *  获取iframe链接接口
 * @param {*} params
 * @returns
 */
export function operate_unifyH5(params) {
  return request({
    url: '/app/operate_unifyH5',
    method: 'post',
    params: params
  })
}
