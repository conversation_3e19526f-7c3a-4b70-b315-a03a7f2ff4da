// // 电子发票列表 type 查询类型：1:月结发票2:充值发票3:开票历史 多个逗号分割
// export const queryInvoicelt1 = {
//   result: '0',
//   list: [
//     {
//       acct_id: '',
//       bill_cycle: '',
//       busi_type: '9999',
//       busi_type_name: '转讫发票生成',
//       check_nbr: '0',
//       create_time: '2021-09-01',
//       cust_name: 0,
//       file_path: '',
//       flag: '1',
//       invc_code: '',
//       invc_fee: '45.70',
//       invc_item_list: [
//         {
//           item_amount: '4570',
//           item_id: '239',
//           item_name: '套餐固定费',
//           item_seq: '1',
//           quantity: '1',
//           unit: '张',
//           unit_price: '4570'
//         }
//       ],
//       invc_num: '',
//       invc_num_seq: '3986275310',
//       invc_sts: '3',
//       invc_type: '7',
//       invc_type_desc: '转讫发票',
//       op_id: '99990019',
//       print_count: '0',
//       receipt_type_name: '',
//       remark: '(转)18201473186 20210901--20210930 应付:45.70 实付:45.70 ',
//       sk_create_time: '2021-12-07 18:51:27',
//       tax_format: '1',
//       v_type: '1'
//     },
//     {
//       acct_id: '',
//       bill_cycle: '',
//       busi_type: '9999',
//       busi_type_name: '转讫发票生成',
//       check_nbr: '0',
//       create_time: '2021-10-01',
//       cust_name: '18201473186',
//       file_path: '',
//       flag: '1',
//       invc_code: '',
//       invc_fee: '45.70',
//       invc_item_list: [
//         {
//           item_amount: '4570',
//           item_id: '239',
//           item_name: '套餐固定费',
//           item_seq: '1',
//           quantity: '1',
//           unit: '张',
//           unit_price: '4570'
//         }
//       ],
//       invc_num: '',
//       invc_num_seq: '3986275311',
//       invc_sts: '3',
//       invc_type: '7',
//       invc_type_desc: '转讫发票',
//       op_id: '99990019',
//       print_count: '0',
//       receipt_type_name: '',
//       remark: '(转)18201473186 20210901--20210930 应付:45.70 实付:45.70 ',
//       sk_create_time: '2021-12-07 18:51:27',
//       tax_format: '1',
//       v_type: '1'
//     }
//   ],
//   common: {
//     // title: 'sddddddddddd',
//     // encMisdn: 'MTgyMDE0NzMxODY=',
//     // email_address: '<EMAIL>'
//     // nsrsbh: '911101086000426500'
//   },
//   mobile: '29811-16774-6181-8213',
//   transactionid: '1638932670823',
//   current_timestamp: 1638932671059
// }

// // 电子发票列表 type 查询类型：1:月结发票2:充值发票3:开票历史 多个逗号分割
// export const queryInvoicelt2 = {
//   result: '0',
//   list: [
//     {
//       acct_id: '',
//       bill_cycle: '',
//       busi_type: '9999',
//       busi_type_name: '转讫发票生成',
//       check_nbr: '0',
//       create_time: '2021-09-01',
//       cust_name: '18201473186',
//       file_path: '',
//       flag: '1',
//       invc_code: '',
//       invc_fee: '45.70',
//       invc_item_list: [
//         {
//           item_amount: '4570',
//           item_id: '239',
//           item_name: '套餐固定费',
//           item_seq: '1',
//           quantity: '1',
//           unit: '张',
//           unit_price: '4570'
//         }
//       ],
//       invc_num: '',
//       invc_num_seq: '3986275310',
//       invc_sts: '3',
//       invc_type: '5',
//       invc_type_desc: '转讫发票',
//       op_id: '99990019',
//       print_count: '0',
//       receipt_type_name: '',
//       remark: '(转)18201473186 20210901--20210930 应付:45.70 实付:45.70 ',
//       sk_create_time: '2021-12-07 18:51:27',
//       tax_format: '1',
//       v_type: '2'
//     }
//   ],
//   common: {
//     title: 'sddddddddddd',
//     encMisdn: 'MTgyMDE0NzMxODY=',
//     // nsrsbh: '911101086000426500',
//     email_address: '<EMAIL>'
//   },
//   mobile: '29811-16774-6181-8213',
//   transactionid: '1638932670823',
//   current_timestamp: 1638932671059
// }
// //  电子发票列表：1:月结发票2:充值发票3:开票历史 多个逗号分割
// export const queryInvoicelt3 = {
//   result: '0',
//   list: [
//     {
//       acct_id: '30100047197474',
//       bill_cycle: '202112',
//       busi_type: '5143',
//       busi_type_name: '手厅充值-预存发票',
//       check_nbr: '0',
//       create_time: '2021-12-02',
//       cust_name: '18201473186',
//       file_path: '/2021/12/07/18',
//       flag: '11',
//       invc_code: '011002100311',
//       invc_fee: '29.94',
//       invc_item_list: [
//         {
//           item_amount: '2994',
//           item_id: '2158',
//           item_name: '预存通话费',
//           item_seq: '1',
//           quantity: '1',
//           unit: '张',
//           unit_price: '2994'
//         }
//       ],
//       invc_num: '88808109',
//       invc_num_seq: '4016168072',
//       invc_sts: '3',
//       invc_type: '5',
//       invc_type_desc: '预存发票',
//       op_id: '99990001',
//       print_count: '0',
//       receipt_type_name: '预存',
//       remark: '18201473186 付费方式: ',
//       sk_create_time: '2021-12-07 18:51:27',
//       tax_format: '1',
//       v_type: '2'
//     },
//     {
//       acct_id: '30100047197474',
//       bill_cycle: '202112',
//       busi_type: '5143',
//       busi_type_name: '手厅充值-预存发票',
//       check_nbr: '0',
//       create_time: '2021-12-02',
//       cust_name: '18201473186',
//       file_path: '/2021/12/07/18',
//       flag: '11',
//       invc_code: '011002100311',
//       invc_fee: '29.94',
//       invc_item_list: [
//         {
//           item_amount: '2994',
//           item_id: '2158',
//           item_name: '预存通话费',
//           item_seq: '1',
//           quantity: '1',
//           unit: '张',
//           unit_price: '2994'
//         }
//       ],
//       invc_num: '88801109',
//       invc_num_seq: '4016168072',
//       invc_sts: '3',
//       invc_type: '5',
//       invc_type_desc: '预存发票',
//       op_id: '99990001',
//       print_count: '0',
//       receipt_type_name: '预存',
//       remark: '18201473186 付费方式: ',
//       sk_create_time: '2021-12-07 18:51:27',
//       tax_format: '1',
//       v_type: '2'
//     },
//     {
//       acct_id: '',
//       bill_cycle: '',
//       busi_type: '9999',
//       busi_type_name: '转讫发票生成',
//       check_nbr: '0',
//       create_time: '2021-10-01',
//       cust_name: '18201473186',
//       file_path: '',
//       flag: '11',
//       invc_code: '',
//       invc_fee: '59.00',
//       invc_item_list: [
//         {
//           item_amount: '5900',
//           item_id: '239',
//           item_name: '套餐固定费',
//           item_seq: '1',
//           quantity: '1',
//           unit: '张',
//           unit_price: '5900'
//         }
//       ],
//       invc_num: '88835200',
//       invc_num_seq: '4002780275',
//       invc_sts: '3',
//       invc_type: '1',
//       invc_type_desc: '转讫发票',
//       op_id: '99990019',
//       print_count: '0',
//       receipt_type_name: '',
//       remark: '(转)18201473186 20211001--20211031 应付:59.00 实付:59.00 ',
//       sk_create_time: '2021-12-07 18:51:27',
//       tax_format: '1',
//       v_type: '2'
//     },
//     {
//       acct_id: '',
//       bill_cycle: '',
//       busi_type: '9999',
//       busi_type_name: '转讫发票生成',
//       check_nbr: '0',
//       create_time: '2021-08-01',
//       cust_name: '18201473186',
//       file_path: '',
//       flag: '11',
//       invc_code: '',
//       invc_fee: '57.20',
//       invc_item_list: [
//         {
//           item_amount: '5720',
//           item_id: '239',
//           item_name: '套餐固定费',
//           item_seq: '1',
//           quantity: '1',
//           unit: '张',
//           unit_price: '5720'
//         }
//       ],
//       invc_num: '89612865',
//       invc_num_seq: '3964189492',
//       invc_sts: '3',
//       invc_type: '2',
//       invc_type_desc: '转讫发票',
//       op_id: '99990019',
//       print_count: '0',
//       receipt_type_name: '',
//       remark: '(转)18201473186 20210801--20210831 应付:57.20 实付:57.20 ',
//       sk_create_time: '2021-12-07 18:51:27',
//       tax_format: '1',
//       v_type: '2'
//     },
//     {
//       acct_id: '',
//       bill_cycle: '',
//       busi_type: '9999',
//       busi_type_name: '转讫发票生成',
//       check_nbr: '0',
//       create_time: '2021-10-01',
//       cust_name: '18201473186',
//       file_path: '',
//       flag: '11',
//       invc_code: '',
//       invc_fee: '59.00',
//       invc_item_list: [
//         {
//           item_amount: '5900',
//           item_id: '239',
//           item_name: '套餐固定费',
//           item_seq: '1',
//           quantity: '1',
//           unit: '张',
//           unit_price: '5900'
//         }
//       ],
//       invc_num: '88835100',
//       invc_num_seq: '4002780275',
//       invc_sts: '5',
//       invc_type: '1',
//       invc_type_desc: '转讫发票',
//       op_id: '99990019',
//       print_count: '0',
//       receipt_type_name: '',
//       remark: '(转)18201473186 20211001--20211031 应付:59.00 实付:59.00 ',
//       sk_create_time: '2021-12-07 18:51:27',
//       tax_format: '1',
//       v_type: '2'
//     },
//     {
//       acct_id: '',
//       bill_cycle: '',
//       busi_type: '9999',
//       busi_type_name: '转讫发票生成',
//       check_nbr: '0',
//       create_time: '2021-08-01',
//       cust_name: '18201473186',
//       file_path: '',
//       flag: '11',
//       invc_code: '',
//       invc_fee: '57.20',
//       invc_item_list: [
//         {
//           item_amount: '5720',
//           item_id: '239',
//           item_name: '套餐固定费',
//           item_seq: '1',
//           quantity: '1',
//           unit: '张',
//           unit_price: '5720'
//         }
//       ],
//       invc_num: '89612861',
//       invc_num_seq: '3964189492',
//       invc_sts: '5',
//       invc_type: '2',
//       invc_type_desc: '转讫发票',
//       op_id: '99990019',
//       print_count: '0',
//       receipt_type_name: '',
//       remark: '(转)18201473186 20210801--20210831 应付:57.20 实付:57.20 ',
//       sk_create_time: '2021-12-07 18:51:27',
//       tax_format: '1',
//       v_type: '2'
//     },
//     {
//       acct_id: '',
//       bill_cycle: '',
//       busi_type: '9999',
//       busi_type_name: '转讫发票生成',
//       check_nbr: '0',
//       create_time: '2021-07-01',
//       cust_name: '18201473186',
//       file_path: '',
//       flag: '11',
//       invc_code: '',
//       invc_fee: '28.80',
//       invc_item_list: [
//         {
//           item_amount: '2880',
//           item_id: '239',
//           item_name: '套餐固定费',
//           item_seq: '1',
//           quantity: '1',
//           unit: '张',
//           unit_price: '2880'
//         }
//       ],
//       invc_num: '42920435',
//       invc_num_seq: '3947423993',
//       invc_sts: '3',
//       invc_type: '8',
//       invc_type_desc: '转讫发票',
//       op_id: '99990019',
//       print_count: '0',
//       receipt_type_name: '',
//       remark: '(转)18201473186 20210701--20210731 应付:28.80 实付:28.80 ',
//       sk_create_time: '2021-12-07 18:51:27',
//       tax_format: '1',
//       v_type: '2'
//     },
//     {
//       acct_id: '',
//       bill_cycle: '',
//       busi_type: '9999',
//       busi_type_name: '转讫发票生成',
//       check_nbr: '0',
//       create_time: '2021-11-01',
//       cust_name: '18201473186',
//       file_path: '',
//       flag: '11',
//       invc_code: '',
//       invc_fee: '59.20',
//       invc_item_list: [
//         {
//           item_amount: '20',
//           item_id: '238',
//           item_name: '增值费',
//           item_seq: '1',
//           quantity: '1',
//           unit: '张',
//           unit_price: '20'
//         },
//         {
//           item_amount: '5900',
//           item_id: '239',
//           item_name: '套餐固定费',
//           item_seq: '2',
//           quantity: '1',
//           unit: '张',
//           unit_price: '5900'
//         }
//       ],
//       invc_num: '89849958',
//       invc_num_seq: '4016198341',
//       invc_sts: '3',
//       invc_type: '7',
//       invc_type_desc: '转讫发票',
//       op_id: '99990019',
//       print_count: '0',
//       receipt_type_name: '',
//       remark: '(转)18201473186 20211101--20211130 应付:59.20 实付:59.20 ',
//       sk_create_time: '2021-12-07 18:51:27',
//       tax_format: '1',
//       v_type: '1'
//     },
//     {
//       acct_id: '',
//       bill_cycle: '',
//       busi_type: '9999',
//       busi_type_name: '转讫发票生成',
//       check_nbr: '0',
//       create_time: '2021-05-01',
//       cust_name: '18201473186',
//       file_path: '',
//       flag: '11',
//       invc_code: '',
//       invc_fee: '18.00',
//       invc_item_list: [
//         {
//           item_amount: '1800',
//           item_id: '239',
//           item_name: '套餐固定费',
//           item_seq: '1',
//           quantity: '1',
//           unit: '张',
//           unit_price: '1800'
//         }
//       ],
//       invc_num: '43837240',
//       invc_num_seq: '3909334652',
//       invc_sts: '3',
//       invc_type: '7',
//       invc_type_desc: '转讫发票',
//       op_id: '99990019',
//       print_count: '0',
//       receipt_type_name: '',
//       remark: '(转)18201473186 20210501--20210531 应付:18.00 实付:18.00 ',
//       sk_create_time: '2021-12-07 18:51:27',
//       tax_format: '1',
//       v_type: '1'
//     }
//   ],
//   common: {
//     title: 'sddddddddddd',
//     // nsrsbh: '911101086000426500',
//     encMisdn: 'MTgyMDE0NzMxODY=',
//     email_address: '<EMAIL>'
//   },
//   mobile: '5235-45446-6175-8215',
//   transactionid: '1638932728368',
//   current_timestamp: 1638932728729
// }
// //  电子发票列表：1:月结发票2:充值发票3:开票历史 4.其他发票 多个逗号分割
// export const queryInvoicelt4 = {
//   result: '0',
//   list: [
//     {
//       acct_id: '',
//       bill_cycle: '',
//       busi_type: '9999',
//       busi_type_name: '转讫发票生成',
//       check_nbr: '0',
//       create_time: '2021-10-01',
//       cust_name: '18201473186',
//       file_path: '',
//       flag: '11',
//       invc_code: '',
//       invc_fee: '59.00',
//       invc_item_list: [
//         {
//           item_amount: '5900',
//           item_id: '239',
//           item_name: '套餐固定费',
//           item_seq: '1',
//           quantity: '1',
//           unit: '张',
//           unit_price: '5900'
//         }
//       ],
//       invc_num: '88835200',
//       invc_num_seq: '4002780275',
//       invc_sts: '3',
//       invc_type: '1',
//       invc_type_desc: '话费发票',
//       op_id: '99990019',
//       print_count: '0',
//       receipt_type_name: '',
//       remark: '(转)18201473186 20211001--20211031 应付:59.00 实付:59.00 ',
//       sk_create_time: '2021-12-07 18:51:27',
//       tax_format: '1',
//       v_type: '2'
//     },
//     {
//       acct_id: '',
//       bill_cycle: '',
//       busi_type: '9999',
//       busi_type_name: '转讫发票生成',
//       check_nbr: '0',
//       create_time: '2021-08-01',
//       cust_name: '18201473186',
//       file_path: '',
//       flag: '11',
//       invc_code: '',
//       invc_fee: '57.20',
//       invc_item_list: [
//         {
//           item_amount: '5720',
//           item_id: '239',
//           item_name: '套餐固定费',
//           item_seq: '1',
//           quantity: '1',
//           unit: '张',
//           unit_price: '5720'
//         }
//       ],
//       invc_num: '89612865',
//       invc_num_seq: '3964189492',
//       invc_sts: '3',
//       invc_type: '2',
//       invc_type_desc: '实时话费发票',
//       op_id: '99990019',
//       print_count: '0',
//       receipt_type_name: '',
//       remark: '(转)18201473186 20210801--20210831 应付:57.20 实付:57.20 ',
//       sk_create_time: '2021-12-07 18:51:27',
//       tax_format: '1',
//       v_type: '2'
//     },
//     {
//       acct_id: '',
//       bill_cycle: '',
//       busi_type: '9999',
//       busi_type_name: '转讫发票生成',
//       check_nbr: '0',
//       create_time: '2021-07-01',
//       cust_name: '18201473186',
//       file_path: '',
//       flag: '11',
//       invc_code: '',
//       invc_fee: '28.80',
//       invc_item_list: [
//         {
//           item_amount: '2880',
//           item_id: '239',
//           item_name: '套餐固定费',
//           item_seq: '1',
//           quantity: '1',
//           unit: '张',
//           unit_price: '2880'
//         }
//       ],
//       invc_num: '42920435',
//       invc_num_seq: '3947423993',
//       invc_sts: '3',
//       invc_type: '8',
//       invc_type_desc: '业务发票',
//       op_id: '99990019',
//       print_count: '0',
//       receipt_type_name: '',
//       remark: '(转)18201473186 20210701--20210731 应付:28.80 实付:28.80 ',
//       sk_create_time: '2021-12-07 18:51:27',
//       tax_format: '1',
//       v_type: '2'
//     }
//   ],
//   common: {
//     title: 'sddddddddddd',
//     encMisdn: 'MTgyMDE0NzMxODY=',
//     email_address: '<EMAIL>'
//   },
//   mobile: '5235-45446-6175-8215',
//   transactionid: '1638932728368',
//   current_timestamp: 1638932728729
// }
// //  电子发票列表 5.代充发票
// export const queryInvoicelt5 = {
//   result: '0',
//   data: {
//     d0: [
//       {
//         acct_id: '003',
//         bill_cycle: '',
//         busi_type: '9999',
//         busi_type_name: '转讫发票生成',
//         check_nbr: '0',
//         create_time: '2021-10-01',
//         cust_name: '我是客户名称',
//         file_path: '',
//         flag: '11',
//         invc_code: '',
//         invc_fee: '59.00',
//         invc_item_list: [
//           {
//             item_amount: '5900',
//             item_id: '239',
//             item_name: '套餐固定费',
//             item_seq: '1',
//             quantity: '1',
//             unit: '张',
//             unit_price: '5900'
//           }
//         ],
//         invc_num: '88835200',
//         invc_num_seq: '4002780275',
//         invc_sts: '3',
//         invc_type: '1',
//         invc_type_desc: '话费发票',
//         op_id: '99990019',
//         print_count: '0',
//         receipt_type_name: '',
//         remark: '(转)18201473186 20211001--20211031 应付:59.00 实付:59.00 ',
//         sk_create_time: '2021-12-07 18:51:27',
//         tax_format: '1',
//         v_type: '2',
//         invc_status: '1'
//       },
//       {
//         acct_id: '003',
//         bill_cycle: '',
//         busi_type: '9999',
//         busi_type_name: '转讫发票生成',
//         check_nbr: '0',
//         create_time: '2021-08-01',
//         cust_name: '18201473186',
//         file_path: '',
//         flag: '11',
//         invc_code: '',
//         invc_fee: '57.20',
//         invc_item_list: [
//           {
//             item_amount: '5720',
//             item_id: '239',
//             item_name: '套餐固定费',
//             item_seq: '1',
//             quantity: '1',
//             unit: '张',
//             unit_price: '5720'
//           }
//         ],
//         invc_num: '89612865',
//         invc_num_seq: '3964189492',
//         invc_sts: '3',
//         invc_type: '1',
//         invc_type_desc: '话费发票',
//         op_id: '99990019',
//         print_count: '0',
//         receipt_type_name: '',
//         remark: '(转)18201473186 20210801--20210831 应付:57.20 实付:57.20 ',
//         sk_create_time: '2021-12-07 18:51:27',
//         tax_format: '1',
//         v_type: '2',
//         invc_status: '1'
//       }],
//     d1: [{
//       acct_id: '003',
//       bill_cycle: '',
//       busi_type: '9999',
//       busi_type_name: '转讫发票生成',
//       check_nbr: '0',
//       create_time: '2021-07-01',
//       cust_name: '18201473186',
//       file_path: '',
//       flag: '11',
//       invc_code: '',
//       invc_fee: '28.80',
//       invc_item_list: [
//         {
//           item_amount: '2880',
//           item_id: '239',
//           item_name: '套餐固定费',
//           item_seq: '1',
//           quantity: '1',
//           unit: '张',
//           unit_price: '2880'
//         }
//       ],
//       invc_num: '42920435',
//       invc_num_seq: '3947423993',
//       invc_sts: '3',
//       invc_type: '1',
//       invc_type_desc: '话费发票',
//       op_id: '99990019',
//       print_count: '0',
//       receipt_type_name: '',
//       remark: '(转)18201473186 20210701--20210731 应付:28.80 实付:28.80 ',
//       sk_create_time: '2021-12-07 18:51:27',
//       tax_format: '1',
//       v_type: '2',
//       invc_status: '2'
//     }]
//   },
// }

// // 查询是否需要校验服务密码接口
// export const queryServicePwdStatus = {
//   result: '0',
//   transactionid: '1638933868478',
//   current_timestamp: 1638933868417
// }
// // 2.4.11.校验服务密码接口
// export const checkServicePwd = {
//   result: '0',
//   transactionid: '1638933868478',
//   current_timestamp: 1638933868417,
//   is_right: '0',
//   msg: '成功'
// }

// // 抬头列表
// export const queryInvoiceTitle = {
//   result: '0',
//   list: [
//     {
//       id: 61,
//       isdefault: 1,
//       title: 'sddddddddddd'
//     },
//     {
//       id: 65,
//       isdefault: 1,
//       title: '21'
//     },
//     {
//       id: 66,
//       isdefault: 1,
//       title: '*********'
//     },
//     {
//       id: 63,
//       isdefault: 1,
//       title: '21111'
//     },
//     {
//       id: 62,
//       isdefault: 1,
//       title: '12222222'
//     },
//     {
//       nsrsbh: '911101086000426500',
//       email_address: '<EMAIL>',
//       id: 23,
//       isdefault: 0,
//       title: '齐旭红'
//     },
//     {
//       nsrsbh: '911101086000426500',
//       email_address: '<EMAIL>',
//       id: 24,
//       isdefault: 1,
//       title: '企业发票测试啥打的的的的的点点滴滴多多多多多多多多多多多多多多多多'
//     },
//     {
//       nsrsbh: '911101086000426500',
//       email_address: '<EMAIL>',
//       id: 25,
//       isdefault: 1,
//       title: '企业发票测试啥打的的的的的点点滴滴多多多多多多多多多多多多多多多多'
//     },
//     {
//       nsrsbh: '911101086000426500',
//       email_address: '<EMAIL>',
//       id: 26,
//       isdefault: 1,
//       title: '企业发票测试啥打的的的的的点点滴滴多多多多多多多多多多多多多多多多'
//     },
//     {
//       nsrsbh: '911101086000426500',
//       email_address: '<EMAIL>',
//       id: 27,
//       isdefault: 1,
//       title: '企业发票测试啥打的的的的的点点滴滴多多多多多多多多多多多多多多多多'
//     }
//   ],
//   current_timestamp: 1638934096627
// }
// // 2.4.6.校验用户类型接口
// export const validateCustomerType = {
//   result: 0,
//   type: '1',
//   msg: '成功!',
//   current_timestamp: 1638934885415
// }
// // 发票下载
// export const invoicedl = {
//   result: '0',
//   msg: '推送成功',
// }

// // 代充发票下载
// export const invoicedlByAcctid = {
//   result: '0',
//   msg: '推送成功'
// }

// // 发票合并下载
// export const invoiceBatchDL = {
//   result: '1',
//   msg: '推送失败',
//   misdn: '18201473186',
//   current_timestamp: 1638943409484
// }
// // 2.4.9.退票重开接口
// export const reInvoicecg = {
//   result: '0',
//   data: {
//     invc_sts: '5',
//     create_time: '2022-01-12',
//     invc_num_seq: '4044154838',
//     cust_name: '王华',
//     invc_fee: '9.80',
//     v_type: '1'
//   },
//   transactionid: '1641967163294',
//   current_timestamp: 1641967165310
// }

// // 2.4.9.保存抬头信息
// export const saveInvoiceTitle = {
//   result: '0',
//   misdn: '18201473186',
//   current_timestamp: 1638943409484
// }

// // 2.4.14.删除发票企业信息和抬头信息接口
// export const delInvoiceTitle = {
//   result: '0',
//   misdn: '18201473186',
//   current_timestamp: 1638943409484
// }

// // 2.4.14.开具发票接口
// export const invoicecg = {
//   result: '1',
//   misdn: '18201473186',
//   current_timestamp: 1638943409484
// }

// // 2.4.25.开具代充发票接口
// export const invoicecgByAcctid = {
//   result: '1',
// }

// // 2.4.14获取插码号码
// export const encByToken = {
//   result: '0',
//   misdn: '18201473186',
//   current_timestamp: 1638943409484,
//   mobile: '114-1111-21111'
// }
