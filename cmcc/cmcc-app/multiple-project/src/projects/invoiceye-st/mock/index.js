import Mock from 'mockjs'
import {
  queryInvoicelt1,
  queryInvoicelt2,
  queryInvoicelt3,
  queryInvoicelt4,
  queryInvoicelt5,
  queryServicePwdStatus,
  checkServicePwd,
  queryInvoiceTitle,
  validateCustomerType,
  invoicedl,
  invoicedlByAcctid,
  invoiceBatchDL,
  reInvoicecg,
  invoicecgByAcctid,
  saveInvoiceTitle,
  delInvoiceTitle,
  invoicecg,
  encByToken
} from './data'
import { getQueryString } from '@/utils/utils'

// 电子发票列表 type 查询类型：1:月结发票2:充值发票3:开票历史 4: 其他发票 5.代充发票多个逗号分割
Mock.mock(RegExp('/app/queryInvoicelt'), function (options) {
  const obj = JSON.parse(options.body) // 将json转化为对象类型
  const type = obj.type
  // console.log(options.body)
  if (type === '1') {
    return queryInvoicelt1
  } else if (type === '2') {
    return queryInvoicelt2
  } else if (type === '4') {
    return queryInvoicelt4
  } else if (type === '5') {
    return queryInvoicelt5
  } else {
    return queryInvoicelt3
  }
})

// 代充发票电子发票列表 
Mock.mock(RegExp('/app/invoiceltByAcctid'), function (options) {
  const obj = JSON.parse(options.body) // 将json转化为对象类型
  const acctId = obj.acct_id
  // console.log(options.body);
  return queryInvoicelt5
})

// 2.4.10.查询是否需要校验服务密码接口
Mock.mock(RegExp('/app/queryServicePwdStatus'), queryServicePwdStatus)

// 2.4.11.校验服务密码接口
if (getQueryString('isMock')) {
  Mock.mock(RegExp('/app/checkServicePwd'), checkServicePwd)
}

// 2.4.12.查询发票企业信息和抬头信息接口
Mock.mock(RegExp('/app/queryInvoiceTitle'), queryInvoiceTitle)

// 客户类型
Mock.mock(RegExp('/app/validateCustomerType'), validateCustomerType)

// 2.4.3.发票下载接口
Mock.mock(RegExp('/app/invoicedl'), invoicedl)

// 2.4.26.代充发票下载接口
Mock.mock(RegExp('/app/invoicedlByAcctid'), invoicedlByAcctid)

// 2.4.3.发票合并下载接口
Mock.mock(RegExp('/app/invoiceBatchDL'), invoiceBatchDL)

// 2.4.9.退票重开接口
Mock.mock(RegExp('/app/reInvoicecg'), reInvoicecg)

// 2.4.13.设置发票企业信息和抬头信息接口
Mock.mock(RegExp('/app/saveInvoiceTitle'), saveInvoiceTitle)

// 2.4.14.删除发票企业信息和抬头信息接口
Mock.mock(RegExp('/app/delInvoiceTitle'), delInvoiceTitle)

// 2.4.2.发票开具接口
Mock.mock(RegExp('/app/invoicecg'), invoicecg)

// 2.4.25.代充发票开具接口
Mock.mock(RegExp('/app/invoicecgByAcctid'), invoicecgByAcctid)

// 获取插码号码
Mock.mock(RegExp('/app/encByToken'), encByToken)
