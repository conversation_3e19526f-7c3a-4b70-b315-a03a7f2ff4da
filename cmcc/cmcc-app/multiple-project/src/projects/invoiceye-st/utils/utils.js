/**
 * 日期格式化
 * @param value
 * @param format
 * @returns {*}
 * index:parseTime(new Date(), 'yyyy-MM-dd')
 */
import { Toast } from 'vant'
export function dateFormat(value, format) {
  if (typeof value === 'string') {
    value = value.replace(/-/g, '/')
  }
  var t = new Date(value)
  var o = {
    'M+': t.getMonth() + 1, // month
    'd+': t.getDate(), // day
    'h+': t.getHours(), // hour
    'm+': t.getMinutes(), // minute
    's+': t.getSeconds(), // second
    'q+': Math.floor((t.getMonth() + 3) / 3), // quarter
    S: t.getMilliseconds() // millisecond
  }
  if (/(y+)/.test(format)) {
    format = format.replace(RegExp.$1, (t.getFullYear() + '').substr(4 - RegExp.$1.length))
  }
  for (var k in o) {
    if (new RegExp('(' + k + ')').test(format)) {
      format = format.replace(RegExp.$1, RegExp.$1.length === 1 ? o[k] : ('00' + o[k]).substr(('' + o[k]).length))
    }
  }
  return format
}
/**
 * 获取当前时间与周一到周日之间相差的天数
 * @param {Number} _weekTime 当前时间
 * @param {Number} _day 周一到周日的时间
 * @returns
 */
export function getDifference(_weekTime, _day) {
  if (_day < _weekTime) {
    // console.log(7 - (_weekTime - _day))
    return 7 - (_weekTime - _day)
  } else {
    return _day - _weekTime
  }
}
/**
 *echarts 自适应字体大小
 * @param {Number} value 原来大小
 * @returns
 */
export function changeFontSize(value) {
  const clientWidth = window.innerWidth || document.documentElement.clientWidth || document.body.clientWidth

  if (!clientWidth) return
  const fontSize = clientWidth / 750

  return value * fontSize
}

/**
 获取电话掩码
 * @param {Number} tel 电话号码明码
 * @returns
 */
export function getMisdnmask(tel) {
  if (tel) {
    tel = '' + tel
    const reg = /(\d{3})\d{4}(\d{4})/
    return tel.replace(reg, '$1****$2')
  }
}

/**
 *  异步请求错误
 *  @params {string}
 *  @params {string}
 *  */
export function toastErr(err) {
  if (err.result === '-99999') {
    Toast('未登录')
  } else {
    Toast('系统异常，请您稍后重新尝试~')
  }
}

/**
 *  获取token
 *  @params {string}
 *  @params {string}
 *  */
export function getStorageToken() {
  const token = sessionStorage.getItem('userToken')
  return token
}
