/**
 * 查询是否需要校验服务密码接口
 */
import { queryServicePwdStatus } from '@/projects/invoiceye-st/api/api.js'
import store from '../store'
export function queryPwdStatus() {
  queryServicePwdStatus({
    token: sessionStorage.getItem('userToken'),
    transactionid: new Date().getTime()
  })
    .then(res => {
      // 0:需要校验 1：不需要
      // console.log('queryServicePwdStatus', res)
      if (String(res.result) === '1') {
        store.commit('UPDATE_PASSWORDSTATUS', true)
      } else {
        store.commit('UPDATE_PASSWORDSTATUS', false)
      }
    })
    .then(err => {
      // console.log(err)
    })
}
