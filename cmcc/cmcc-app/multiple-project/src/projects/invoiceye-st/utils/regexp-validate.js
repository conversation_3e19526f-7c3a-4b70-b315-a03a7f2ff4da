/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 19/12/18.
 * 正则校验公共js
 * export {
 *   regexp, // 正则表达式
 *   regexpFun, // 正则表达式校验方法
 * }
 * 示例：
 *  <el-form-item
 *      :rules="{ validator: validateEmail }"
 *      label="联系人邮箱"
 *      prop="email"
 *  >
 *  import { regexp, regexpFun } from '@/utils/regexp-validate'
 *  ...
 *  data() {
 *      // demo1, 使用 regexp
 *      validateEmail: function(rule, value, callback) {
 *          const myRegexp = regexp.regEmail
 *          if ((!myRegexp.text(value)) && value !== '') {
 *              callback(new Error('邮箱不对时自定义提示语句'))
 *          } else {
 *              callback()
 *          }
 *      },
 *      // deme2, 使用 regexpFun
 *      validateEmail: function(rule, value, callback) {
 *          const myRegexpFun = regexpFun.regFunEmail
 *          if ((!myRegexpFun(value)) && value !== '') {
 *              callback(new Error('邮箱不对时自定义提示语句'))
 *          } else {
 *              callback()
 *          }
 *      }
 *  }
 *
 */

// 合法uri
const regUrL = /^(https?|ftp):\/\/([a-zA-Z0-9.-]+(:[a-zA-Z0-9.&%$-]+)*@)*((25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9][0-9]?)(\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])){3}|([a-zA-Z0-9-]+\.)*[a-zA-Z0-9-]+\.(com|edu|gov|int|mil|net|org|biz|arpa|info|name|pro|aero|coop|museum|[a-zA-Z]{2}))(:[0-9]+)*(\/($|[a-zA-Z0-9.,?'\\+&%$#=~_-]+))*$/
// 数字
const regNumber = /^[1-9]\d{0,}$/g
// 小写字母
const regLowercase = /^[a-z]+$/
// 大写字母
const regUppercase = /^[A-Z]+$/
// 大小写字母
const regAlphabets = /^[A-Za-z]+$/
// 邮箱
const regEmail = /[\w!#$%&'*+/=?^_`{|}~-]+(?:\.[\w!#$%&'*+/=?^_`{|}~-]+)*@(?:[\w](?:[\w-]*[\w])?\.)+[\w](?:[\w-]*[\w])?/
// 手机号 字符串有11个数字
const regMobile = /^([1]\d{10})$/g
// 电话号码
const regTel = /^(\+\d{2}-)?(0\d{2,3}-)\d{7,8}$/
// 邮编号码
const regPostcode = /^[0-9][0-9]{5}$/
// 大陆身份证号码
// 15, 地区6位/年份2位/月份/日期/3位数
// 18, 地区6位/年份4位/月份/日期/4位数或3位加后面是Xx
const regChinaIdNo = /(^\d{8}((0[1-9])|(1(0|1|2)))(0[1-9]|[1|2][0-9]|3[0|1])\d{3}$)|(^\d{6}(1|2)\d{3}((0[1-9])|(1(0|1|2)))(0[1-9]|[1|2][0-9]|3[0|1])\d{3}(\d|X|x)$)/
// 港澳身份证 第一位为H/M 后面跟8位数字 共9位
const regChinaHKAMIdNo = /(^[H|M][0-9]{8}$)/
// 抬头名称
// eslint-disable-next-line
var regLoudName = /^[A-Za-z0-9\u4E00-\u9FA5\s\(\)\（\）\&\-\!\！.《》_\'\'\"\"·‘’”“]+$/

// 正则校验工厂方法
function regFunFactory(reg) {
  return function (str) {
    return reg.test(str)
  }
}

function regFunUrl(str) {
  return regFunFactory(regUrL)(str)
}

function regFunNumber(str) {
  return regFunFactory(regNumber)(str)
}

function regFunLowerCase(str) {
  return regFunFactory(regLowercase)(str)
}

function regFunUpperCase(str) {
  return regFunFactory(regUppercase)(str)
}

function regFunAlphabets(str) {
  return regFunFactory(regAlphabets)(str)
}

function regFunEmail(str) {
  return regFunFactory(regEmail)(str)
}

function regFunMobile(str) {
  return regFunFactory(regMobile)(str)
}

function regFunTel(str) {
  return regFunFactory(regTel)(str)
}

function regFunPostcode(str) {
  return regFunFactory(regPostcode)(str)
}

function regFunChinaIdNo(str) {
  return regFunFactory(regChinaIdNo)(str)
}

function regFunChinaHKAMIdNo(str) {
  return regFunFactory(regChinaHKAMIdNo)(str)
}

function regFunLoudName(str) {
  return regFunFactory(regLoudName)(str)
}

/**
 * 税号
 * @param {String} str
 * @returns
 */
function regFunTaxationNumber(str) {
  if (str.length === 15 || str.length === 18 || str.length === 20) {
    if (str.length === 18) {
      var reg = /^((?!OISZ)([^a-z|O|S|I|Z$]))*$/
      if (reg.test(str)) {
        return true
      }
    }
    if (str.length === 15 || str.length === 20) {
      var reg2 = /^[0-9A-Z]+$/
      if (reg2.test(str)) {
        return true
      }
    }
    return false
  }
  return false
}

const regexp = {
  regUrL,
  regNumber,
  regLowercase,
  regUppercase,
  regAlphabets,
  regEmail,
  regMobile,
  regTel,
  regPostcode,
  regChinaIdNo,
  regChinaHKAMIdNo,
  regLoudName
}

const regexpFun = {
  regFunUrl,
  regFunNumber,
  regFunLowerCase,
  regFunUpperCase,
  regFunAlphabets,
  regFunEmail,
  regFunMobile,
  regFunTel,
  regFunPostcode,
  regFunChinaIdNo,
  regFunChinaHKAMIdNo,
  regFunTaxationNumber,
  regFunLoudName
}

export { regexp, regexpFun }
