import Vue from 'vue'
import Router from 'vue-router'

Vue.use(Router)

export default new Router({
  mode: 'hash',
  routes: [
    {
      path: '/',
      redirect: '/index'
    },
    {
      path: '/index',
      name: 'Index',
      component: resolve => require(['../views/index.vue'], resolve),
      meta: {
        title: '电子发票'
      }
    },
    {
      path: '/question',
      name: 'Question',
      component: resolve => require(['../views/question.vue'], resolve),
      meta: {
        title: '电子发票'
      }
    },
    {
      path: '/invoiceye-list/:type',
      name: 'InvoiceyeList',
      component: resolve => require(['../views/invoiceye-list.vue'], resolve),
      meta: {
        title: '电子发票'
      }
    },
    {
      path: '/history',
      name: 'History',
      component: resolve => require(['../views/history.vue'], resolve),
      meta: {
        title: '电子发票'
      }
    },
    {
      path: '/detail',
      name: 'HistoryDetail',
      component: resolve => require(['../views/history-detail.vue'], resolve),
      meta: {
        title: '电子发票'
      }
    },
    {
      path: '/pdf',
      name: 'HistoryDetailPDF',
      component: resolve => require(['../views/history-detail-pdf.vue'], resolve),
      meta: {
        title: '电子发票'
      }
    },
    {
      path: '/result/:type/:status',
      name: 'Result',
      component: resolve => require(['../views/result.vue'], resolve),
      meta: {
        title: '电子发票'
      }
    },
    {
      path: '/rise',
      name: 'Rise',
      component: resolve => require(['../views/rise.vue'], resolve),
      meta: {
        title: '电子发票'
      }
    },
    {
      path: '/invoice',
      name: 'Invoice',
      component: resolve => require(['../views/invoice.vue'], resolve),
      meta: {
        title: '电子发票'
      }
    },
    {
      path: '/invoice/detail',
      name: 'InvoiceDetail',
      component: resolve => require(['../views/invoice-detail.vue'], resolve),
      meta: {
        title: '电子发票'
      }
    },
    {
      path: '/rise/detail/:type',
      name: 'RiseDetail',
      component: resolve => require(['../views/rise-detail.vue'], resolve),
      meta: {
        title: '电子发票'
      }
    },
    {
      path: '/replace/:type/:acct_id',
      name: 'Replace',
      component: resolve => require(['../views/replace.vue'], resolve),
      meta: {
        title: '电子发票'
      }
    },
    {
      path: '/replace-invoice',
      name: 'ReplaceInvoice',
      component: resolve => require(['../views/replace-invoice.vue'], resolve),
      meta: {
        title: '电子发票'
      }
    },
    {
      path: '/replace-result/:type/:status',
      name: 'ReplaceResult',
      component: resolve => require(['../views/replace-result.vue'], resolve),
      meta: {
        title: '电子发票'
      }
    },
    {
      path: '/replace-history',
      name: 'ReplaceHistory',
      component: resolve => require(['../views/replace-history.vue'], resolve),
      meta: {
        title: '电子发票'
      }
    },
    {
      path: '/replace-pdf',
      name: 'ReplaceFDF',
      component: resolve => require(['../views/replace-pdf.vue'], resolve),
      meta: {
        title: '电子发票'
      }
    }
  ]
})
