/*
 * @Author: zhen<PERSON><PERSON><PERSON> <EMAIL>
 * @Date: 2023-11-30 16:28:28
 * @LastEditors: zhengwenling <EMAIL>
 * @LastEditTime: 2023-12-04 17:34:02
 * @FilePath: \multiple-project\src\projects\invoiceye-st\main.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import '@/utils/amfe-flexible'
// The Vue build version to load with the `import` command
// (runtime-only or standalone) has been set in webpack.base.conf with an alias.
import Vue from 'vue'
// By default we import all the components.
// Only reserve the components on demand and remove the rest.
// Style is always required.
import './utils/activity'
import Loading from '@/components/Loading'
import '@/styles/reset.css'
import './style/rewrite-vant.scss'
import '@/utils/vantUI'
import App from './App'
import router from './router'
import * as filters from './filters'
import store from './store'
// 项目公共模块
import actMixins from '@/mixins'
// 引用vant
import Vant from 'vant'
import 'vant/lib/index.css'

import { jumpPage } from './utils/index'
import '@/utils/config/link'
import Webtrends1 from '@/utils/webtrends'

// require('./mock/index')
Vue.prototype.jumpPage = jumpPage
Vue.mixin(actMixins)
Vue.use(Loading)
Vue.use(Vant)
Object.keys(filters).forEach(key => {
  Vue.filter(key, filters[key])
})

initVue()

function initVue() {
  /* eslint-disable no-new */
  new Vue({
    el: '#app',
    router,
    store,
    render: h => h(App)
  })
}
// window.onload = () => {
//   Webtrends1.setGeneralProps()
//   Webtrends1.gdpCommonTrack('pageview', 'H5PageShow')
// }
