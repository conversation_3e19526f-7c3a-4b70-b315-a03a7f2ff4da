import Vue from 'vue'
import Vuex from 'vuex'

Vue.use(Vuex)

export default new Vuex.Store({
  state: {
    // 本次登录，是否输入过客服密码
    PDStatus: false,
    // 电子发票企业信息和抬头
    commonInformation: {}
  },
  mutations: {
    UPDATE_PASSWORDSTATUS(state, val) {
      state.PDStatus = val
    },
    UPDATE_COMMONINFORMATION(state, val) {
      state.commonInformation = val
    }
  },
  actions: {
    updatePasswordStatus({ commit }, val) {
      commit('UPDATE_PASSWORDSTATUS', val)
    },
    upCommonInformation({ commit }, val) {
      commit('UPDATE_COMMONINFORMATION', val)
    }
  }
})
