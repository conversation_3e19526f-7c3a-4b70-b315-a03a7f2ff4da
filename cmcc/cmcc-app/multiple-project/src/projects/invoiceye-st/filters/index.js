/**
 *  类型枚举
 *  @params {string}
 *  */
export function typeDictionary(key) {
  let val = ''
  const typeDictionary = {
    1: '其他发票',
    2: '其他发票',
    8: '其他发票',
    5: '充值发票',
    7: '月结发票'
  }
  for (const prop in typeDictionary) {
    if (key === prop) {
      val = typeDictionary[prop]
      break
    }
  }
  return val
}

/**
 *  其他发票类型枚举
 *  @params {string}
 *  */
export function otherTypeDictionary(key) {
  let val = ''
  const typeDictionary = {
    1: '话费发票',
    2: '实时话费发票',
    8: '业务发票 ',
    5: '充值发票',
    7: '月结发票'
  }
  for (const prop in typeDictionary) {
    if (key === prop) {
      val = typeDictionary[prop]
      break
    }
  }
  return val
}
/**
 *  状态枚举
 *  @params {string}
 *  */
export function statusDictionary(key) {
  let val = '已开票'
  const statusDictionary = {
    5: '已重新开票'
  }
  for (const prop in statusDictionary) {
    if (key === prop) {
      val = statusDictionary[prop]
      break
    }
  }
  return val
}

/**
 *  提交页面图标文案
 *  @params {string}
 *  @params {string}
 *  */
export function getResultIconTxt(type, status) {
  let result = ''
  if (type === 'send') {
    result = status === 'success' ? '发送成功' : '发送失败'
  }

  if (type === 'submit') {
    result = status === 'success' ? '提交成功' : '提交失败'
  }

  return result
}

/**
 *  提交页面提示文案
 *  @params {string}
 *  @params {string}
 *  */
export function getResultTipsTxt(type, status) {
  let result = ''
  result = status === 'success' ? '您的发票已成功发送，请注意查收！' : '系统繁忙，请您稍后再试~'
  return result
}
