<template>
  <div id="app">
    <router-view />
  </div>
</template>

<script>
import Webtrends1 from '@/utils/webtrends'
export default {
  created() {
    // 在页面加载时读取sessionStorage里的状态信息
    if (sessionStorage.getItem('store')) {
      this.$store.replaceState(
        Object.assign(
          {},
          this.$store.state,
          JSON.parse(sessionStorage.getItem('store'))
        )
      )
    }
    // 在页面刷新时将vuex里的信息保存到sessionStorage里
    window.addEventListener('beforeunload', () => {
      sessionStorage['set' + 'Item']('store', JSON.stringify(this.$store.state))
    })
  },
  mounted() {
    Webtrends1.setGeneralProps()
  }
}
</script>

<style lang="scss">
#app {
  font-family: 'Avenir', Helvetica, Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  height: 100%;
  max-width: 1080px;
  margin: 0 auto;
}
</style>
