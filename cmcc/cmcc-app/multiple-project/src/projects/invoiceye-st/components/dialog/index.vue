<template>
  <div v-if="show" class="dialog" @click="hide">
    <div class="dialog-content" @click.stop>
      <div class="dialog-content__title">
        {{ title }}
        <span @click.stop="hide" />
      </div>
      <slot />
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      show: false,
      title: ''
    }
  },
  methods: {
    hide() {
      this.show = false
      this.$emit('closeCallBack')
    }
  }
}
</script>

<style lang="scss" scoped>
.dialog {
  position: fixed;
  left: 0;
  top: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  z-index: 100;
  &-content {
    position: absolute;
    left: 0;
    bottom: 0;
    background: #fff;
    border-radius: 20px 20px 0 0;
    &__title {
      width: 100%;
      height: 80px;
      line-height: 80px;
      text-align: center;
      color: #000000;
      font-size: 28px;
      border-bottom: 2px solid #e0e0e0;
      font-weight: bold;
      position: relative;
      span {
        width: 30px;
        height: 28px;
        background: url(../../assets/base/icon-close.png) no-repeat center
          center;
        background-size: 100% 100%;
        position: absolute;
        right: 30px;
        top: 50%;
        transform: translateY(-50%);
      }
    }
  }
}
</style>
