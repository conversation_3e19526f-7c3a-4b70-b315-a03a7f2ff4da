<template>
  <div class="module">
    <div class="module-title">
      <h2>{{ params.TITLE }}</h2>
      <h3 v-if="params.HIT_TITLE">
        {{ params.HIT_TITLE }}
      </h3>
    </div>
    <div class="module-list">
      <div
        v-for="(item, index) in params.LIST"
        :key="'module' + index"
        class="module-list__item"
        @click="jump(item)"
      >
        <img :src="item.ICON" />
        <p v-html="item.NAME"></p>
      </div>
    </div>
  </div>
</template>

<script>
import Webtrends1 from '@/utils/webtrends'

export default {
  props: {
    params: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {}
  },
  methods: {
    jump(obj) {
      if (obj.CODE) {
        Webtrends1.multiTrack(obj.CODE)
      }
      if (obj.TYPE === 'outer') {
        window.$APPABILITY.viewCall(obj.PATH, obj.NAME)
      } else {
        // console.log(obj.PATH)
        this.jumpPage(obj.PATH)
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.module {
  width: 700px;
  border-radius: 20px;
  margin: 0 auto 16px;
  background: #fff;
  padding-bottom: 32px;
  &-title {
    width: 100%;
    height: 76px;
    color: #000000;
    display: flex;
    align-items: center;
    justify-content: flex-start;
    padding-left: 20px;
    h2 {
      font-size: 28px;
      font-weight: bold;
    }
    h3 {
      font-size: 24px;
      color: #8a8a8a;
      margin-left: 20px;
    }
  }
  &-list {
    width: 120%;
    padding: 0 60px;
    display: flex;
    justify-content: flex-start;
    flex-wrap: wrap;
    margin: 0 auto;
    position: relative;
    left: -10%;
    &__item {
      // width: 96px;
      width: 33.33%;
      // height: 80px;
      color: #777777;
      font-size: 24px;
      text-align: center;
      white-space: nowrap;
      position: relative;
      line-height: 32px;
      img {
        width: 96px;
        height: 80px;
        display: block;
        margin: 0 auto;
      }
      &:nth-child(n + 4) {
        margin-top: 20px;
      }
      // p{
      //     position: absolute;
      //     left: 50%;
      //     top: 80px;
      //     transform: translateX(-50%);
      //     line-height: 32px;
      // }
    }
  }
}
</style>
