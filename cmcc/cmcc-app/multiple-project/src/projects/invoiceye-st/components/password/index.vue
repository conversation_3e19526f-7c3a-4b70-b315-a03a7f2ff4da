<template>
  <div class="password">
    <van-action-sheet
      v-model="PDShow"
      title="验证码"
      @click-overlay="closeCallBack"
      @close="closeCallBack"
    >
      <div class="password-filed">
        <div class="password-filed__label">短信验证码</div>
        <div class="password-filed__input">
          <van-field
            v-model="codeValue"
            maxlength="8"
            type="password"
            class=""
            id="codeValue"
          />
          <p class="error" v-if="isError">密码不正确，请重新输入</p>
        </div>
        <a class="password-filed__forget" @click="getCaptcha">{{
          sendCaptcha ? `${countTime}s` : '发送验证码'
        }}</a>
      </div>
      <div
        :class="`password-btn ${codeValue !== '' ? 'light' : ''}`"
        @click="checkServicePwd"
      >
        提交
      </div>
    </van-action-sheet>
  </div>
</template>

<script>
import { Field } from 'vant'
import {
  sendInvoiceCode,
  checkInvoiceCode
} from '@/projects/invoiceye-st/api/api'
// import Webtrends1 from '@/utils/webtrends'

export default {
  components: {
    [Field.name]: Field
  },
  props: {
    backType: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      PDShow: true,
      isError: false,
      codeValue: '', // 验证码
      countTime: '', // 倒计时时间
      codeInterval: '', // 短信计时器
      sendCaptcha: false // 正在发送验证码
    }
  },
  mounted() {
    document.getElementById('codeValue').focus()
  },
  methods: {
    closeCallBack() {
      this.$emit('closePasswordSheet')
    },
    toSetPassword() {
      // eslint-disable-next-line quote-props
      const jsonObject = { pageid: 'bjcmcc01057' }
      window.aspireweb.viewcall(JSON.stringify(jsonObject))
    },
    /* 获取验证码 */
    getCaptcha() {
      if (this.sendCaptcha) {
        return
      }
      this.clearInterval()
      this.timerDown()
      const params = {
        token: this.getStorgeToken,
        channel: 'JT',
        transactionid: new Date().getTime()
      }
      sendInvoiceCode(params)
        .then((res) => {
          let result = res.result
          result = String(result)
          if (result !== '0') {
            this.sendCaptcha = false
            this.clearInterval()
            if (result === '-523') {
              this.$toast('验证码获取太频繁了哦，请60秒后再试试吧！')
            } else {
              this.$toast('获取验证码失败，请稍后尝试！')
            }
          } else {
            this.sendCaptcha = true
          }
        })
        .catch((err) => {
          this.sendCaptcha = false
          const { result } = err
          if (result !== '-99999') {
            this.$toast('系统繁忙，请您稍后再试~')
          }
        })
    },
    // 倒计时
    timerDown() {
      this.countTime = 60
      this.sendCaptcha = true
      const _this = this
      this.codeInterval = setInterval(() => {
        _this.countTime--
        if (_this.countTime === 0) {
          _this.sendCaptcha = false
          _this.clearInterval()
        }
      }, 1000)
    },
    // 清除倒计时
    clearInterval() {
      window.clearInterval(this.codeInterval)
    },
    /* 验证码校验 */
    checkCode() {
      const reg = /^[0-9]*$/
      if (this.codeValue === '') {
        this.$toast('请输入验证码！')
      } else if (!reg.test(this.codeValue)) {
        this.$toast('请输入正确的验证码哦！')
        return false
      } else {
        return true
      }
    },
    // 验证码登录
    checkServicePwd() {
      const params = {
        token: this.getStorgeToken,
        transactionid: new Date().getTime(),
        channel: 'JT',
        pwd: this.codeValue
      }
      if (!this.checkCode()) {
        return
      }
      this.$loading.show()
      checkInvoiceCode(params)
        .then((res) => {
          this.$loading.hide()
          let { result, msg } = res
          result = String(result)
          if (result === '0') {
            this.$store.commit('UPDATE_PASSWORDSTATUS', true)
            this.$emit('checkServicePwdSuccess', this.backType)
            this.sendCaptcha = false
            this.clearInterval()
            this.codeValue = ''
            // 登录成功抽奖
          } else {
            this.$toast(msg || '请输入正确的验证码')
          }
        })
        .catch((err) => {
          this.$loading.hide()
          const { result } = err
          if (result !== '-99999') {
            this.$toast('系统繁忙，请您稍后再试~')
          }
        })
    }
  },
  beforeDestroy() {
    if (this.codeInterval) {
      this.clearInterval()
    }
  }
}
</script>

<style lang="scss" scoped>
.password {
  .van-action-sheet__header {
    font-size: 28px;
    text-align: center;
    color: #000000;
  }
  &-filed {
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 60px auto 220px;
    &__label {
      color: #000000;
      font-size: 28px;
    }
    &__input {
      width: 360px;
      // height: 60px;
      margin: 0 20px;
      font-size: 28px;
      // line-height: 60px;
      position: relative;
      border-radius: 10px;
      input {
        width: 100%;
        height: 100%;
      }
      .error {
        position: absolute;
        left: 0;
        top: 60px;
        color: #fd6934;
        font-size: 24px;
        text-align: left;
        line-height: 54px;
        white-space: nowrap;
      }
    }
    .password-filed__input {
      .van-cell {
        background: #ffffff;
        border: 1px solid #c5c5c5;
        border-radius: 5px;
        padding: 0 11px;
      }
    }
    &__forget {
      color: #fd6934;
      font-size: 24px;
      min-width: 120px;
    }
  }
  &-btn {
    width: 700px;
    height: 60px;
    text-align: center;
    line-height: 60px;
    background: #c9c9c9;
    border-radius: 8px;
    color: #ffffff;
    font-size: 28px;
    margin: 0 auto 20px;
    &.light {
      background: #353333;
      color: #fff;
    }
  }
}
</style>
