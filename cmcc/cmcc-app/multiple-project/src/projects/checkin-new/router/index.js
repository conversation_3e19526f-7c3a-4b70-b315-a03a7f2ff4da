/*
 * @Author: zhen<PERSON><PERSON><PERSON> zhengwen<PERSON>@aspirecn.com
 * @Date: 2023-03-24 19:08:31
 * @LastEditors: zhengwenling <EMAIL>
 * @LastEditTime: 2025-03-03 15:18:51
 * @FilePath: \multiple-project\src\projects\checkin-new\router\index.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import Vue from 'vue'
import Router from 'vue-router'
import indexCom from '../views/index'

Vue.use(Router)

export default new Router({
  mode: 'hash',
  routes: [
    {
      path: '/',
      redirect: '/index'
    },
    {
      path: '/index',
      name: 'Index',
      component: indexCom
    },
    {
      path: '/prize',
      name: 'Prize',
      component: resolve => require(['../views/prize'], resolve)
    },
    {
      path: '/calendar',
      name: 'Calendar',
      component: resolve => require(['../views/calendar'], resolve)
    },
    {
      path: '/update-family',
      name: 'updateFamily',
      component: resolve => require(['../views/update-family'], resolve)
    }
  ]
})
