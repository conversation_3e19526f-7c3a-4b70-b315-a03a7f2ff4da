@import "~@/projects/checkin-new/styles/mixin.scss";
.index {
  width: 100%;
  padding-top: 80px;
  min-height: 100vh;
  text-align: left;
  color: #fff;
  position: relative;
  overflow: hidden;
  @include box-text();
  z-index: 0;
  text-align: center;
  &__top {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 80px;
    ::v-deep {
      .van-notice-bar {
        padding: 0 4px;
      }
      .van-notice-bar__content {
        font-size: 26px;
      }
    }
  }
  &__banner {
    margin-top: 80px;
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    z-index: -1;
    width: 100%;
    height: 325px;
    background: url('~@/projects/checkin-new/assets/index/bg-area.png') no-repeat center center / contain;
  }
  &__top__banner {
    width: 700px;
    height: 120px;
    border-radius: 20px;
    display: block;
  }
  &-tips {
    position: relative;
    width: 302px;
    height: 64px;
    background-position: -100px -250px;
    // @include background(302px, 64px, "index/bg-tips.png");
    margin: 241px 0 20px 40px;
  }
  &-main {
    position: relative;
    width: 700px;
    padding: 0 25px 30px;
    background: linear-gradient(#ffffff, #ffffff 80%, #fedded);
    box-shadow: 0px 0px 10px 0px #febcbd;
    margin: auto;
    border-radius: 34px;
    z-index: 2;
    &::before {
      position: absolute;
      content: '';
      display: inline-block;
      width: 100%;
      height: 130px;
      top: 0;
      right: 0;
      z-index: -1;
      background-position: 0 -350px;
      // background: url('../assets/checkin/bg-right-top.png') no-repeat center center / contain;
    }
  }
  &__rule {
    position: absolute;
    top: 0;
    left: 0;
    width: 50%;
    height: 100%;
  }
  &__prize {
    position: absolute;
    top: 0;
    right: 0;
    width: 50%;
    height: 100%;
  }
  &__wish {
    position: absolute;
    top: 250px;
    right: 47px;
    display: flex;
    flex-direction: column;
    align-items: center;
    .image {
      width: 50px;
      height: 50px;
      @include background(50px, 50px, "index/wish.png");
    }
    .title {
      color: #ffffff;
      font-size: 24px;
    }
  }
  &__guide {
    position: absolute;
    top: 281px;
    left: 308px;
    height: 46px;
    background-image: linear-gradient(
        180deg,
        rgba(255, 255, 255, 0.86) 0%,
        #ffffff 100%
      ),
      linear-gradient(#ffffff, #ffffff);
    background-blend-mode: normal, normal;
    box-shadow: 0px 5px 13px 0px rgba(192, 6, 32, 0.28);
    border-radius: 16px 23px 23px 0px;
    font-size: 20px;
    font-weight: normal;
    font-stretch: normal;
    letter-spacing: 0px;
    color: #f14c47;
    line-height: 46px;
    padding: 0 15px;
  }
  &__sub {
    position: fixed;
    top: 630px;
    right: 10px;
    @include background(83px, 155px, "index/subscribe.png");
    z-index: 8;
  }
  &__dec {
    margin: 40px auto;
    @include background(467px, 27px, "index/bottom.png");
  }
  .bj-banner {
    margin: 12px auto;
    width: 726px;
    height: 146px;
    background: url('../assets/index/bj-banner.png') no-repeat center center;
    background-size: 100% 100%;
  }
}
.swiper {
  width: 700px;
  background-color: #ffffff;
  border-radius: 24px;
  margin: 0 auto;
  margin-bottom: 20px;
}
.wrap {
  width: 700px;
  background-color: #ffffff;
  border-radius: 24px;
  margin: 20px auto;
  padding: 20px 0;
  margin-bottom: 20px;
}
.banner-swipe {
  margin-bottom: 30px;
  .banner {
    width: 700px;
    height: 180px;
    border-radius: 24px;
    margin: 0 auto;
    background: #ffffff;
  }
}
.back-duanwu {
  position: fixed;
  top: 7vh;
  left: 0;
  width: 160px;
  height: 60px;
  background: url('../assets/index/back-duanwu.gif') no-repeat center center;
  background-size: 100% 100%;
  z-index: 20;
}

.new {
  z-index: 99;

  &-mian {
    @include background(634px, 810px, "index/bg-pop1.png");
    position: absolute;
    left: 50%;
    top: 45%;
    transform: translate(-50%, -50%);
    &__btn {
      @include background(355px, 108px, "index/btn-pop.png");
      position: absolute;
      bottom: 90px;
      left: 22%;
      // transform: translateX(-50%);
      animation: button 1s linear alternate infinite;
    }
    &__close {
      position: absolute;
      bottom: -60px;
      left: 50%;
      transform: translateX(-50%);
      @include background(79px, 79px, "index/X.png");
    }
  }
}
@keyframes button {
  0% {
    transform: scale(1);
  }
  100% {
    transform: scale(0.8);
  }
}
@keyframes roundRule {
  0% {
    transform: rotate(-15deg);
  }
  100% {
    transform: rotate(-15deg);
  }
  50% {
    transform: rotate(15deg);
  }
}
