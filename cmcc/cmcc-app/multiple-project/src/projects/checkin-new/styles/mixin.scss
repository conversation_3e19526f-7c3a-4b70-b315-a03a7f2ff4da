@mixin ellipsis($clamp: 1) {
    overflow: hidden;
    text-overflow: ellipsis;
    @if ($clamp==1) {
        white-space: nowrap;
    } @else {
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-line-clamp: $clamp;
        -webkit-box-orient: vertical;
    }
}

@mixin flex($x: flex-start, $y: flex-start, $direction: row, $wrap: nowrap) {
    display: -webkit-box;
    display: box;
    display: flex;
    align-items: $y;
    justify-content: $x;
    flex-direction: $direction;
    flex-wrap: $wrap;
}

@mixin bg($url, $sizeX: 100%, $sizeY: 100%) {
    background: url('~@/projects/checkin-new/assets/'+$url) no-repeat center center;
    background-size: $sizeX $sizeY;
    @if ($sizeY) {
        background-size: $sizeX $sizeY;
    } @else {
        background-size: $sizeX;
    }
}

@mixin box-text($font-size: 30px, $color: #666, $line-height: 40px, $text-align: left, $font-weight: 400) {
    font-size: $font-size;
    font-weight: $font-weight;
    font-stretch: normal;
    line-height: $line-height;
    letter-spacing: 0px;
    text-align: $text-align;
    color: $color;
}

@mixin rule-title-text {
    font-size: 26px;
    font-weight: bold;
    font-stretch: normal;
    line-height: 70px;
    letter-spacing: 0px;
    color: #ffffff;
}
@mixin background($width, $height, $url, $sizeX: 100%, $sizeY: 100%) {
    width: $width;
    height: $height;
    background: url('~@/projects/checkin-new/assets/'+$url) no-repeat center center;
    background-size: $sizeX $sizeY;
    @if ($sizeY) {
        background-size: $sizeX $sizeY;
    } @else {
        background-size: $sizeX;
    }
}

@mixin btn() {
    margin: 0 auto;
    text-align: center;
    width: 330px;
    height: 72px;
    background-image: linear-gradient(9deg, rgba(255, 73, 69, 1) 0%, #ff846d 100%), linear-gradient(#ff9962, #ff9962);
    background-blend-mode: normal, normal;
    box-shadow: 0px 4px 8px 0px rgba(255, 60, 60, 0.34);
    border-radius: 40px;
    font-size: 30px;
    font-weight: normal;
    font-stretch: normal;
    letter-spacing: 0px;
    color: #ffffff;
    line-height: 72px;

}
