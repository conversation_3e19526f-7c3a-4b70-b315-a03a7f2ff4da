// 1.1 获取签到信息接口
export const getSignIn = {
  result: 0,
  isBj: 1,
  encMisdn: '41013-42004-5947-32797',
  openId: 'oGdIs6J6K2W18m8uNsn902kWd6Xs',
  newUser: false,
  signCardNum: 2,
  signInList: [
    { signInDate: '2023-12-01', signInPrize: '' },
    // { signInDate: '2023-12-02', signInPrize: '50MB流量' },
    // { signInDate: '2023-12-03', signInPrize: '' },
    // { signInDate: '2023-12-04', signInPrize: '' },
    // { signInDate: '2023-12-05', signInPrize: '' },
    // { signInDate: '2023-12-06', signInPrize: '50MB流量' },
    // { signInDate: '2023-12-07', signInPrize: '' },
    // { signInDate: '2023-12-08', signInPrize: '' },
    // { signInDate: '2023-12-09', signInPrize: '' },
    // { signInDate: '2023-12-10', signInPrize: '50MB流量' },
    // { signInDate: '2023-12-11', signInPrize: '' },
    // { signInDate: '2023-12-12', signInPrize: '' },
    // { signInDate: '2023-12-13', signInPrize: '' },
    // { signInDate: '2023-12-14', signInPrize: '50MB流量' },
    // { signInDate: '2023-12-15', signInPrize: '' },
    // { signInDate: '2023-12-16', signInPrize: '' },
    // { signInDate: '2023-12-17', signInPrize: '' },
    // { signInDate: '2023-12-18', signInPrize: '' },
    // { signInDate: '2023-12-19', signInPrize: '' },
    // { signInDate: '2023-12-20', signInPrize: '50MB流量' },
    // { signInDate: '2023-12-21', signInPrize: '' },
    // { signInDate: '2023-12-22', signInPrize: '' },
    // { signInDate: '2023-12-23', signInPrize: '' },
    // { signInDate: '2023-12-24', signInPrize: '50MB流量' },
    // { signInDate: '2023-12-25', signInPrize: '' },
    // { signInDate: '2023-12-26', signInPrize: '' },
    // { signInDate: '2023-12-27', signInPrize: '' },
    // { signInDate: '2023-12-28', signInPrize: '50MB流量' },
    // { signInDate: '2023-12-29', signInPrize: '' },
    // { signInDate: '2023-12-30', signInPrize: '' }
  ],
  errmsg: 'OK',
  misdnmask: '178****5414',
  isSignIn: false,
  queryPrize: true,
  timestamp: new Date().getTime(),
};
export const doPrize = {
  result: 0,
  isBj: 1,
  encMisdn: '42306-51222-5459-33039',
  prizelist: [
    {
      prizeDesc: '5MB流量',
      expireDays: 30,
      awardId: 3221,
      isgived: 0,
      prizeType: 5,
      show: 1,
      isBjuser: 1,
      poptips:
        '提示：首次分享赢XXMB，参与机会+1，每人最多可获得X次参与机会哦！',
      prizeId: 1502,
      listId: 0,
      imgurl:
        'https://mobilebj.cn:7443/upload/image/activityTempLateOMP/ActSignintoflip/20220914/1663125808067_file_5MB.png',
      prizeName: '5MB流量',
      addrId: 0,
      id: 0,
    },
    {
      prizeDesc: '5MB流量',
      expireDays: 30,
      awardId: 3221,
      isgived: 0,
      prizeType: 1,
      show: 1,
      isBjuser: 1,
      poptips:
        '提示：首次分享赢XXMB，参与机会+1，每人最多可获得X次参与机会哦！',
      prizeId: 1502,
      listId: 0,
      imgurl:
        'https://mobilebj.cn:7443/upload/image/activityTempLateOMP/ActSignintoflip/20220914/1663125808067_file_5MB.png',
      prizeName: '5MB流量',
      addrId: 0,
      id: 0,
    },
    {
      prizeDesc: '5MB流量',
      expireDays: 30,
      awardId: 3221,
      isgived: 0,
      prizeType: 1,
      show: 1,
      isBjuser: 1,
      poptips:
        '提示：首次分享赢XXMB，参与机会+1，每人最多可获得X次参与机会哦！',
      prizeId: 1502,
      listId: 0,
      imgurl:
        'https://mobilebj.cn:7443/upload/image/activityTempLateOMP/ActSignintoflip/20220914/1663125808067_file_5MB.png',
      prizeName: '5MB流量',
      addrId: 0,
      id: 0,
    },
  ],
  openId: 'o3yHr4riHMG-L_kXh_AQLK98xOrY',
  errmsg: 'OK',
  misdnmask: '158****2816',
  tid: '1640673892357',
  timestamp: new Date().getTime(),
};

export const getSponsor = {
  result: 0,
  sponsor: 'EtYx4ybXD8l9nCi',
  isBj: 1,
  encMisdn: '54594-43030-5507-33036',
  openId: 'o3yHr4riHMG-L_kXh_AQLK98xOrY',
  errmsg: 'OK',
  misdnmask: '158****2816',
  tid: '1650099791022',
  timestamp: 1650099791032,
};
export const helpSponsor = {
  result: 0,
  isBj: 1,
  encMisdn: '13634-47126-5528-33029',
  openId: 'o3yHr4riHMG-L_kXh_AQLK98xOrY',
  errmsg: '尊敬的用户，您本月已领取奖励!',
  misdnmask: '158****2816',
  timestamp: 1650700999840,
  exception: '1GB',
};
/* 1.3.查询用户中奖信息接口 */
export const queryPrizeInfo = {
  result: 0,
  isBj: 1,
  encMisdn: '9538-55318-5616-33036',
  prizelist: [
    {
      prizeId: 1735,
      prizeName: '500MB流量',
      prizeDesc: '500MB流量',
      subtitle: null,
      prizeSign: null,
      prizeType: 1,
      prizeValue: null,
      isStock: 1,
      dayStock: 0,
      monthStock: 0,
      totalStock: 0,
      couponId: 0,
      codePath: null,
      imgurl:
        'https://mobilebj.cn:7443/upload/image/activityTempLateOMP/ActSignintoflip/20220914/1663125808067_file_5MB.png',
      bizAppUrl: null,
      bizWxUrl: null,
      poptips: null,
      expireDays: 30,
      expireDate: null,
      popupImgUrl: null,
      attr: null,
      show: 1,
      id: 0,
      listId: 143088,
      prizeTime: '2022-04-20 17:54:56',
      prizeCode: '10397',
      alias: 'Coupon',
      isBjuser: 1,
      awardId: 3743,
      addrId: 0,
      isgived: 0,
      keyword: 'ActSignIn2022',
    },
    {
      prizeId: 1735,
      prizeName: '500能量',
      prizeDesc: '500能量',
      subtitle: null,
      prizeSign: null,
      prizeType: 12,
      prizeValue: null,
      isStock: 1,
      dayStock: 0,
      monthStock: 0,
      totalStock: 0,
      couponId: 0,
      codePath: null,
      imgurl:
        'https://mobilebj.cn:7443/upload/image/activityTempLateOMP/ActSignintoflip/20220914/1663125808067_file_5MB.png',
      bizAppUrl: null,
      bizWxUrl: null,
      poptips: null,
      expireDays: 30,
      expireDate: null,
      popupImgUrl: null,
      attr: null,
      show: 1,
      id: 0,
      listId: 143066,
      prizeTime: '2022-04-20 17:04:24',
      prizeCode: '10377',
      alias: 'Coupon',
      isBjuser: 1,
      awardId: 3743,
      addrId: 0,
      isgived: 0,
      keyword: 'ActSignintoflip',
    },
    {
      prizeId: 1735,
      prizeName: '500MB流量',
      prizeDesc: '500MB流量',
      subtitle: null,
      prizeSign: null,
      prizeType: 1,
      prizeValue: null,
      isStock: 1,
      dayStock: 0,
      monthStock: 0,
      totalStock: 0,
      couponId: 0,
      codePath: null,
      imgurl:
        'https://mobilebj.cn:7443/upload/image/activityTempLateOMP/ActSignintoflip/20220914/1663125808067_file_5MB.png',
      bizAppUrl: null,
      bizWxUrl: null,
      poptips: null,
      expireDays: 30,
      expireDate: null,
      popupImgUrl: null,
      attr: null,
      show: 1,
      id: 0,
      listId: 143088,
      prizeTime: '2022-04-20 17:54:56',
      prizeCode: '10397',
      alias: 'Coupon',
      isBjuser: 1,
      awardId: 3743,
      addrId: 0,
      isgived: 0,
      keyword: 'ActSignIn2022',
    },
    {
      prizeId: 1735,
      prizeName: '500MB流量',
      prizeDesc: '500MB流量',
      subtitle: null,
      prizeSign: null,
      prizeType: 1,
      prizeValue: null,
      isStock: 1,
      dayStock: 0,
      monthStock: 0,
      totalStock: 0,
      couponId: 0,
      codePath: null,
      imgurl:
        'https://mobilebj.cn:7443/upload/image/activityTempLateOMP/ActSignintoflip/20220914/1663125808067_file_5MB.png',
      bizAppUrl: null,
      bizWxUrl: null,
      poptips: null,
      expireDays: 30,
      expireDate: null,
      popupImgUrl: null,
      attr: null,
      show: 1,
      id: 0,
      listId: 143088,
      prizeTime: '2022-04-20 17:54:56',
      prizeCode: '10397',
      alias: 'Coupon',
      isBjuser: 1,
      awardId: 3743,
      addrId: 0,
      isgived: 0,
      keyword: 'ActSignintoflip',
    },
    {
      prizeId: 1735,
      prizeName: '500MB流量',
      prizeDesc: '500MB流量',
      subtitle: null,
      prizeSign: null,
      prizeType: 1,
      prizeValue: null,
      isStock: 1,
      dayStock: 0,
      monthStock: 0,
      totalStock: 0,
      couponId: 0,
      codePath: null,
      imgurl:
        'https://mobilebj.cn:7443/upload/image/activityTempLateOMP/ActSignintoflip/20220914/1663125808067_file_5MB.png',
      bizAppUrl: null,
      bizWxUrl: null,
      poptips: null,
      expireDays: 30,
      expireDate: null,
      popupImgUrl: null,
      attr: null,
      show: 1,
      id: 0,
      listId: 143088,
      prizeTime: '2022-04-20 17:54:56',
      prizeCode: '10397',
      alias: 'Coupon',
      isBjuser: 1,
      awardId: 3743,
      addrId: 0,
      isgived: 0,
      keyword: 'ActSignIn2022',
    },
    {
      prizeId: 1735,
      prizeName: '500MB流量',
      prizeDesc: '500MB流量',
      subtitle: null,
      prizeSign: null,
      prizeType: 1,
      prizeValue: null,
      isStock: 1,
      dayStock: 0,
      monthStock: 0,
      totalStock: 0,
      couponId: 0,
      codePath: null,
      imgurl:
        'https://mobilebj.cn:7443/upload/image/activityTempLateOMP/ActSignintoflip/20220914/1663125808067_file_5MB.png',
      bizAppUrl: null,
      bizWxUrl: null,
      poptips: null,
      expireDays: 30,
      expireDate: null,
      popupImgUrl: null,
      attr: null,
      show: 1,
      id: 0,
      listId: 143088,
      prizeTime: '2022-04-20 17:54:56',
      prizeCode: '10397',
      alias: 'Coupon',
      isBjuser: 1,
      awardId: 3743,
      addrId: 0,
      isgived: 0,
      keyword: 'ActSignIn2022',
    },
    {
      prizeId: 1735,
      prizeName: '500MB流量',
      prizeDesc: '500MB流量',
      subtitle: null,
      prizeSign: null,
      prizeType: 1,
      prizeValue: null,
      isStock: 1,
      dayStock: 0,
      monthStock: 0,
      totalStock: 0,
      couponId: 0,
      codePath: null,
      imgurl: null,
      bizAppUrl: null,
      bizWxUrl: null,
      poptips: null,
      expireDays: 30,
      expireDate: null,
      popupImgUrl: null,
      attr: null,
      show: 1,
      id: 0,
      listId: 143065,
      prizeTime: '2022-04-20 16:56:02',
      prizeCode: '10376',
      alias: 'Coupon',
      isBjuser: 1,
      awardId: 3743,
      addrId: 0,
      isgived: 0,
      keyword: 'ActSignIn2022',
    },
    {
      prizeId: 1735,
      prizeName: '业务奖励',
      prizeDesc: '业务奖励',
      subtitle: null,
      prizeSign: null,
      prizeType: 6,
      url: 'http://www.baidu.com',
      prizeValue: null,
      isStock: 1,
      dayStock: 0,
      monthStock: 0,
      totalStock: 0,
      couponId: 0,
      codePath: null,
      imgurl: null,
      bizAppUrl: null,
      bizWxUrl: null,
      poptips: null,
      expireDays: 30,
      expireDate: null,
      popupImgUrl: null,
      attr: null,
      show: 1,
      id: 0,
      listId: 143062,
      prizeTime: '2022-04-20 16:44:38',
      prizeCode: '10373',
      alias: 'Coupon',
      isBjuser: 1,
      awardId: 3743,
      addrId: 0,
      isgived: 0,
      keyword: 'ActSignIn2022',
    },
  ],
  openId: 'o3yHr4riHMG-L_kXh_AQLK98xOrY',
  errmsg: 'OK',
  misdnmask: '158****2816',
  timestamp: 1650451434513,
};
/* 1.3.查询用户 */
export const queryTuijianzInfo = {
  result: 0,
  isBj: 1,
  encMisdn: '9538-55318-5616-33036',
  openId: 'o3yHr4riHMG-L_kXh_AQLK98xOrY',
  errmsg: 'OK',
  info: {
    isNewUser: false, // 是否新人
    isEinvoice: true, // 是否有新人专区-电子发票标签
    isVoice: true, // 语音标签
    isBroadband: true, // 宽带标签},
  },
  misdnmask: '158****2816',
  timestamp: 1650451434513,
};
/* 1.3.查询用户中奖信息接口 */
export const operate_unifyH5 = {
  result: 0,
  region: [
    {
      icon_color: '#000000',
      catype: '0',
      blocktitle: '翻牌子任务banner',
      style: '0',
      block: [
        {
          serialversionuid: -1868137163503345987,
          opid: 52072,
          imgsize: '240x690',
          type: 5,
          iscollect: 0,
          imgurl:
            'https://www.mobilebj.cn:7443/upload/image/flowUpload/2022/07/01/NNBZBa.jpg',
          mustlogin: 0,
          channelsign: '',
          coid: 39091,
          mark_expire: '20220701141650',
          opchannel: 'APP',
          title: '翻牌子任务bannber',
          timestamp: '2022-07-01 16:12:02.0',
          createtime: '2022-07-01 14:16:50',
          url: 'http://www.baidu.com',
          button_status: '0',
          sharestatus: '0',
        },
      ],
      keyword: 'fanpaizi_renwubanner',
      cid: '1150',
      subicon_color: '#000000',
    },
    {
      icon_color: '#000000',
      catype: '0',
      blocktitle: '长短期活动入口-banner',
      style: '0',
      block: [
        {
          serialversionuid: -1868137163503345987,
          opid: 52076,
          imgsize: '240x690',
          type: 5,
          iscollect: 0,
          imgurl:
            'https://www.mobilebj.cn:7443/upload/image/flowUpload/2022/07/01/USqz0w.jpg',
          mustlogin: 0,
          channelsign: '',
          coid: 39093,
          mark_expire: '20220701141708',
          opchannel: 'APP',
          title: '养小树',
          timestamp: '2022-07-01 16:12:02.0',
          createtime: '2022-07-01 14:17:08',
          url: 'http://www.baidu.com',
          button_status: '0',
          sharestatus: '0',
        },
        {
          serialversionuid: -1868137163503345987,
          opid: 52078,
          imgsize: '1280x720',
          type: 5,
          iscollect: 0,
          imgurl:
            'https://www.mobilebj.cn:7443/upload/image/flowUpload/2022/07/01/CCewhx.jpg',
          mustlogin: 0,
          channelsign: '',
          coid: 39094,
          mark_expire: '20220701142233',
          opchannel: 'APP',
          title: '能量中心',
          timestamp: '2022-07-01 16:12:02.0',
          createtime: '2022-07-01 14:22:33',
          url: 'http://www.baidu.com',
          button_status: '0',
          sharestatus: '0',
        },
      ],
      keyword: 'actsignin_bannerjt',
      cid: '1151',
      subicon_color: '#000000',
    },
    {
      icon_color: '#000000',
      catype: '0',
      blocktitle: '长短期活动入口-轮播',
      style: '0',
      block: [
        {
          serialversionuid: -1868137163503345987,
          opid: 52076,
          imgsize: '240x690',
          type: 5,
          iscollect: 0,
          imgurl:
            'https://www.mobilebj.cn:7443/upload/image/flowUpload/2022/07/01/USqz0w.jpg',
          mustlogin: 0,
          channelsign: '',
          coid: 39093,
          mark_expire: '20220701141708',
          opchannel: 'APP',
          title: '养小树',
          timestamp: '2022-07-01 16:12:02.0',
          createtime: '2022-07-01 14:17:08',
          url: 'http://www.baidu.com',
          button_status: '0',
          sharestatus: '0',
        },
        {
          serialversionuid: -1868137163503345987,
          opid: 52078,
          imgsize: '1280x720',
          type: 5,
          iscollect: 0,
          imgurl:
            'https://www.mobilebj.cn:7443/upload/image/flowUpload/2022/07/01/CCewhx.jpg',
          mustlogin: 0,
          channelsign: '',
          coid: 39094,
          mark_expire: '20220701142233',
          opchannel: 'APP',
          title: '能量中心',
          timestamp: '2022-07-01 16:12:02.0',
          createtime: '2022-07-01 14:22:33',
          url: 'http://www.baidu.com',
          button_status: '0',
          sharestatus: '0',
        },
      ],
      keyword: 'actsignin_lunbojt',
      cid: '1152',
      subicon_color: '#000000',
    },
    {
      icon_color: '#000000',
      catype: '0',
      blocktitle: '签到头图-banner',
      style: '0',
      block: [
        {
          serialversionuid: -1868137163503345987,
          opid: 52080,
          imgsize: '240x690',
          type: 5,
          iscollect: 0,
          imgurl:
            'https://www.mobilebj.cn:7443/upload/image/flowUpload/2022/07/01/SmkTpQ.jpg',
          mustlogin: 0,
          channelsign: '',
          coid: 39095,
          mark_expire: '20220701143834',
          opchannel: 'APP',
          title: '签到头图',
          timestamp: '2022-07-01 16:12:02.0',
          createtime: '2022-07-01 14:38:34',
          url: 'http://www.baidu.com',
          button_status: '0',
          sharestatus: '0',
        },
      ],
      keyword: 'actqiandao_toutubanner',
      cid: '1153',
      subicon_color: '#000000',
    },
  ],
  current_timestamp: 1656663971057,
};
export const queryStatus = {
  visitShare: false,
  isBj: 1,
  encMisdn: '34114-22550-5555-33028',
  openId: 'o3yHr4riHMG-L_kXh_AQLK98xOrY',
  count: 0,
  errmsg: 'OK',
  shareNum: 2,
  tid: '1662197973950',
  isSignIn: false,
  result: 0,
  visitStatus: false,
  misdnmask: '158****2816',
  usedCount: 2,
  shareStatus: false,
  timestamp: 1662197973960,
};
