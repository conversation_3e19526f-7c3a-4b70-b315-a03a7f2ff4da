import Mock from 'mockjs'
import { getSignIn, doPrize, queryPrizeInfo, getSponsor, helpSponsor, queryTuijianzInfo, queryOrderBiz, operate_unifyH5, queryStatus } from './data'

import { getQueryString } from '@/utils/utils'
// let wxRelate = 'WeiXinWeb'
// if (location.port !== '' || location.hostname === 'st.bj.chinamobile.com') {
//     wxRelate = 'WeiXinWebtmp'
// }
const keyword = 'ActSignIn2023JT'
const basePath = 'ActSignIn2023'
const keywordfpz = 'ActSignintoflip'
const basePathfpz = 'ActSignintoflip'
const ua = window.navigator.userAgent.toLowerCase()
let channel = 'JT'

if (window.aspireweb) {
    channel = 'APP'
}
if (ua.indexOf('micromessenger') > -1) {
    channel = 'WX'
}
if (getQueryString('mm') !== null) {
    channel = 'TUWEN'
}

Mock.mock(RegExp(`/${basePath}/getSignIn/${channel}/${keyword}`), getSignIn)
Mock.mock(RegExp(`/${basePath}/doPrize/${channel}/${keyword}`), doPrize)
    // Mock.mock(RegExp(`/${basePath}/repairSign/${channel}/${keyword}`), doPrize)
Mock.mock(RegExp(`/${basePath}/queryPrizeList/${channel}/${keyword}`), queryPrizeInfo)
Mock.mock(RegExp(`/${basePath}/helpSponsor/${channel}/${keyword}`), helpSponsor)
Mock.mock(RegExp(`/${basePath}/queryTuijianzInfo/${channel}/${keyword}`), queryTuijianzInfo)
Mock.mock(RegExp('/app/operate_unifyH5'), operate_unifyH5)
Mock.mock(RegExp(`/${basePathfpz}/queryStatus/${channel}/${keywordfpz}`), queryStatus)
Mock.mock(RegExp(`/${basePathfpz}/doPrize/${channel}/${keywordfpz}`), doPrize)
Mock.mock(RegExp(`/${basePathfpz}/assistantSponsorNew/${channel}/${keywordfpz}`), doPrize)
Mock.mock(RegExp(`/${basePathfpz}/saveVisit/${channel}/${keywordfpz}`), getSponsor)
Mock.mock(RegExp(`/${basePathfpz}/getSponsor/${channel}/${keywordfpz}`), getSponsor)
Mock.mock(RegExp(`/${basePath}/queryAwardInfo/${channel}/${keyword}`), () => {
    return {
        result: 0,
        timestamp: new Date().getTime(),
        list: [{
                awardName: 'date20240222',
                prize: [{
                    prizeName: '66MB元宵礼',
                    imgurl: 'https://mobilebj.cn:7443/upload/image/activityTempLateOMP/ActSignintoflip/20220914/1663125808067_file_5MB.png'
                }]
            }, {
                awardName: 'date20231223',
                prize: [{
                    prizeName: '5MB',
                    imgurl: 'https://mobilebj.cn:7443/upload/image/activityTempLateOMP/ActSignintoflip/20220914/1663125808067_file_5MB.png'
                }]
            }, {
                awardName: 'date20231224',
                prize: [{
                    prizeName: '5MB',
                    imgurl: 'https://mobilebj.cn:7443/upload/image/activityTempLateOMP/ActSignintoflip/20220914/1663125808067_file_5MB.png'
                }]
            }, {
                awardName: 'date20231225',
                prize: [{
                    prizeName: '5MB',
                    imgurl: 'https://mobilebj.cn:7443/upload/image/activityTempLateOMP/ActSignintoflip/20220914/1663125808067_file_5MB.png'
                }]
            }, {
                awardName: 'date20231226',
                prize: [{
                    prizeName: '5MB',
                    imgurl: 'https://mobilebj.cn:7443/upload/image/activityTempLateOMP/ActSignintoflip/20220914/1663125808067_file_5MB.png'
                }]
            },
            {
                awardName: 'prize1',
                prize: [{
                    prizeName: '5MB',
                    imgurl: 'https://mobilebj.cn:7443/upload/image/activityTempLateOMP/ActSignintoflip/20220914/1663125808067_file_5MB.png'
                }]
            }, {
                awardName: 'prize4',
                prize: [{
                    prizeName: '10MB',
                    imgurl: 'https://h5.bj.10086.cn/cmcc_activity/uniApp/bj-mini-app/act/healthyday/bg.png'
                }]
            }, {
                awardName: 'prize16',
                prize: [{
                    prizeName: '10MB',
                    imgurl: 'https://mobilebj.cn:7443/upload/image/activityTempLateOMP/ActSignintoflip/20220914/1663125808067_file_5MB.png'
                }]
            }, {
                awardName: 'prize26',
                prize: [{
                    prizeName: '10MB',
                    imgurl: 'https://mobilebj.cn:7443/upload/image/activityTempLateOMP/ActSignintoflip/20220914/1663125808067_file_5MB.png'
                }]
            }, {
                awardName: 'signFull',
                prize: [{
                    prizeName: '5MB流量',
                    imgurl: 'https://mobilebj.cn:7443/upload/image/activityTempLateOMP/ActSignintoflip/20220914/1663125808067_file_5MB.png'
                }, {
                    prizeName: '10MB',
                    imgurl: 'https://mobilebj.cn:7443/upload/image/activityTempLateOMP/ActSignintoflip/20220914/1663125808067_file_5MB.png'
                }, {
                    prizeName: '10MB',
                    imgurl: 'https://mobilebj.cn:7443/upload/image/activityTempLateOMP/ActSignintoflip/20220914/1663125808067_file_5MB.png'
                }, {
                    prizeName: '10MB',
                    imgurl: 'https://mobilebj.cn:7443/upload/image/activityTempLateOMP/ActSignintoflip/20220914/1663125808067_file_5MB.png'
                }, {
                    prizeName: '10MB',
                    imgurl: 'https://mobilebj.cn:7443/upload/image/activityTempLateOMP/ActSignintoflip/20220914/1663125808067_file_5MB.png'
                }, {
                    prizeName: '10MB',
                    imgurl: 'https://mobilebj.cn:7443/upload/image/activityTempLateOMP/ActSignintoflip/20220914/1663125808067_file_5MB.png'
                }, {
                    prizeName: '10MB',
                    imgurl: 'https://mobilebj.cn:7443/upload/image/activityTempLateOMP/ActSignintoflip/20220914/1663125808067_file_5MB.png'
                }, {
                    prizeName: '10MB',
                    imgurl: 'https://mobilebj.cn:7443/upload/image/activityTempLateOMP/ActSignintoflip/20220914/1663125808067_file_5MB.png'
                }, {
                    prizeName: '10MB',
                    imgurl: 'https://mobilebj.cn:7443/upload/image/activityTempLateOMP/ActSignintoflip/20220914/1663125808067_file_5MB.png'
                }]
            }
        ]
    }
})
Mock.mock(RegExp(`/${basePath}/customPrize/${channel}/${keyword}`), () => {
    return {
        result: 0,
        prizelist: [{
            subtitle: '这是附表其',
            prizeDesc: '5MB流量',
            expireDays: 30,
            awardId: 3221,
            isgived: 0,
            prizeType: 1,
            show: 1,
            isBjuser: 1,
            poptips: '提示：首次分享赢XXMB，参与机会+1，每人最多可获得X次参与机会哦！',
            prizeId: 1502,
            listId: 0,
            imgurl: 'https://mobilebj.cn:7443/upload/image/activityTempLateOMP/ActSignintoflip/20220914/1663125808067_file_5MB.png',
            prizeName: '5MB流量',
            addrId: 0,
            id: 0,
        }, {
            prizeDesc: '5MB',
            expireDays: 30,
            awardId: 3221,
            isgived: 0,
            prizeType: 1,
            show: 1,
            isBjuser: 1,
            poptips: '提示：首次分享赢XXMB，参与机会+1，每人最多可获得X次参与机会哦！',
            prizeId: 1502,
            listId: 0,
            imgurl: 'https://mobilebj.cn:7443/upload/image/activityTempLateOMP/ActSignintoflip/20220914/1663125808067_file_5MB.png',
            prizeName: '5MB',
            addrId: 0,
            id: 0,
        }, {
            prizeDesc: '5MB',
            expireDays: 30,
            awardId: 3221,
            isgived: 0,
            prizeType: 1,
            show: 1,
            isBjuser: 1,
            poptips: '提示：首次分享赢XXMB，参与机会+1，每人最多可获得X次参与机会哦！',
            prizeId: 1502,
            listId: 0,
            imgurl: 'https://mobilebj.cn:7443/upload/image/activityTempLateOMP/ActSignintoflip/20220914/1663125808067_file_5MB.png',
            prizeName: '5MB',
            addrId: 0,
            id: 0,
        }]
    }
})
Mock.mock(RegExp(`/${basePath}/queryTjlbInfo/${channel}/${keyword}`), () => {
    return {
        result: 0,
        isPop: true,
        tjlbStatus: 2,
        signFullStatus: 0,
        isSendSubscribe: false
    }
})
Mock.mock(RegExp('http://jiguang.coc.10086.cn'), () => {
    return {
        result: 0,
        isPop: true,
        tjlbStatus: 2
    }
})
Mock.mock(RegExp(`/ActSignIn2023/ContinuousSignIn2023/getActivityMain/${channel}/ContinuousSignIn2023`), {
    "isBj": 1,
    "encMisdn": "21826-10262-5467-33032",
    "openId": "oYHwl444Bixe2Wi8vFkm8gxR9Gco",
    "prizeStatus3": 2,
    "prizeStatus2": 2,
    "errmsg": "ok",
    "prizeStatus1": 2,
    "result": "0",
    "currentSignIndex": 6,
    "jtEncMisdn": "duP3MLKteB3jeshjAyZYSw==",
    "signStatusList": [
        1,
        1,
        1,
        1
    ],
    "misdnmask": "158****2816",
    "timestamp": 1709026572925
})
Mock.mock(RegExp('/ActSignIn2023/querySignCardTaskStatus/JT/ActSignIn2023JT'), {
    result: 0,
    list: [{
        task1: 0
    }, {
        task2: 1
    }, {
        task3: 1
    }]
})

Mock.mock(RegExp(`/ActSignIn2023/ContinuousSignIn/getActivityMain/JT/ContinuousSignIn2025`), {
    "result": 0,
    "isBj": 1,
    "encMisdn": "34114-18454-5391-33028",
    "jtEncMisdn": "duP3MLKteB3jeshjAyZYSw==",
    "openId": "oYHwl444Bixe2Wi8vFkm8gxR9Gco",
    "errmsg": "ok",
    "list": [{
            "signStatus": 1,
            "signPrize": "",
            "month": 1,
            "prizeStatus": 0
        },
        {
            "signStatus": 0,
            "signPrize": "",
            "month": 2,
            "prizeStatus": 0
        },
        {
            "signStatus": 1,
            "signPrize": "",
            "month": 3,
            "prizeStatus": 0
        },
        {
            "signStatus": 0,
            "signPrize": "",
            "month": 4,
            "prizeStatus": 0
        },
        {
            "signStatus": 0,
            "signPrize": "",
            "month": 5,
            "prizeStatus": 0
        },
        {
            "signStatus": 0,
            "signPrize": "",
            "month": 6,
            "prizeStatus": 0
        }
    ],
    "misdnmask": "158****2816",
    "timestamp": new Date('2025/03/01 00:01:00').getTime()
})
Mock.mock(RegExp(`/ActSignIn2023/ContinuousSignIn/customPrize/JT/ContinuousSignIn2025`), {
    "result": 0,
    "errmsg": 'OK',
    "prizeList": [{ "prizeId": 3615, "prizeName": "100MB流量", "prizeDesc": "100MB流量", "subtitle": "您获得签到奖励100MB流量", "prizeSign": null, "prizeType": 1, "prizeValue": null, "isStock": 0, "dayStock": 0, "monthStock": 0, "totalStock": 0, "indexname": null, "couponId": 184067, "codePath": null, "imgurl": "https://mobilebj.cn:7443/upload/image/activityTempLateOMP/ActSignIn2023JT/20230829/1693299348355_file_100MB_.png", "bizAppUrl": null, "bizWxUrl": null, "bizJTUrl": null, "poptips": "提示：首次分享赢XXMB，参与机会+1，每人最多可获得X次参与机会哦！", "expireDays": 30, "expireDate": null, "popupImgUrl": null, "attr": null, "show": 1, "exchangeResultImgUrl": null, "exchangeResultImgShowUrl": null, "batchIdValue": null, "actIdValue": null, "isStockRefresh": 0, "stockRefreshHour": 0, "stockRefreshMinute": 0, "id": 0, "listId": 0, "prizeTime": null, "prizeCode": "35757", "alias": null, "isBjuser": 1, "awardId": 6622, "addrId": 0, "isgived": 0 }]
})
Mock.mock(RegExp(`/${basePath}/repairSign/${channel}/${keyword}`), { "result": 0, "isBj": 1, "encMisdn": "58690-22550-5571-33032", "prizelist": [{ "prizeId": 3615, "prizeName": "100MB流量", "prizeDesc": "100MB流量", "subtitle": "您获得签到奖励100MB流量", "prizeSign": null, "prizeType": 1, "prizeValue": null, "isStock": 0, "dayStock": 0, "monthStock": 0, "totalStock": 0, "indexname": null, "couponId": 184067, "codePath": null, "imgurl": "https://mobilebj.cn:7443/upload/image/activityTempLateOMP/ActSignIn2023JT/20230829/1693299348355_file_100MB_.png", "bizAppUrl": null, "bizWxUrl": null, "bizJTUrl": null, "poptips": "提示：首次分享赢XXMB，参与机会+1，每人最多可获得X次参与机会哦！", "expireDays": 30, "expireDate": null, "popupImgUrl": null, "attr": null, "show": 1, "exchangeResultImgUrl": null, "exchangeResultImgShowUrl": null, "batchIdValue": null, "actIdValue": null, "isStockRefresh": 0, "stockRefreshHour": 0, "stockRefreshMinute": 0, "id": 0, "listId": 0, "prizeTime": null, "prizeCode": "35757", "alias": null, "isBjuser": 1, "awardId": 6622, "addrId": 0, "isgived": 0 }], "jtEncMisdn": "duP3MLKteB3jeshjAyZYSw==", "openId": "oYHwl444Bixe2Wi8vFkm8gxR9Gco", "errmsg": "OK", "misdnmask": "158****2816", "timestamp": 1721035790165 })


Mock.mock(RegExp('/app/familyCircle/queryUserCircleZiType'), {
    "result": 0,
    "isMigrate": 0,
    userType: 3
})
Mock.mock(RegExp('/app/familyCircle/agreeUpgrade'), {
    "result": '0',
    "isMigrate": 0,
    "userType": 3
})