import { links } from '@/projects/checkin-new/config/link'

export const PRIZE_PARAMS = {
  1: {
    prizeName: '10MB',
    prizeImg: require('../assets/checkin/prize-ll-10.png'),
    type: 'll'
  },
  2: {
    prizeName: '50能量',
    prizeImg: require('../assets/checkin/prize-50.png'),
    type: 'nl'
  },
  3: {
    prizeName: '50MB',
    prizeImg: require('../assets/checkin/prize-ll-50.png'),
    type: 'll'
  },
  4: {
    prizeName: '50能量',
    prizeImg: require('../assets/checkin/prize-50.png'),
    type: 'nl'
  },
  5: {
    prizeName: '100MB',
    prizeImg: require('../assets/checkin/prize-ll-100.png'),
    type: 'll'
  },
  6: {
    prizeName: '100能量',
    prizeImg: require('../assets/checkin/prize-100.png'),
    type: 'nl'
  },
  16: {
    prizeName: '100能量',
    prizeImg: require('../assets/checkin/prize-100.png'),
    type: 'nl'
  },
  26: {
    prizeName: '100能量',
    prizeImg: require('../assets/checkin/prize-100.png'),
    type: 'nl'
  }
}
export const PRIZE_DAYS = [1, 2, 3, 4, 5, 6, 16, 26]
// 星期一：能量中心，星期二：养小树，星期三：幸运星期三，星期四：幸运星期四，星期五：福利社专区，星期六：家庭专区，星期日：砍价
// 更改为 除了星期三：幸运星期三外，其余为养小树
export const BANNER_LIST = [
  // {
  //   title: '砍价',
  //   imgUrl: require('@/projects/checkin-new/assets/banner/banner7.png'),
  //   url: links.bargain,
  //   chama: {
  //     qdP: 'P00000025029,中国移动APP每日签到_签到获奖弹窗_砍价',
  //     qdNP: 'P00000025030,中国移动APP每日签到_签到未获奖弹窗_砍价',
  //     fpzP: 'P00000025035,中国移动APP每日签到_无翻牌机会获奖弹窗_砍价',
  //     fpNP: 'P00000025036,中国移动APP每日签到_无翻牌机会未获奖弹窗_砍价'
  //   }
  // },
  // {
  //   title: '能量中心',
  //   imgUrl: require('@/projects/checkin-new/assets/banner/banner1.png'),
  //   url: links.energyZoneUrl,
  //   chama: {
  //     qdP: 'P00000025029,中国移动APP每日签到_签到获奖弹窗_能量中心',
  //     qdNP: 'P00000025030,中国移动APP每日签到_签到未获奖弹窗_能量中心',
  //     fpzP: 'P00000025035,中国移动APP每日签到_无翻牌机会获奖弹窗_能量中心',
  //     fpNP: 'P00000025036,中国移动APP每日签到_无翻牌机会未获奖弹窗_能量中心'
  //   }
  // },
  {
    title: '养小树',
    imgUrl: require('@/projects/checkin-new/assets/banner/banner2.png'),
    url: links.tree,
    chama: {
      qdP: 'P00000025029,中国移动APP每日签到_签到获奖弹窗_养小树',
      qdNP: 'P00000025030,中国移动APP每日签到_签到未获奖弹窗_养小树',
      fpzP: 'P00000025035,中国移动APP每日签到_无翻牌机会获奖弹窗_养小树',
      fpNP: 'P00000025036,中国移动APP每日签到_无翻牌机会未获奖弹窗_养小树'
    }
  },
  {
    title: '养小树',
    imgUrl: require('@/projects/checkin-new/assets/banner/banner2.png'),
    url: links.tree,
    chama: {
      qdP: 'P00000025029,中国移动APP每日签到_签到获奖弹窗_养小树',
      qdNP: 'P00000025030,中国移动APP每日签到_签到未获奖弹窗_养小树',
      fpzP: 'P00000025035,中国移动APP每日签到_无翻牌机会获奖弹窗_养小树',
      fpNP: 'P00000025036,中国移动APP每日签到_无翻牌机会未获奖弹窗_养小树'
    }
  },
  {
    title: '养小树',
    imgUrl: require('@/projects/checkin-new/assets/banner/banner2.png'),
    url: links.tree,
    chama: {
      qdP: 'P00000025029,中国移动APP每日签到_签到获奖弹窗_养小树',
      qdNP: 'P00000025030,中国移动APP每日签到_签到未获奖弹窗_养小树',
      fpzP: 'P00000025035,中国移动APP每日签到_无翻牌机会获奖弹窗_养小树',
      fpNP: 'P00000025036,中国移动APP每日签到_无翻牌机会未获奖弹窗_养小树'
    }
  },
  {
    title: '幸运星期三',
    imgUrl: require('@/projects/checkin-new/assets/banner/banner3.png'),
    url: links.XY3,
    chama: {
      qdP: 'P00000025029,中国移动APP每日签到_签到获奖弹窗_幸运星期三',
      qdNP: 'P00000025030,中国移动APP每日签到_签到未获奖弹窗_幸运星期三',
      fpzP: 'P00000025035,中国移动APP每日签到_无翻牌机会获奖弹窗_幸运星期三',
      fpNP: 'P00000025036,中国移动APP每日签到_无翻牌机会未获奖弹窗_幸运星期三'
    }
  },
  // {
  //   title: '幸运星期四',
  //   imgUrl: require('@/projects/checkin-new/assets/banner/banner4.png'),
  //   url: links.XY4,
  //   chama: {
  //     qdP: 'P00000025029,中国移动APP每日签到_签到获奖弹窗_幸运星期四',
  //     qdNP: 'P00000025030,中国移动APP每日签到_签到未获奖弹窗_幸运星期四',
  //     fpzP: 'P00000025035,中国移动APP每日签到_无翻牌机会获奖弹窗_幸运星期四',
  //     fpNP: 'P00000025036,中国移动APP每日签到_无翻牌机会未获奖弹窗_幸运星期四'
  //   }
  // },
  // {
  //   title: '福利社专区',
  //   imgUrl: require('@/projects/checkin-new/assets/banner/banner5.png'),
  //   url: links.flUrl,
  //   chama: {
  //     qdP: 'P00000025029,中国移动APP每日签到_签到获奖弹窗_福利社专区',
  //     qdNP: 'P00000025030,中国移动APP每日签到_签到未获奖弹窗_福利社专区',
  //     fpzP: 'P00000025035,中国移动APP每日签到_无翻牌机会获奖弹窗_福利社专区',
  //     fpNP: 'P00000025036,中国移动APP每日签到_无翻牌机会未获奖弹窗_福利社专区'
  //   }
  // },
  // {
  //   title: '家庭专区',
  //   imgUrl: require('@/projects/checkin-new/assets/banner/banner6.png'),
  //   url: links.familyArea,
  //   chama: {
  //     qdP: 'P00000025029,中国移动APP每日签到_签到获奖弹窗_家庭专区',
  //     qdNP: 'P00000025030,中国移动APP每日签到_签到未获奖弹窗_家庭专区',
  //     fpzP: 'P00000025035,中国移动APP每日签到_无翻牌机会获奖弹窗_家庭专区',
  //     fpNP: 'P00000025036,中国移动APP每日签到_无翻牌机会未获奖弹窗_家庭专区'
  //   }
  // }
  {
    title: '养小树',
    imgUrl: require('@/projects/checkin-new/assets/banner/banner2.png'),
    url: links.tree,
    chama: {
      qdP: 'P00000025029,中国移动APP每日签到_签到获奖弹窗_养小树',
      qdNP: 'P00000025030,中国移动APP每日签到_签到未获奖弹窗_养小树',
      fpzP: 'P00000025035,中国移动APP每日签到_无翻牌机会获奖弹窗_养小树',
      fpNP: 'P00000025036,中国移动APP每日签到_无翻牌机会未获奖弹窗_养小树'
    }
  },
  {
    title: '养小树',
    imgUrl: require('@/projects/checkin-new/assets/banner/banner2.png'),
    url: links.tree,
    chama: {
      qdP: 'P00000025029,中国移动APP每日签到_签到获奖弹窗_养小树',
      qdNP: 'P00000025030,中国移动APP每日签到_签到未获奖弹窗_养小树',
      fpzP: 'P00000025035,中国移动APP每日签到_无翻牌机会获奖弹窗_养小树',
      fpNP: 'P00000025036,中国移动APP每日签到_无翻牌机会未获奖弹窗_养小树'
    }
  },
  {
    title: '养小树',
    imgUrl: require('@/projects/checkin-new/assets/banner/banner2.png'),
    url: links.tree,
    chama: {
      qdP: 'P00000025029,中国移动APP每日签到_签到获奖弹窗_养小树',
      qdNP: 'P00000025030,中国移动APP每日签到_签到未获奖弹窗_养小树',
      fpzP: 'P00000025035,中国移动APP每日签到_无翻牌机会获奖弹窗_养小树',
      fpNP: 'P00000025036,中国移动APP每日签到_无翻牌机会未获奖弹窗_养小树'
    }
  }
]

// 常用功能推荐入口:目前一期暂时不使用
export const recommendParams = {
  mobileTerminal: {
    title: '超值购机',
    imgUrl: require('@/projects/checkin-new/assets/tj/phone.png'),
    url: links.phone,
    chama: '220613_QDGB_JHY_JCTJ_SJZD'
  },
  welfare: {
    title: '福利社专区',
    imgUrl: require('@/projects/checkin-new/assets/tj/flUrl.png'),
    url: links.flUrl,
    chama: '220613_QDGB_JHY_JCTJ_FLSZQ'
  },
  recharge: {
    title: '充值95折',
    imgUrl: require('@/projects/checkin-new/assets/tj/cz.png'),
    url: links.cz,
    chama: '220613_QDGB_JHY_JCTJ_CZRK'
  },
  numberCard: {
    title: '靓号随心选',
    imgUrl: require('@/projects/checkin-new/assets/tj/cardZone.png'),
    url: links.cardZone,
    chama: '220613_QDGB_JHY_JCTJ_HK'

  },
  newerArea: {
    title: '新人专区',
    imgUrl: require('@/projects/checkin-new/assets/tj/newFace.png'),
    url: links.newFace,
    chama: '220613_QDGB_JHY_JCTJ_XRZQ'

  },
  invoice: {
    title: '线上开发票',
    imgUrl: require('@/projects/checkin-new/assets/tj/invoiceye.png'),
    url: links.invoiceye,
    chama: '220613_QDGB_JHY_JCTJ_DZFP'

  },
  monthlyReport: {
    title: '电子月报',
    imgUrl: require('@/projects/checkin-new/assets/tj/monthlyReport.png'),
    url: links.monthlyReport,
    chama: '220613_QDGB_JHY_JCTJ_DZYB'
  },
  languageBusiness: {
    title: '我的权益',
    imgUrl: require('@/projects/checkin-new/assets/tj/YY.png'),
    url: links.YY,
    chama: '220613_QDGB_JHY_JCTJ_WDQY'

  },
  broadbandBusiness: {
    title: '宽带服务',
    imgUrl: require('@/projects/checkin-new/assets/tj/KD.png'),
    url: links.KD,
    chama: '220613_QDGB_JHY_JCTJ_KDYW'
  },
  videoBusinessHall: {
    title: '视频营业厅',
    imgUrl: require('@/projects/checkin-new/assets/tj/video.png'),
    url: links.video,
    chama: '220613_QDGB_JHY_JCTJ_SPYYT'
  }
}

let baseUrl = 'https://h5.bj.10086.cn/'
// 端口不为空或链接路径存在GRAYACTStatic，为灰度
if (window.location.port !== '' || window['loca' + 'tion'].href.indexOf('GRAYACTStatic') > -1) {
  // baseUrl = 'https://www.mobilebj.cn:7443/'
  baseUrl = 'https://st.bj.chinamobile.com:7443/'
}
export const GRAY_AVATARURL = require('@/projects/checkin-new/assets/common/no-login-avatar.png')
export const DEFAULT_AVATARURL = require('@/projects/checkin-new/assets/common/avatar.png')
export const SUCCESS_CODE = 0

export const HAS_RECEIVE_CODE = 20011 // 已经领取助力奖励
export const HAS_HELP = 20002 // 已经助力
export const HAS_INVITE_SMS = 20010 // 今日已短信邀请过该用户
export const MYSELF_SMS = 20013 // 自己给自己发短信限制
export const BASEURL = baseUrl
export const IS_YW_JMESS = '很抱歉，本活动仅限北京移动用户参与哦，感谢您的关注~'
export const HB_MESS = '活动太火爆了，稍后再来吧~'
export const YM_OVERTIME = '页面超时，还请退出再重新进入~'
export const SMS_SUCCESS_MESS = '已成功下发短信邀请您的好友，记得喊好友参与哦~'
export const MYSELF_SMS_MESS = '很抱歉，自己不能邀请自己哦~'
export const SMS_AGAIN_MESS = '您今天已短信邀请过该好友了哦，试试邀请其他好友吧~'

