/*
 * @Author: zhen<PERSON><PERSON><PERSON> <EMAIL>
 * @Date: 2023-03-24 19:08:31
 * @LastEditors: zhengwenling <EMAIL>
 * @LastEditTime: 2025-03-28 17:34:01
 * @FilePath: \multiple-project\src\projects\checkin-new\store\index.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import Vue from 'vue'
import Vuex from 'vuex'

Vue.use(Vuex)

export default new Vuex.Store({
    state: {
        canClick: false,
        agent: false,
        signInList: [],
        futurePrizeList: [],
        currentDate: new Date().getTime(),
        recentPrizeDate: {},
        misdnmask: '',
        isSignIn: false, // 当天签到状态
        signCardNum: 0, // 补签卡数
        actsignTasks: {}, // 补签任务配置内容
        actsignTaskStatuslist: [], // 补签任务状态
        testTime: false, // 测试时间
        isCurrentMonthSubscribe: false, // 是否当前月已经订阅
        netageTask: {}, //网龄卡补签任务
        fingerprintUrl: '', // 封控指纹链接
        fingerprintId: '', // 封控ID
        isMigrate: 0, // 家人圈
        fmlUpgradeMsg: {} // 家人圈升级消息
    },
    mutations: {
        UPDATE_FMLUPGRADEMSG(state, b) {
            state.fmlUpgradeMsg = b
        },
        UPDATE_ISMIGRATE(state, b) {
            state.isMigrate = b
        },
        UPDATE_CANDONG(state, b) {
            state.canClick = b
        },
        UPDATE_AGENT(state, b) {
            state.agent = b
        },
        UPDATE_SIGNINLIST(state, b) {
            state.signInList = b
        },
        UPDATE_FUTUREPRIZELIST(state, b) {
            state.futurePrizeList = b
        },
        UPDATE_CURRENTDATE(state, b) {
            state.currentDate = b
        },
        UPDATE_RECENTPRIZEDATE(state, b) {
            state.recentPrizeDate = b
        },
        UPDATE_MISDNMASK(state, b) {
            state.misdnmask = b
        },
        UPDATE_ISSIGNIN(state, b) {
            state.isSignIn = b
        },
        UPDATE_SIGNCARDNUM(state, b) {
            state.signCardNum = b
        },
        UPDATE_ACTSIGNTASKREGION(state, b) {
            if (b && b.block) {
                state.actsignTasks = b.block.filter(item => item.url.indexOf('/cmcc_app/net-age/index.html') === -1)
                state.netageTask = b.block.filter(item => item.url.indexOf('/cmcc_app/net-age/index.html') !== -1)[0] || {}
            }
        },
        UPDATE_ACTSIGNTASKSTATUSLIST(state, b) {
            state.actsignTaskStatuslist = b
        },
        UPDATE_TESTTIME(state, b) {
            state.testTime = b
        },
        UPDATE_ISCURRENTMONTHSUBSCRIBE(state, b) {
            state.isCurrentMonthSubscribe = b
        },
        UPDATE_FINGERPRINTURL(state, b) {
            state.fingerprintUrl = b
        },
        UPDATE_FINGERPRINTID(state, b) {
            state.fingerprintId = b
        },
    },
    actions: {
        updateFmlUpgradeMsg({ commit }, b) {
            commit('UPDATE_FMLUPGRADEMSG', b)
        },
        updateIsMigrate({ commit }, b) {
            commit('UPDATE_ISMIGRATE', b)
        },
        updateCandoing({ commit }, b) {
            commit('UPDATE_CANDONG', b)
        },
        updateAgent({ commit }, b) {
            commit('UPDATE_AGENT', b)
        },
        updateSignInList({ commit }, b) {
            commit('UPDATE_SIGNINLIST', b)
        },
        updateFuturePrizeList({ commit }, b) {
            commit('UPDATE_FUTUREPRIZELIST', b)
        },
        updateCurrentdate({ commit }, b) {
            commit('UPDATE_CURRENTDATE', b)
        },
        updateRecentprizedate({ commit }, b) {
            commit('UPDATE_RECENTPRIZEDATE', b)
        },
        updateMisdnmask({ commit }, b) {
            commit('UPDATE_MISDNMASK', b)
        },
        updateIsSign({ commit }, b) {
            commit('UPDATE_ISSIGNIN', b)
        },
        updateSignCardNum({ commit }, b) {
            commit('UPDATE_SIGNCARDNUM', b)
        },
        updateActsigntaskregion({ commit }, b) {
            commit('UPDATE_ACTSIGNTASKREGION', b)
        },
        updateActsigntaskstatuslist({ commit }, b) {
            commit('UPDATE_ACTSIGNTASKSTATUSLIST', b)
        },
        updateTestTime({ commit }, b) {
            commit('UPDATE_TESTTIME', b)
        },
        updateIsCurrentMonthSubscribe({ commit }, b) {
            commit('UPDATE_ISCURRENTMONTHSUBSCRIBE', b)
        },
        updateFingerprintUrl({ commit }, b) {
            commit('UPDATE_FINGERPRINTURL', b)
        },
        updateFingerprintId({ commit }, b) {
            commit('UPDATE_FINGERPRINTID', b)
        },
    }
})