<!--
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON> zheng<PERSON><PERSON>@aspirecn.com
 * @Date: 2024-07-08 16:13:10
 * @LastEditors: zheng<PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2024-07-08 18:07:41
 * @FilePath: \multiple-project\src\projects\checkin-new\font-index.html
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<!doctype html>
<html lang='en'>
<head>
  <meta charset='UTF-8'>
  <meta name='viewport' content='width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0'>
  <meta http-equiv='X-UA-Compatible' content='ie=edge'>
  <title>Document</title>
  <style>
      @font-face {
          font-family: 'HYYAKUHEIW';
          src: url('./assets/fonts/HYYAKUHEIW.ttf')  format('truetype');
      }
      #app {
        font-family: 'HYYAKUHEIW';
      }
  </style>
</head>
<body>
<div id='app'>
    连续月签赢 6GB1234567890GBMB做任务得补签卡签到日历您有2张补签卡年月日
</div>
</body>
</html>