<template>
  <section class="checkin">
    <div class="checkin-top">
      {{ isSignIn ? "签到成功!" : "" }} 本月已累计签到
      <span>{{ totalTimes }}</span>
      天
      <!-- @todo 满签抽奖当天的状态 -->
      <div v-if="signFullStatus !== 2" class="checkin-top__prize index-icons--before" @click="goGetContinuousPrize">
        <div class="checkin-top__prize__content">
          <span>
            {{signFullStatus === 1 ? '点我抽奖' : '满签抽5GB哟'}}
          </span>
        </div>
      </div>
    </div>
    <div class="checkin-content" :class="{'have-recent': recentPrizeDate && recentPrizeDate.prizeObj}">
      <div
        v-for="(item, index) in indexList"
        :key="index"
        :class="['checkin-item',{ 'has-checkin': showSignInList[index], 'sign' :isSignIn}]"
      >
          <img :src="getImg(item, index)" alt="" class="checkin-item__img" />
          <div class="checkin-item__text">第{{ item }}天</div>
      </div>
      <!-- 再签到多少天可获取奖励 -->
      <div class="checkin-item checkin-item--big" v-if="isSignIn && recentPrize">
        <img :src="recentPrize.prizeObj.prizeImg" alt="" class="checkin-item__top" />
        <div>{{ recentPrize.difDays }}天后可领</div>
      </div>
      <recentPrize :recentPrizeDate="recentPrizeDate" />
    </div>
    <div v-if="signFullStatus === 1" :class="['checkin__btn index-icons animation', FullCheckinPopupImgurl ? 'full-tips':'']" @click="goGetContinuousPrize('P00000063297', '中国移动APP每日签到_主页_满签抽奖')">
      满签抽奖
    </div>
    <div v-else class="checkin__btn index-icons" @click="chickin()" :class="{animation:!isSignIn, 'full-tips': FullCheckinPopupImgurl}">
      {{ isSignIn ? "已签到" : "立即签到" }}
    </div>
    <continuous-prize-popup v-if="signFullPrize.length > 0" :show.sync="showContinuousPrizePopup" :prizelist="signFullPrize" @success="successGotContinuousPrize"/>
  </section>
</template>

<script>
import recentPrize from './recent-prize.vue'
import ContinuousPrizePopup from '../../../components/ContinuousPrizePopup'
// import { PRIZE_PARAMS, PRIZE_DAYS } from '@/projects/checkin-new/config/index'
import { dateFormat, getCurrentDays, isSameMonth } from '@/projects/checkin-new/utils/utils'
import jtWebtrends1 from '@/utils/jtWebtrends'
import { mapActions, mapState } from 'vuex'

export default {
  components: { ContinuousPrizePopup, recentPrize },
  props: {
    signInList: {
      type: Array,
      default() {
        return []
      }
    },
    isSignIn: {
      type: Boolean,
      default: false
    },
    currentDate: {
      type: Number,
      default: 0
    },
    signFullStatus: {
      type: Number,
      default: 0
    },
    FullCheckinPopupImgurl: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      showContinuousPrizePopup: false,
      totalTimes: 0,
      showSignInList: [],
      prizeParams: {},
      prizeDays: [], // 获奖的时间列表
      signFullPrize: [],
      indexList: [],
      recentPrize: {
        difDays: 0,
        prizeObj: {}
      }, // 下一次奖励的时间
      month: 4,
      isContinuous: false, // 本月是否连签
      prizeDates: [],
    }
  },
  computed: {
    ...mapState({
      futurePrizeList: state => state.futurePrizeList,
      recentPrizeDate: state => state.recentPrizeDate
    })
  },
  watch: {
    signInList: {
      handler(val) {
        this.updateData()
      },
      immediate: true,
      deep: true
    },
    isSignIn: {
      handler(val) {
        this.updateData()
      }
    }
  },
  mounted() {},
  methods: {
    ...mapActions(['updateFuturePrizeList', 'updateRecentprizedate']),
    // 签到
    chickin() {
      if (!this.isSignIn) {
        jtWebtrends1.multiTrack('P00000025013', '中国移动APP每日签到_立即签到')
      }
      this.$emit('checkin')
    },
    updateData() {
      this.signFullPrize = JSON.parse(sessionStorage['get' + 'Item']('signFullPrize') || '[]') || []
      this.prizeParams = JSON.parse(sessionStorage['get' + 'Item']('PRIZE_PARAMS') || '{}')
      this.prizeDays = JSON.parse(sessionStorage['get' + 'Item']('PRIZE_DAYS') || '[]')
      this.prizeDates = JSON.parse(sessionStorage['get' + 'Item']('PRIZE_DATES') || '[]')
      this.updateRecentprizedate(this.prizeDates.filter(item => (item.timestamp > this.currentDate) && !(this.isSignIn && item.difDays === 0))[0])
      const signInList = this.signInList
      const length = signInList.length || 0
      this.totalTimes = length
      this.indexList = [1, 2, 3]
      this.showSignInList = []
      const days = new Date(this.currentDate).getDate()
      // 已经签到的展示
      if (this.isSignIn) {
        this.getShowData(signInList, length, 4)
        this.isContinuous = days === length
      } else {
        // 未签到的
        this.getShowData(signInList, length, 4)
        this.isContinuous = days === length + 1
      }
      // 连续签到的话，最后一天有能量奖励
      if (this.isContinuous) {
        const _day = getCurrentDays(this.currentDate)
        this.prizeParams[_day] = {
          prizeName: '抽5GB',
          prizeDesc: '抽5GB',
          prizeImg: require('../../../assets/checkin/prize-mq.png'),
          type: 'mq'
        }
        // 连续签到的话最后一天可以获奖
        this.prizeDays.push(_day)
      }
      this.getRecentDay(length)
    },
    // 获取即将获得的全部奖品信息
    getRecentDay(length) {
      const futurePrizeList = []
      this.recentPrize = null
      // 签到次数
      this.prizeDays.forEach((element) => {
        if (element > length) {
          const difDays = this.isSignIn ? element - length : element - length - 1
          const _time = this.currentDate + difDays * 24 * 60 * 60 * 1000
          const sameMonth = isSameMonth(_time, this.currentDate)
          sameMonth && futurePrizeList.push({
            difDays,
            timestamp: _time,
            date: dateFormat(_time, 'yyyy-MM-dd'),
            prizeObj: this.prizeParams[element]
          })
        }
      })
      // 指定日期
      this.prizeDates.forEach(item => {
        item.times = -1
        const isDifDaysOverZero = this.isSignIn ? item.difDays > 0 : item.difDays >= 0
        if (isDifDaysOverZero && isSameMonth(item.timestamp, this.currentDate)) { // 同一个月并且在当前日期之后
          item.times = this.signInList.length + (this.isSignIn ? item.difDays : item.difDays + 1) // 计算是签到第几天
          // 如果重复的话，优先指定签到次数奖品展示优先
          if (futurePrizeList.filter(iItem => iItem.date === item.date).length === 0) {
            futurePrizeList.push(item)
          }
        }
      })
      // 日期展示的内容根据日期排序
      futurePrizeList.sort((a, b) => a.timestamp - b.timestamp)
      // 获取最近一次的有奖品的日期内容
      this.recentPrize = futurePrizeList.length > 0 ? futurePrizeList[0] : null
      if (!this.recentPrize && this.isSignIn) {
        // 已经签到的展示
        this.getShowData(this.signInList, length, 6, 'no')
      }
      this.updateFuturePrizeList(futurePrizeList || [])
    },
    /**
    获取展示数据
    signInList: 签到数据
    length: 签到数据的长度
    targetLength: 展示目标数据的长度
    isNoPrize: 是否没有奖品
    获取展示的数据
     */
    getShowData(signInList, length, targetLength, isNoPrize) {
      this.indexList = []
      const _day = getCurrentDays(this.currentDate)
      if (length > targetLength) {
      // 展示的奖品
        this.showSignInList = signInList.slice(length - targetLength, length)
        // 展示的最大日期
        const endIndex = isNoPrize ? targetLength : targetLength + 1
        for (let index = endIndex; index > 0; index--) {
        // 没有奖品展示最近6天的签到数据
          const dayLength = isNoPrize ? length - index + 1 : length - index + 2
          if (dayLength > _day) {
            break
          }
          this.indexList.push(dayLength)
        }
      } else {
        this.showSignInList = signInList
        const indexList = []
        // 展示的最大日期
        const endIndex = isNoPrize ? targetLength + 1 : targetLength + 2
        for (let index = 1; index < endIndex; index++) {
          indexList.push(index)
        }
        this.indexList = indexList
      }
    },
    // 获取图片
    getImg(item, index) {
      let prizeParams = this.prizeParams[item]
      if (!prizeParams && this.showSignInList[index]) { // 当天之前的日期都是已签到的，可以通过签到列表查找指定日期的奖品
        const tmpObj = this.prizeDates.filter(iItem => iItem.date === this.showSignInList[index].signInDate)[0]
        prizeParams = tmpObj && tmpObj.prizeObj
      }
      if (!prizeParams) { // 未签到的，属于未来的日期，通过指定日期的次数来查找
        const tmpObj = this.prizeDates.filter(iItem => iItem.times === item)[0]
        prizeParams = tmpObj && tmpObj.prizeObj
      }
      const signInParams = this.showSignInList[index]
      let img = ''
      if (this.isSignIn) {
        img = signInParams ? require('@/projects/checkin-new/assets/checkin/img-c.png') : require('@/projects/checkin-new/assets/checkin/img-not.png')
      } else {
        img = prizeParams && prizeParams.prizeImg ? prizeParams.prizeImg : require('@/projects/checkin-new/assets/checkin/img-c.png')
      }
      return img
    },
    // 获取满签的奖品内容
    goGetContinuousPrize(wm, envName) {
      if (this.signFullStatus === 1) {
        this.showContinuousPrizePopup = true
        jtWebtrends1.multiTrack(wm || 'P00000063295', envName || '中国移动APP每日签到_主页_满签抽5GB_点我抽奖')
      } else {
        this.$toast('签满整月才可以抽奖哦！')
        jtWebtrends1.multiTrack('P00000063294', '中国移动APP每日签到_主页_满签抽5GB')
      }
    },
    // 成功获取满签到奖励
    successGotContinuousPrize(prize) {
      this.$emit('update:signFullStatus', 2)
      if (prize.prizeType === 5) {
        this.thankPopup(prize)
      } else {
        this.otherPrizePopup(prize.subtitle || ('恭喜您获得' + prize.prizeName), prize, { event: 'P00000063296', envName: '中国移动APP每日签到_主页_满签抽5GB弹窗_查看奖励' })
      }
    }
  }
}
</script>

<style lang="scss" scoped>
@import "~@/projects/checkin-new/styles/mixin.scss";
.checkin {
  width: 100%;
  padding-top: 30px;
  // padding: 20px 20px 0;
  // width: 700px;
  // background-color: #ffffff;
  border-radius: 20px;
  margin: 0 auto;
  &-top {
    position: relative;
    @include flex(flex-start, center);
    font-family: PingFangSC-Semibold;
    font-size: 29px;
    font-weight: bold;
    font-stretch: normal;
    line-height: 46px;
    // letter-spacing: 2px;
    color: #5e3131;
    padding-bottom: 10px;
    span {
      color: #ff6553;
      // margin: 0 5px;
      &.checkin-top__icon {
        margin-right: 10px;
        display: inline-block;
        @include background(45px, 45px, "checkin/icon-checkin.gif");
      }
    }
    .checkin-top__prize {
      width: 210px;
      font-size: 24px;
      position: absolute;
      right: 0;
      padding: 0 0 0 45px;
      line-height: 50px;
      background-color: #fff2e4;
      border-radius: 18px 18px 18px 0px;
      border: solid 2px #ffe0c4;
      margin: 0;
      &__content {
        width: 100%;
        overflow: auto;
        span {
          white-space: nowrap;
          display: inline-block;
          font-weight: 500;
          // animation: leftToRight 3s linear infinite;
        }
      }
      &::before {
        content: '';
        display: inline-block;
        position: absolute;
        top: -5px;
        left: -41px;
        width: 82px;
        height: 58px;
        background-position: 0 -150px;
        // @include background(82px, 58px, "checkin/jqr.png");
      }
    }
  }
  &-content {
    width: 100%;
    height: 200px;
    background: #fff;
    margin: 10px auto 20px;
    @include flex(space-evenly, center);
    background-color: rgba(#fb7088, 0.05);
    box-shadow: inset 0px 0px 7px 1px rgba(#d93024, 0.05);
    border-radius: 8px;
    &.have-recent {
      height: 240px;
      padding-bottom: 40px;
      position: relative;
    }
  }
  &-item {
    width: 100px;
    height: 130px;
    text-align: center;
    border-radius: 12px;
    background-image: linear-gradient(0deg, rgba(#fc9243, 0.15) 0%, rgba(#ffb76b, 0.15) 100%);
    font-size: 20px;
    font-weight: normal;
    font-stretch: normal;
    letter-spacing: 0px;
    color: #f55a4c;
    &__img {
      width: 80px;
      height: 80px;
      margin: 6px auto 0;
      overflow: hidden;
      position: relative;
      z-index: 2;
    }

    &--big {
      margin: 0;
      width: 110px;
      height: 160px;
      background-image: linear-gradient(0deg,
        #fc9142 0%,
        #ffb86b 100%);
      border-radius: 12px;
      font-size: 20px;
      color: #ffffff;
      margin-right: 10px;
      img {
        width: 100px;
        height: 100px;
      }
    }
    &__text {
      line-height: 20px;
    }
    &__day {
      line-height: 48px;
      position: relative;
      color: #fc872f;
    }
  }
  .has-checkin {
    background-image: linear-gradient(0deg, #fc9142 0%, #ffb86b 100%);
    color: #ffffff;
    min-height: 48px;
    border-radius: 24px;
  }
  .sign {
      border-radius: 24px;
      background:none;
      color: #666666;
      position: relative;
      .checkin-item__img {
        width: 64px;
        height: 64px;
      }
      &::before {
        width: 100%;
        height: 3px;
        content: "";
        position: absolute;
        top: 30%;
        right: 80%;
        background-color: rgba(#fc9142, 0.14);
        display: inline-block;
      }
  }

  .has-checkin.sign {
     color: #f55a4c;
      &::before {
        width: 100%;
        height: 3px;
        content: "";
        position: absolute;
        top: 30%;
        right: 80%;
        background-color:#fc9142;
        display: inline-block;
      }
       &:nth-child(1) {
        &::before {
          content: "";
          width: 0px;
          height: 0px;
          position: absolute;
          top: 30%;
          right: 80%;
          background-color: rgba(#fc9142, 0.14);
          display: inline-block;
        }
      }
  }
  &__btn {
    line-height: 110px;
    font-family: SourceHanSansCN-Bold;
    font-size: 44px;
    font-weight: normal;
    font-stretch: normal;
    font-weight: bold;
    letter-spacing: 0px;
    color: #ffffff;
    background-position: 38px 0;
    margin: 0 auto;
  }
  .full-tips {
    position: relative;
    &::before {
      content: '';
      position: absolute;
      top: -60px;
      width: 236px;
      height: 68px;
      background: url('../../../assets/index/btn-tips.png') no-repeat center center;
      background-size: 100% 100%;
    }
  }
  .animation{
      animation: button 1s linear alternate infinite;
  }
}
@keyframes button {
    0% {
        transform: scale(1);
    }
    100% {
        transform: scale(0.8);
    }
}

@keyframes leftToRight {
    0% {
        transform: translateX(100%);
    }
    100% {
        transform: translateX(-100%);
    }
}
</style>
