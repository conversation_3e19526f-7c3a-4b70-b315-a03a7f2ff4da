<template>
  <div class="checkin-top">
    <div v-if="showTitle" class="checkin-top__title index-icons--before" @click="goCalendar">
      <span class="icon"></span><span class="text">签到日历<van-icon name="arrow" color="#5e3131" style="font-weight: bold;"/></span><span class="kbq">可补签</span>
    </div>
    <div v-if="showDy" class="checkin-top__qdtx" :class="{'no-dy': isCurrentMonthSubscribe}">
      <span class="open-icon" @click="openWXMi"></span>
      {{ isCurrentMonthSubscribe ? '已设提醒' : '签到提醒' }}
      <span class="refresh-icon index-icons" :class="{'show-atn': showAtn}" @click.stop="refreshSubscribeTemplateFn"></span>
    </div>
  </div>
</template>
<script>
import { mapState, mapActions } from 'vuex'
import jtWebtrends1 from '@/utils/jtWebtrends'
import { checkBjUser } from '@/projects/checkin-new/utils/utils'
import { querySubscribeTemplate } from '../../../api/index'
export default {
  props: {
    showTitle: {
      type: Boolean,
      default: true
    },
    showDy: {
      type: Boolean,
      default: true
    },
    haveToken: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      showAtn: 0
    }
  },
  computed: {
    ...mapState({
      misdnmask: state => state.misdnmask,
      isCurrentMonthSubscribe: state => state.isCurrentMonthSubscribe
    })
  },
  watch: {
    haveToken: {
      handler: function(newVar) {
        if(newVar) this.querySubscribeTemplateFn()
      },
      immediate: true
    }
  },
  destroyed() {
    clearTimeout(this.atnTimer)
  },
  methods: {
    ...mapActions(['updateIsCurrentMonthSubscribe']),
    // 刷新订阅状态
    refreshSubscribeTemplateFn() {
      if(this.showAtn) return
      this.setAtnFn(true)
      this.querySubscribeTemplateFn()
    },
    // 查询订阅情况
    querySubscribeTemplateFn() {
      querySubscribeTemplate({keyword: 'SignIn', templateId: "27xeGrW6935G7UEXwjYUNTWstsl6LV7v62gOY-_34N0" }).then(res => {
        if(res.result === 0 && res.info && res.info.length > 0) {
          let getDate = new Date(res.info[0].createTime)
          let nowDate = new Date()
          const isSameMonth = getDate.getFullYear() === nowDate.getFullYear() && getDate.getMonth() === nowDate.getMonth()
          this.updateIsCurrentMonthSubscribe(isSameMonth)
        }
      }).finally(() => {
        this.setAtnFn(false)
      })
    },
    // 设置刷新动效
    setAtnFn(status) {
      if(status) {
        this.showAtn = new Date().getTime()
      } else if(this.showAtn) {
        const leftTime = 1500 - ((new Date().getTime() - this.showAtn) % 1500)
        this.atnTimer = setTimeout(() => {
          this.showAtn = 0
        }, leftTime);
      }
    },
    // 跳转日历页
    goCalendar() {
      if (!checkBjUser()) return
      this.$router.push('/calendar')
    },
    // 唤醒微信小程序-订阅提醒
    openWXMi() {
      if(this.isCurrentMonthSubscribe) {
        this.$toast('本次订阅成功后不支持当月进行关闭')
        return
      }
      jtWebtrends1.multiTrack('P00000025016', '中国移动APP每日签到_订阅提醒')
      if (!checkBjUser()) return
      const path = `pagesAct/checkin-st/subscribe?from=app&&apptoken=${sessionStorage.getItem('userToken')}&misdnmask=${this.misdnmask}`
      this.launchwxmini({
        username: 'gh_4dd6b7e7ef37',
        url: path,
        path
      })
    },
  }
}
</script>
<style lang="scss" scoped>
.checkin-top {
  &__qdtx {
      position: absolute;
      top: 14px;
      right: 30px;
      // width: 188px;
      height: 62px;
      line-height: 62px;
      font-size: 24px;
      color: #ffffff;
      font-size: 26px;
      color: #333333;
      padding-left: 85px;
      padding-right: 30px;
      font-weight: bold;
      .open-icon {
        display: inline-block;
        position: absolute;
        left: 0;
        top: 0;
        width: 76px;
        height: 100%;
        &::after, &::before {
          content: '';
          position: absolute;
          border-radius: 18px;
          box-sizing: border-box;
        }
        &::before {
          left: 0;
          top: 12px;
          width: 76px;
          height: 38px;
          background: #ffc2ba;
          box-shadow: inset 0px 0px 12px 0px #ff929f;
        }
        &::after {
          left: 4px;
          top: 16px;
          width: 30px;
          height: 30px;
          background: #fff3f3;
          box-shadow: none;
          transition: all .5s;
        }
      }
      &.no-dy {
        .open-icon {
          &::after {
            left: 42px;
          }
          &::before {
            background-color: #f56250;
            box-shadow: inset 0px 0px 9px 0px rgba(185, 19, 39, 0.58);
          }
        }
      }
      .refresh-icon {
        position: absolute;
        right: 0;
        top: 20px;
        display: inline-block;
        width: 24px;
        height: 21px;
        background-position: -500px -229px;
        // background: url('../../../assets/common/refresh-icon.png') no-repeat center center / contain;
        &.show-atn {
          animation: atnAotate 1.5s linear infinite;
        }
      }
    }
    &__title {
      position: relative;
      width: fit-content;
      font-size: 30px;
      line-height: 62px;
      color: #5e3131;
      text-align: left;
      font-weight: bold;
      padding-top: 20px;
      padding-left: 68px;
      &::before {
        content: '';
        display: inline-block;
        position: absolute;
        left: 0;
        top: 18px;
        width: 65px;
        height: 62px;
        background-position: 0 -250px;
      }
      span {
        vertical-align: middle;
      }
      .kbq {
        position: relative;
        display: inline-block;
        width: 97px;
        height: 34px;
        background-image: linear-gradient(90deg,  #ff8f17 0%,  #f14947 100%);
        box-shadow: 0px 4px 10px 0px rgba(255, 178, 107, 0.54);
        border-radius: 4px;
        font-size: 20px;
        line-height: 34px;
        color: #ffffff;
        text-align: center;
        margin-left: 10px;
        z-index: 1;
        &::before {
          position: absolute;
          left: -8px;
          top: 42%;
          content: '';
          display: inline-block;
          width: 0;
          height: 0;
          border-radius: 2px;
          border: 8px solid #ff8f17;
          transform: rotate(45deg) translateY(-50%);
          z-index: -1;
        }
      }
      .text, .kbq {
        display: inline-block;
      }
    }
}
@keyframes atnAotate {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(-360deg);
  }
}
</style>
