<template>
  <div class="checkin__recent" :class="type" v-if="recentPrizeDate && recentPrizeDate.prizeObj">
    <van-icon name="volume" color="#f6685b"/>{{ recentPrizeDate.month }}月{{ recentPrizeDate.day }}日签到可额外获得<span>{{recentPrizeDate.prizeObj.prizeName}}</span>哦
  </div>
</template>
<script>
export default {
  props: {
    recentPrizeDate: {
      type: Object,
      default: () => {}
    },
    type: {
      type: String,
      default: ''
    }
  }
}
</script>
<style lang="scss" scoped>
.checkin__recent {
  width: 100%;
  text-align: center;
  margin-bottom: 10px;
  position: absolute;
  bottom: 5px;
  font-size: 28px;
  color: #666666;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  span {
    color: #ff6553;
  }
  .van-icon {
    margin-right: 10px;
  }
  &.rili {
    position: absolute;
    top: 33px;
    font-size: 25px;
    height: fit-content;
    left: 0;
    padding-left: 170px;
    padding-right: 56px;
    text-align: left;
  }
}
</style>
