<template>
  <div v-if="show" class="feedback">
    <p class="feedback-title">连续月签赢 <span>6GB</span></p>
    <p class="feedback-time">活动时间：{{ lxyqText.timeText }}</p>
    <prizeShow :prizeShowList="prizeShowList" />
    <div class="main">
      <checkinPrizeItem
        v-for="(item, index) in signStatusList"
        :item="item"
        :isOver="isOver"
        :key="index"
        :nowMonth="nowMonth"
        @customPrizeFeedback="customPrizeFeedback"
      />
    </div>
    <p class="feedback-bottom">请在<span>{{ lxyqText.endTimeText }}</span>领取奖品，过期未领视为放弃奖品</p>
    <popup-prize :params.sync="popupParams"></popup-prize>
  </div>
</template>

<script>
import checkinPrizeItem from './checkin-prize-item.vue'
import prizeShow from './prize-show.vue'
import popupPrize from '../../../components/AspDialog/index-two.vue'
import { getSignInFeedback, customPrizeFeedback } from '../../../api/feedback'
import { lxyqOverTime, getLxyqText } from '../../../config/rule'
import { mapState } from 'vuex'
export default {
  components: {
    popupPrize,
    prizeShow,
    checkinPrizeItem
  },
  props: {
    isSignIn: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      lxyqText: getLxyqText(new Date().getTime()),
      token: sessionStorage.getItem('userToken') || '',
      show: false,
      signStatusList: [{}, {}, {}, {}, {}], // 个月份签到情况
      nowMonth: new Date().getMonth() + 1, // 当前月份
      popupParams: { show: false },
      prizeShowList: {
        2: '1GB',
        4: '2GB',
        6: '3GB'
      },
      isOver: false,
      customPrizeFeedbacking: false
    }
  },
  watch: {
    // 监听用户签到，签到后重新获取基本信息
    isSignIn: {
      handler(val) {
        this.getSignInFeedback()
      }
    }
  },
  computed: {
    ...mapState({
      testTime: state => state.testTime
    })
  },
  mounted() {
    this.getSignInFeedback()
  },
  methods: {
    // 开奖
    // btnClick() {
    //   if (this.btnText === '立即签到') {
    //     console.log('立即签到')
    //     jtWebtrends1.multiTrack('P00000071222', '中国移动APP每日签到_连续月签_立即签到')
    //     this.$emit('checkin')
    //   } else if (this.btnText === '已领取全部奖励') {
    //     this.$toast('前往我的奖品查看')
    //     return false
    //   } else if (this.btnText === '立即领1GB' || this.btnText === '立即领2GB' || this.btnText === '立即领3GB') {
    //     if (this.btnText === '立即领1GB') {
    //       jtWebtrends1.multiTrack('P00000071221', '中国移动APP每日签到_连续月签_领1GB奖励')
    //     } else if (this.btnText === '立即领2GB') {
    //       jtWebtrends1.multiTrack('P00000071219', '中国移动APP每日签到_连续月签_领2GB奖励')
    //     } else if (this.btnText === '立即领3GB') {
    //       jtWebtrends1.multiTrack('P00000071218', '中国移动APP每日签到_连续月签_领3GB奖励')
    //     }
    //     console.log('开奖')
    //     this.customPrizeFeedback()
    //   }
    // },
    // 获取基本信息
    getSignInFeedback() {
      getSignInFeedback({ token: this.token })
        .then((res) => {
          const { result, list } = res
          const timestamp = this.testTime ? new Date().getTime() : res.timestamp
          // if(timestamp > new Date('2025/07/01 00:00:00').getTime() || timestamp < new Date('2025/12/31 23:59:59').getTime()) {
            if (Number(result) === 0) {
              this.nowMonth = new Date(timestamp).getMonth() + 1
              this.isOver = timestamp > lxyqOverTime
              this.lxyqText = getLxyqText(timestamp)
              if(!this.isOver) {
                let tmp = []
                this.signStatusList = list.sort((a, b) => a.month - b.month).map(item => {
                  const beforeNowMonth = item.month >= this.nowMonth // 当年本月以及本月之后的月份
                  tmp.push(beforeNowMonth ? 1 : item.signStatus) // 月份签到情况列表-含预签到
                  if(!item.signPrize && beforeNowMonth) {// 只有当月还没有待领取奖励和之后的月份才需要计算预奖励
                    const preSignNum = this.findLXMonthNum(tmp)
                    item.preSignPrize = this.prizeShowList[preSignNum]
                  }
                  return item
                })
              } else {// 过期直接按照接口展示
                this.signStatusList = list
              }
              this.show = true
            // } else {
            //   this.show = false
            // }
          }
        })
        .catch((err) => {
          console.log('连续月签接口报错', err)
          this.show = false
        }).finally(() => {
          this.$emit('showLXYQRule', this.show)
        })
    },
    // 查询连续月签信息
    findLXMonthNum(list) {
      let l = JSON.parse(JSON.stringify(list))
      let len = 0
      let isdq = false
      l.reverse()
      l.forEach(item => {
        if(item === 1 && !isdq) {
          len = len + 1
        } else {
          isdq = true
        }
      })
      return len
    },
    // 开奖接口
    customPrizeFeedback(item) {
      if(this.customPrizeFeedbacking) return
      this.customPrizeFeedbacking = true
      customPrizeFeedback({ token: this.token, month: item.month })
        .then((res) => {
          const { result,  prizelist = [], errmsg } = res
          if (Number(result) === 0 && prizelist && prizelist.length > 0) {
            this.popupParams = {
              show: true,
              icon: 'liwu',
              title: '恭喜您',
              imgurl: prizelist[0].imgurl,
              content: '获得' + prizelist[0].prizeDesc.replace('流量', '') + '流量奖励',
              desc: '奖励已为您放入我的奖品啦～'
            }
            this.signStatusList = this.signStatusList.map(iItem => {
              if(iItem.month === item.month) iItem.prizeStatus = 1
              return iItem
            })
            this.getSignInFeedback()
          } else {
            this.$toast(errmsg)
          }
        })
        .catch(() => { console.log('出奖接口报错') })
        .finally(() => {
          this.customPrizeFeedbacking = false
        })
    }
  }
}
</script>

<style lang="scss" scoped>
@import '~@/projects/checkin-new/styles/mixin.scss';
.feedback {
  position: relative;
  margin: 30px auto auto;
  width: 698px;
	height: 733px;
	background: linear-gradient(-45deg, #fef1dc, #ffffff, #ffefee);
	box-shadow: 0px 0px 10px 0px  #febcbd;
	border-radius: 34px;
  padding: 30px 25px;
  overflow: hidden;
  &-title {
    font-size: 40px;
    line-height: 50px;
    letter-spacing: 2px;
    color: #5e3131;
    font-family: 'HYYAKUHEIW';
    font-weight: bold;
    span {
      font-family: 'HYYAKUHEIW';
      font-weight: bold;
      padding: 8px;
      background-color: #ffcc50;
      border-radius: 10px;
      color: #f14948;
      vertical-align:middle;
    }
  }
  &-time {
    font-size: 24px;
    line-height: 36px;
    color: #666666;
    margin-top: 14px;
  }
  .main {
    position: relative;
    display: flex;
    justify-content: space-around;
    margin: 36px 0 0;
    z-index: 1;
    &::after {
      content: '';
      display: inline-block;
      position: absolute;
      bottom: 40px;
      left: 62px;
      width: 533px;
      height: 8px;
      background-color: #ffcfce;
      border-radius: 3px;
      z-index: -1;
    }
  }
  .btn {
    width: 520px;
    height: 84px;
    line-height: 84px;
    background-image: linear-gradient(0deg, #ff5049 0%, #ff826c 100%);
    border-radius: 42px;
    font-size: 40px;
    color: #fffaad;
    text-align: center;
    font-weight: 500;
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
    bottom: 120px;
  }
  .tips {
    position: absolute;
    width: 100%;
    left: 50%;
    bottom: 30px;
    transform: translateX(-50%);
    font-size: 24px;
    line-height: 36px;
    color: #999999;
  }
  &-bottom {
    position: absolute;
    left: 0;
    bottom: 0;
    width: 698px;
    height: 54px;
    line-height: 54px;
    text-align: center;
    background-color: #ffe4e9;
    border-radius: 0 0 14px 14px;
    font-size: 24px;
    color: #666666;
    span {
      color: #f95d66;
    }
  }
}
@keyframes scaleAtn {
  0% {
    transform: scale(1.1);
  }
  100% {
    right: scale(1);
  }
}
</style>
