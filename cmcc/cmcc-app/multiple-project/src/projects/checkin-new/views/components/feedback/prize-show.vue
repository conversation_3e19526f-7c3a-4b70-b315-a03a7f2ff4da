<!--
 * @Author: z<PERSON><PERSON><PERSON><PERSON> zhen<PERSON><PERSON><PERSON>@aspirecn.com
 * @Date: 2024-07-15 15:08:38
 * @LastEditors: zhengwen<PERSON> <EMAIL>
 * @LastEditTime: 2024-11-15 14:39:58
 * @FilePath: \multiple-project\src\projects\checkin-new\views\components\feedback\prize-show.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <ul class="list">
    <li class="list-item" v-for="item in Object.keys(prizeShowList)" :key="item">
      <div class="list-item__img feed-icons">{{ prizeShowList[item].replace('GB', '') }}<span>GB</span></div>
      <p class="list-item__tip">连续签到<br />满<span>{{ item }}个月</span>可获得</p>
      <div class="list-item__dot"><span></span></div>
    </li>
  </ul>
</template>
<script>
export default {
  props: {
    prizeShowList: {
      type: Object,
      default: () => {}
    }
  }
}
</script>
<style lang="scss" scoped>
.list {
  margin: 20px 0 0;
  width: 648px;
	height: 261px;
	background-color: #fff4f6;
	box-shadow: inset 0px 0px 10px 0px rgba(217, 48, 36, 0.06);
	border-radius: 24px;
  padding-top: 26px;
  display: flex;
  justify-content: space-between;
  &-item {
    position: relative;
    width: 30%;
    text-align: center;
    &:nth-last-child(1) {
      .list-item__dot {
        display: none;
      }
    }
    &__img {
      width: 140px;
      height: 134px;
      line-height: 98px;
      // background: url(./img/prize-item-bg.png) no-repeat center center;
      // background-size: 100% 100%;
      background-position: -100px -100px;
      font-size: 48px;
      color: #f14948;
      font-weight: bold;
      margin: auto;
      font-family: 'HYYAKUHEIW';
      font-weight: bold;
      span {
        font-size: 18px;
      }
    }
    &__tip {
      font-size: 24px;
      line-height: 30px;
      letter-spacing: 0px;
      color: #333333;
      font-weight: bold;
      text-align: center;
      margin-top: 15px;
      span {
        color: #f14948;
      }
    }
    &__dot {
      position: absolute;
      right: -22%;
      top: 50px;
      &::before, span, &::after {
        display: inline-block;
        vertical-align: middle;
        border-radius: 50%;
        margin-right: 8px;
      }
      &::before {
        content: "";
        width: 8px;
        height: 8px;
        background-color: #ff9089;
      }
      span {
        width: 8px;
        height: 8px;
        background-color: #fdaba6;
      }
      &::after {
        content: "";
        width: 8px;
        height: 8px;
        background-color: #ffccc9;
      }
    }
  }
}
</style>
