<template>
  <my-popop :showPopup="showPopup" @closePopup="closePopup" title="签到翻牌子" class="popup-card">
    <div class="popup-card-content">
      <!-- 翻牌子 -->
      <turn-card
        :initImgList="prizeImgs"
        :back-list="backImgs"
        :index-bg="indexBg"
        :prize-img.sync="prizeImg"
        :show="showPopup"
        @doPrize="toDoPrize"
        @hadRotate="toshowDialog"
      ></turn-card>
    </div>
  </my-popop>
</template>

<script>
import turnCard from '@/projects/checkin-new/components/TurnCard'
import { mapActions } from 'vuex'
import { PRIZEIMGS, BACKIMGS } from './turn-card'
import { Popup } from 'vant'
import MyPopop from './popup.vue'
import JtWebtrends1 from '@/utils/jtWebtrends'

export default {
  components: {
    turnCard,
    MyPopop,
    [Popup.name]: Popup
  },
  props: {
    prizeImg: {
      type: String,
      default: ''
    },
    visible: {
      type: Boolean,
      default: false
    }
  },

  data() {
    return {
      showPopup: false,
      prizeImgs: PRIZEIMGS,
      backImgs: BACKIMGS,
      indexBg: ''
    }
  },
  watch: {
    prizeImg(newValue) {
      this.prizeImg = newValue
    },
    showPopup(newValue) {
      this.$emit('update:visible', newValue)
    }
  },
  mounted() {},
  methods: {
    ...mapActions(['updateCandoing', 'updateAgent']),
    toDoPrize(index) {
      this.$emit('getPrize', index)
    },
    toshowDialog() {
      JtWebtrends1.multiTrack('P00000025019', '中国移动APP每日签到_翻牌子弹窗_点击牌子')
    },
    // 点击翻牌子
    showPupup() {
      this.updateCandoing(false)
      this.updateAgent(true)
      this.showPopup = true
      this.$emit('update:visible', true)
    },
    closePopup() {
      this.showPopup = false
      JtWebtrends1.multiTrack('P00000025020', '中国移动APP每日签到_翻牌子弹窗_关闭')
      this.$emit('update:visible', false)
    }
  }
}
</script>

<style lang="scss" scoped>
.popup-card {
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 14;
  font-size: 32px;
  font-weight: bold;
  font-stretch: normal;
  line-height: 36px;
  letter-spacing: 0px;
  color: #000000;
  /deep/ .my-popup-main {
    width: 100%;
    background-color: #fffae0;
    border-radius: 24px 24px 0px 0px;
  }

  &-content {
    width: 100%;
    height: 100%;
  }
}
</style>
