<template>
  <div>
    <section v-if="tjlb.tjlbStatus !== 0" class="sky-draw">
      <!-- <p class="sky-draw__title">天降礼包</p> -->
      <ul class="sky-draw__main">
        <!-- <li class="main-icon">
          {{ tjlbPrize.tjlb2.prizeName }}
        </li> -->
        <li class="main-desc">{{ mainBtnText.title }}</li>
        <li class="main-btn" :class="'main-btn--' + tjlb.tjlbStatus" @click="mainBtn">
          {{ mainBtnText.btn }}
        </li>
      </ul>
    </section>
    <!-- 天降礼包首次进入的弹窗 -->
    <section v-if="tjlb.isPop" class="sky-draw-popup">
      <div class="sky-draw-popup__contain">
        <!-- <p class="title">
          天降礼包
        </p> -->
        <div class="content">
          <div class="tjlb-img-1" :class="{'got': timesGot}"></div>
          <div class="tjlb-img-2"></div>
        </div>
        <button v-if="timesGot" class="btn" @click="close('P00000063291', '中国移动APP每日签到_天降礼包弹窗_下月再来')">
          下月再来
        </button>
        <button v-else class="btn" @click="customPrize('tjlb1')">
          签到领取
        </button>
        <p v-if="!timesGot" class="tips">已领取的奖励请前往我的奖品查看</p>
        <div class="close" @click="close">
          ×
        </div>
      </div>
    </section>
  </div>
</template>

<script>
import jtWebtrends1 from '@/utils/jtWebtrends'
import { customPrize } from '../../../api/index'
import { openMiniProgram } from '../../../../../../../../../bjapp-model/vue2/js/jt-app-ability'
import JTFK from '../../../utils/JTFK'
import { mapState } from 'vuex'
export default {
  props: {
    skyDrawUser: {
      type: Object,
      defaultl: () => {}
    },
    isSignIn: {
      type: Boolean,
      default: false
    },
    tjlb: {
      type: Object,
      default: () => {}
    },
    tjlbPrize: {
      type: Object,
      default: () => {}
    },
    misdnmask: {
      type: [String, Number],
      default: ''
    }
  },
  data() {
    return {
      timesGot: false,
      checkinPrize: null,
      nowTimeStamp: new Date().getTime()
    }
  },
  computed: {
    mainBtnText() {
      switch (this.tjlb.tjlbStatus) {
        case 3:
          return {
            title: '签到即可领取',
            btn: '已领取'
          }
        case 2:
          return {
            title: '签到即可领取',
            btn: '领取'
          }
        default:
          return {
            title: '下月签到即可领取！',
            btn: ''
          }
      }
    },
    ...mapState({
      fingerprintUrl: state => state.fingerprintUrl,
      fingerprintId: state => state.fingerprintId,
    })
  },
  mounted() {},
  methods: {
    // 主按钮出奖
    mainBtn() {
      if (this.tjlb.tjlbStatus === 3) return
      if (this.tjlb.tjlbStatus === 2) { // 天降礼包2的出奖
        if (!this.isSignIn) {
          this.$toast('今天还没签到哦！')
        } else {
          this.customPrize('tjlb2')
        }
      } else if (this.tjlb.isSendSubscribe) { // 订阅提醒
        openMiniProgram({
          path: `pagesAct/checkin-st/tjlb?type=subscribe&from=app&apptoken=${sessionStorage.getItem('userToken')}&misdnmask=${this.misdnmask}`
        })
        jtWebtrends1.multiTrack('P00000063292', '中国移动APP每日签到_主页_天降礼包_订阅提醒')
      } else {
        this.$toast('您已订阅过了哟！')
        jtWebtrends1.multiTrack('P00000063292', '中国移动APP每日签到_主页_天降礼包_订阅提醒')
      }
    },
    // 出奖
    async customPrize(type) {
      this.$loading.waitShow()
      let constid = ''
      if(this.fingerprintUrl && this.fingerprintId) {
        constid = await JTFK({ appId: this.fingerprintId, server: this.fingerprintUrl })
      }
      customPrize({ type: type, constid }).then(res => {
        const { result, prizelist, errmsg, timestamp } = res
        this.nowTimeStamp = timestamp
        if (result === 0 && prizelist && prizelist.length > 0) {
          if (type === 'tjlb1') {
            // 天降礼包1的出奖弹窗
            this.timesGot = true
            this.checkinPrize = prizelist.slice(1)
          } else {
            // 天降礼包2的出奖弹窗
            this.otherPrizePopup(prizelist[0].subtitle || ('恭喜您获得天降礼包奖励' + prizelist[0].prizeName), prizelist[0])
            this.$emit('update:tjlb', {
              ...this.tjlb,
              tjlbStatus: 3,
              isPop: false
            })
          }
        } else {
          this.showErrToast(errmsg)
        }
      }).finally(() => {
        this.$loading.waitClose()
      })
      if (type === 'tjlb1') {
        jtWebtrends1.multiTrack('P00000063290', '中国移动APP每日签到_天降礼包弹窗_签到领取')
      } else {
        jtWebtrends1.multiTrack('P00000063293', '中国移动APP每日签到_主页_天降礼包_领取')
      }
    },
    // 展示错误信息
    showErrToast(errmsg) {
      this.$toast(errmsg || '活动太火爆了，清稍后再试~')
    },
    // 关闭天降礼包1弹窗
    close(wm, envName) {
      sessionStorage['set' + 'Item']('isTjlbPop', true)
      if (wm && envName) {
        jtWebtrends1.multiTrack(wm, envName)
      }
      if (this.timesGot) { // 天降礼包1的出奖弹窗关闭
        this.timesGot = false
        this.$emit('update:tjlb', { ...this.tjlb, tjlbStatus: 1, isPop: false })
        this.$emit('queryTjlbInfo')
        if (this.checkinPrize.length > 0) { // 有签到奖励
          this.$emit('showCheckinPrize', this.checkinPrize, this.nowTimeStamp)
        }
        return
      }
      // 普通弹窗的关闭
      this.$emit('update:tjlb', { ...this.tjlb, isPop: false })
    }
  }
}
</script>

<style lang="scss" scoped>
.sky-draw {
  width: 660px;
  height: 165px;
  background: url('../../../assets/checkin/tjlb-banner.png') no-repeat center center;
  background-size: 100% 100%;
  margin: auto auto;
  transform: translateX(-15px);
  &__title {
    text-align: left;
  }
  &__main {
    // display: flex;
    // align-items: center;
    width: 100%;
    height: 100%;
    padding-top: 108px;
    padding-left: 50px;
    position: relative;
    .main-desc {
      font-size: 30px;
      line-height: 36px;
      color: #ffffff;
    }
    .main-btn {
      position: absolute;
      width: 106px;
      height: 107px;
      line-height: 91px;
      background: url('../../../assets/checkin/tjlb-btn.png') no-repeat center center;
      background-size: 100% 100%;
      top: 29%;
      right: 2.5%;
      border-radius: 50%;
      font-size: 31px;
      color: #f25049;
      font-style: italic;
      box-shadow: inset 1px 2px 2px 0px rgba(217, 107, 0, 0.35);
      padding-right: 8px;
      &.main-btn--3 {
        font-size: 27px;
      }
      &.main-btn--2 {
        animation: button 1s linear alternate infinite;
      }
      &.main-btn--1 {
        background: url('../../../assets/checkin/tjlb-dy.gif') no-repeat center center;
        background-size: 100% 100%;
        width: 120px;
        height: 109px;
        top: 27%;
      }
    }
  }
}
.sky-draw-popup {
  position: fixed;
  left: 0;
  top: 0;
  width: 100vw;
  height: 100vh;
  background: rgba(0,0,0,.5);
  z-index: 100;
  &__contain {
    width: 652px;
    height: 798px;
    background: url('../../../assets/checkin/tjlb.png') no-repeat center center;
    background-size: 100% 100%;
    position: absolute;
    left: 50%;
    top: 45%;
    transform: translate(-50%, -50%);
    padding-top: 47%;
    .content {
      .tjlb-img-1 {
        margin-right: 40px;
      }
      .tjlb-img-1, .tjlb-img-2 {
        display: inline-block;
        width: 213px;
        height: 258px;
        background: url('../../../assets/checkin/tjlb-1.png') no-repeat center center;
        background-size: 100% 100%;
        &.got {
          position: relative;
          // margin-top: 25px;
          &::before {
            content: '';
            position: absolute;
            bottom: 10px;
            left: 0;
            display: inline-block;
            width: 212px;
            height: 163px;
            background: url('../../../assets/checkin/tjlb-got.png') no-repeat center center;
            background-size: 100% 100%;
          }
        }
      }
      .tjlb-img-2 {
        background: url('../../../assets/checkin/tjlb-2.png') no-repeat center center;
        background-size: 100% 100%;
      }
    }
    .btn {
      width: 520px;
      height: 84px;
      background-image: linear-gradient(0deg,
        #ff5049 0%,
        #ff826c 100%);
      box-shadow: 0px 2px 5px 0px
        rgba(148, 15, 6, 0.54);
      border-radius: 42px;
      margin: auto;
      border: none;
      font-size: 40px;
      color: #fffaad;
      margin-top: 10px;
      &.got {
        font-size: 40px;
        color: #989898;
        font-weight: bold;
        background-image: linear-gradient(0deg, #c9c9c9 0%, #c9c9c9 0%, #ebebeb 100%);
        box-shadow: none;
        margin-top: 40px;
      }
    }
    .tips {
      font-size: 28px;
      color: #999999;
      margin-top: 20px;
    }
    .close {
      position: absolute;
      left: 50%;
      bottom: -90px;
      transform: translateX(-50%);
      width: 60px;
      height: 60px;
      border: 1px solid #ffffff;
      font-size: 50px;
      line-height: 60px;
      text-align: center;
      border-radius: 50%;
      color: #ffffff;
    }
  }
}
@keyframes button {
  0% {
    transform: scale(1);
  }
  100% {
    transform: scale(0.8);
  }
}
</style>
