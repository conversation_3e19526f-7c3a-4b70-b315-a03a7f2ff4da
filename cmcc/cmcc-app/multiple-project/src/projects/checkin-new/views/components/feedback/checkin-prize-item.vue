<template>
  <div class="main-item">
    <div class="main-item__prize feed-icons" :class="[{'before': (item.month > nowMonth) && !isOver || item.preSignPrize || !item.signStatus}, {'have-prize': item.signPrize || item.preSignPrize}]">
      <p>{{ item.month }}月</p>
      <span>{{ item.signPrize || item.preSignPrize }}</span>
    </div>
    <div v-if="item.signPrize && item.prizeStatus === 1" class="main-item__status gray" @click="showToast('您已经领取奖励了哦')">已领取</div>
    <div v-else-if="item.signPrize && item.prizeStatus === 0" class="main-item__status" @click="doPrize">去领取</div>
    <div v-else-if="item.signStatus === 1" class="main-item__dd feed-icons"></div>
    <div v-else-if="(item.month >= nowMonth) && !isOver" class="main-item__dd gray feed-icons"></div>
    <div v-else class="main-item__cw feed-icons" @click="showToast('您在此月份未签到哦')"></div>
  </div>
</template>
<script>
import jtWebtrends1 from '@/utils/jtWebtrends'
export default {
  props: {
    item: {
      type: Object,
      default: () => {}
    },
    isOver: {
      type: Boolean,
      default: false
    },
    nowMonth: {
      type: Number,
      default: new Date().getTime()
    }
  },
  methods: {
    doPrize() {
      this.$emit('customPrizeFeedback', this.item)
      if(this.item.signPrize === '1GB') {
        jtWebtrends1.multiTrack('P00000071221', '中国移动APP每日签到_连续月签_领1GB奖励')
      } else if(this.item.signPrize === '2GB') {
        jtWebtrends1.multiTrack('P00000071219', '中国移动APP每日签到_连续月签_领2GB奖励')
      } else if(this.item.signPrize === '3GB') {
        jtWebtrends1.multiTrack('P00000071218', '中国移动APP每日签到_连续月签_领3GB奖励')
      }
    },
    showToast(msg) {
      this.$toast(msg)
    }
  }
}
</script>
<style lang="scss" scoped>
.main {
  display: flex;
  justify-content: space-evenly;
  margin: 36px 0 0;
  &-item {
    &__prize {
      position: relative;
      width: 79px;
      height: 99px;
      // background: url('./img/month-bg.png') no-repeat center center;
      // background-size: 100% 100%;
      background-position: 0 0;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      margin-bottom: 20px;
      &::after {
        content: '';
        display: inline-block;
        width: 6px;
        height: 6px;
        background-color: #fbb3a4;
        border-radius: 50%;
        position: absolute;
        left: 50%;
        transform: translateX(-50%);
        bottom: -15px;
      }
      p {
        font-size: 20px;
        color: #ffffff;
        line-height: 40px;
      }
      span {
        font-size: 24px;
        line-height: 56px;
        color: #ffffff;
        margin-top: 5px;
      }
      &.have-prize {
        // background: url('./img/month-active-bg.png') no-repeat center center;
        // background-size: 100% 100%;
        background-position: -300px 0;
      }
      &.before {
        opacity: .5;
      }
    }
    &__dd {
      width: 80px;
      height: 80px;
      // background: url('./img/icon-dd.png') no-repeat center center;
      // background-size: 100% 100%;
      background-position: 0 -100px;
      &.gray {
        // background: url('./img/icon-dd-gray.png') no-repeat center center;
        // background-size: 100% 100%;
        background-position: -100px 0;
      }
    }
    &__cw {
      width: 80px;
      height: 80px;
      // background: url('./img/icon-cw.png') no-repeat center center;
      // background-size: 100% 100%;
      background-position: -200px 0;
    }
    &__status {
      padding: 10px 7px;
      font-size: 24px;
      line-height: 25px;
      color: #ffffff;
      border-radius: 9px;
      margin:  34px auto auto;
      background-image: linear-gradient(90deg, #ff455f 0%, #ff8f5f 100%),  linear-gradient(#f06247, #f06247);
      &.gray {
        background-image: linear-gradient(#ffd2d6, #ffd2d6), linear-gradient(#f06247, #f06247);
      }
    }
  }
}
</style>
