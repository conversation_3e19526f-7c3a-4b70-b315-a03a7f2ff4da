<template>
  <section class="turn-card" @click="turnCard()">
    <div class="turn-card__head index-icons"></div>
    <div class="turn-card__text" v-if="count">
      您有
      <span>{{ count }}</span>
      次翻牌机会
    </div>
    <div class="turn-card-content">
      <div class="turn-card__cover"></div>
    </div>
    <v-turn-card ref="turnCard" :prizeImg="prizeImg" @getPrize="getPrize" :visible.sync="showFpz"></v-turn-card>
    <popup-prize
      :showPopup.sync="showPopup"
      :popupType="popupType"
      :usedCount="usedCount"
      :visitStatus="visitStatus"
      :currentDate="currentDate"
      :browseTask="browseTask"
      :prizeParams="prizeParams"
      @closePopup="closePopup"
      @showUpgrade="showUpgrade"
      @doTask="doTask"
      @changeShowGradePopup="changeShowGradePopup"
    >
    </popup-prize>
  </section>
</template>

<script>
import { doPrizeFpz, saveVisit } from '@/projects/checkin-new/api/index'
import vTurnCard from './popup-card.vue'
import popupPrize from './components/popup-prize.vue'
import { SUCCESS_CODE } from '@/projects/checkin-new/config/index'
import { PRIZEIMGS } from './turn-card'
import { checkBjUser } from '@/projects/checkin-new/utils/utils'
import jtWebtrends1 from '@/utils/jtWebtrends'

export default {
  components: {
    vTurnCard,
    popupPrize
  },
  props: {
    browseTask: {
      type: Object,
      default() {
        return {}
      }
    },
    isSignIn: {
      type: Boolean,
      default: false
    },
    // 当天浏览任务状态
    visitStatus: {
      type: Boolean,
      default: false
    },
    // 今日已用翻牌次数
    usedCount: {
      type: Number,
      default: 0
    },
    // 今日可用翻牌次数
    count: {
      type: Number,
      default: 0
    },
    // 分享次数
    shareNum: {
      type: Number,
      default: 0
    },
    // 时间
    currentDate: {
      type: Number,
      default: 0
    }
  },
  data() {
    return {
      userToken: sessionStorage.getItem('userToken'),
      showFpz: false, // 翻牌子
      prizeImg: '',
      popupType: '',
      showPopup: false, // 翻牌子的弹窗
      prizeParams: {} // 翻牌子奖励
    }
  },
  watch: {},
  created() {
    sessionStorage.removeItem('shareSponsor')
  },
  mounted() {},
  methods: {
    // 获取即将获得的全部奖品信息
    turnCard() {
      jtWebtrends1.multiTrack('P00000025018', '中国移动APP每日签到_签到翻牌子_签到翻牌赢1GB')
      if (!checkBjUser()) return
      if (!this.isSignIn) {
        // 未签到
        this.popupType = 'noCheckin'
        this.showPopup = true
      } else if (this.usedCount < 3 && !this.count) {
        // 没有翻牌机会弹窗
        this.popupType = 'noChange'
        this.showPopup = true
      } else if (this.usedCount === 3) {
        // 每日三次翻牌机会用完-没有翻牌机会弹窗
        this.popupType = 'allNoChange'
        this.showPopup = true
      } else {
        this.showFpz = true
        this.prizeImg = ''
        this.$refs.turnCard.showPupup() // 弹出翻转牌子的界面
      }
    },
    // type 关闭类型
    closePopup() {
      this.$refs.turnCard.closePopup()
    },
    // 翻牌子奖励
    getPrize() {
      if (this.$loading.show()) return
      doPrizeFpz({ token: this.userToken })
        .then((res) => {
          const { timestamp, result } = res
          if (this.getResult(result)) {
            this.$emit('doPrizeFpz', timestamp)
            const prizelist = res.prizelist
            if (prizelist && prizelist.length && String(prizelist[0].prizeType) !== '5') {
              PRIZEIMGS.forEach(item => {
                if (item.prizeName === prizelist[0].prizeName) {
                  this.prizeImg = item.img
                }
              })
              this.popupType = 'winPrize'
              this.showPopup = true
              this.prizeParams = prizelist[0]
            } else {
              this.popupType = 'noWinPrize'
              this.prizeImg = require('@/projects/checkin-new/assets/turn-card/pai8.png')
              this.showPopup = true
            }
          }
        })
        .catch((err) => {
          this.getResult(err.result)
        })
        .finally(() => {
          this.$loading.hide()
        })
    },
    changeShowGradePopup() {
      this.$emit('changeShowGradePopup')
    },
    // 完成浏览任务
    doTask() {
      saveVisit({ token: this.userToken })
        .then((res) => {
          // console.log(res)
          const { result } = res
          if (result === SUCCESS_CODE) {
            this.$emit('doTaskSuccess')
            const taskUrl = this.browseTask.block[0].url
            // ios返回不刷新，需要强制刷新，跳转任务之后设置强刷标志
            sessionStorage['set' + 'Item']('isReload', true)
            location.href = taskUrl
          } else if (result === -206) {
            this.$toast('今日已翻牌三次，请明天再来吧！')
          }
        })
        .catch(() => {})
        .finally(() => {
          this.closePopup()
        })
    },
    getResult(result) {
      let flag = false
      switch (String(result)) {
        case '0':
          flag = true
          break
        case '-109':
          // 当天尚未签到
          this.popupType = 'noCheckin'
          this.showPopup = true
          break
        case '-100':
        case '-101':
        case '-103':
          this.$toast('抱歉，活动已结束哦~')
          break
        case '-102':
          this.$toast('抱歉，活动未开始哦~')
          break
        case '-104': // 无库存展示未中奖弹窗
          flag = true
          break
        case '-110':
          // 暂无可用翻牌次数
          this.popupType = 'noChange'
          this.showPopup = true
          break
        case '-111':
          // 当天已超三次翻牌限制
          this.popupType = 'noChange'
          this.showPopup = true
          break
        default:
          this.$toast('活动太火爆了，请稍后再试哦~')
          break
      }
      return flag
    },
    // 展示升级弹窗
    showUpgrade() {
      this.$emit('showUpgrade')
    }
  }
}
</script>

<style lang="scss" scoped>
@import "~@/projects/checkin-new/styles/mixin.scss";
.turn-card {
  width: 700px;
  background-blend-mode: normal, normal;
  margin: 0 auto;
  padding-bottom: 20px;
  position: relative;
  background-color: #ffffff;
  border-radius: 24px;
  &__head {
    width: 280px;
    height: 56px;
    background-position: 0 -500px;
    // @include background(280px, 56px, "turn-card/title-red.png");
    margin: 10px auto 20px;
  }
  &__text {
    margin: 0 auto;
    font-family: SourceHanSansCN-Regular;
    font-size: 26px;
    font-weight: normal;
    font-stretch: normal;
    line-height: 36px;
    letter-spacing: 0px;
    position: relative;
    margin-bottom: 15px;
    color: #000;
    span {
      color: #f2554a;
      font-weight: bold;
      font-size: 30px;
      display: inline-block;
      animation: roundRule 1s linear alternate infinite;
    }
  }
  &-content {
    width: 100%;
  }
  &__cover {
    margin: 0px auto;
    @include background(666px, 276px, "turn-card/banner.png");
  }
}
@keyframes roundRule {
  0% {
    transform:  rotate(20deg)
  }
  20% {
    transform:  rotate(-20deg)
  }
  40% {
    transform:  rotate(20deg)
  }
  60% {
    transform:  rotate(-20deg)
  }
  80% {
    transform:  rotate(0deg)
  }
  100% {
    transform: rotate(0deg)
  }
}

</style>
