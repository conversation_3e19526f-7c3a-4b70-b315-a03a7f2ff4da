<template>
  <div v-if="showPopup" class="common-popup" @touchmove.prevent>
    <section class="common-popup-main">
      <div class="close" @click.stop="hide" />
      <div class="common-popup-top">
        <div class="common-popup__dec"></div>
        <div class="common-popup__title">{{ popupParams.title }}</div>
        <div class="common-popup-content" :class="popupParams.styleClass">
          <div
            :style="popupParams.dialogStyle ? popupParams.dialogStyle : ''"
            v-html="popupParams.content"
          />
          <div class="common-popup-prize" v-if="popupParams.isPrize">
            <p class="prize__text">
              获得翻牌奖励
              <span>{{ prizeParams.prizeName }}</span>
            </p>
            <div v-if="prizeParams.imgurl" class="prize__bg">
              <img :src="prizeParams.imgurl" alt="" />
            </div>
          </div>
        </div>
        <div
          class="common-popup__btn"
          @click.stop="btnFun"
          v-if="popupParams.showPopupBtn"
        >
          {{ popupParams.btn }}
        </div>
        <div v-if="prizeParams.poptips && (prizeParams.prizeType === 2 || prizeParams.prizeType === '2')" class="common-popup__tips">{{ prizeParams.poptips }}</div>
        <!-- 每日三次翻牌机会用完-banner -->
        <div
          class="common-popup__banner"
          v-if="banner.imgUrl && popupParams.showBanner && this.usedCount === 3"
        >
          <div v-if="popupParams.tips" class="tips">{{ popupParams.tips }}</div>
          <img :src="banner.imgUrl" alt="" @click.stop="jumpview(banner)" />
        </div>
        <p v-if="popupParams.isPrize" class="popup-manyidu-tips">感谢您的参与，我们期待您的10分好评！<br />祝您生活愉快！</p>
      </div>
      <!-- 任务 -->
      <div class="task" v-if="popupParams.showTask && this.usedCount < 3">
        <p class="task__tips">您还可以通过完成以下任务获得更多翻牌机会哦：</p>
        <!-- 分享任务 -->
        <div class="task-item">
          <img src="@/projects/checkin-new/assets/turn-card/share.png" alt="" />
          <div class="task-item-center">
            <div class="task-item__title">分享好友</div>
            <div>邀请好友获得一次翻牌机会</div>
          </div>
          <div class="task-item__btn" @click.stop="fpzShare()">去分享</div>
        </div>
        <!-- 浏览任务 -->
        <div
          class="task-item2"
          v-if="!visitStatus && task && task.imgurl"
          @click.stop="doTask()"
        >
          <img :src="task.imgurl" alt="" />
          <!-- <div class="task-item-center">
              <div class="task-item__title">{{ browseTask.blocktitle }}</div>
              <div>{{ task.title }}</div>
          </div>
          <div class="task-item__btn" @click="doTask()">去查询</div> -->
        </div>
      </div>
    </section>
  </div>
</template>

<script>
import { BANNER_LIST, SUCCESS_CODE } from '@/projects/checkin-new/config/index'
import { goUrl } from '@/projects/checkin-new/utils/utils'
import { getSponsor } from '@/projects/checkin-new/api/index'
// import { Base64 } from '@/utils/base64'
import jtWebtrends1 from '@/utils/jtWebtrends'
import CHANNEL from '../../../../../../../../../../bjapp-model/vue2/js/channel'

export default {
  components: {},
  data() {
    return {
      popupParamsList: {
        // 三次无机会
        allNoChange: {
          title: '很遗憾',
          content: '没有翻牌机会了<br>每日最多可翻三次哟~',
          tips: '您还可以参与以下活动哦',
          dialogStyle: {
            alignItems: 'center',
            textAlign: 'center',
            justifyContent: 'center',
            display: 'flex'
          },
          btn: '',
          showPopupBtn: false,
          showBanner: true,
          callBack: () => {}
        },
        // 无机会
        noChange: {
          title: '很遗憾',
          content: '您没有翻牌机会了',
          tips: '',
          dialogStyle: {
            alignItems: 'center',
            textAlign: 'center',
            justifyContent: 'center',
            display: 'flex'
          },
          btn: '',
          showPopupBtn: false,
          showBanner: false,
          showTask: true,
          callBack: () => {}
        },
        // 未签到
        noCheckin: {
          title: '温馨提示',
          content: '完成签到后可获得一次翻牌机会哦！',
          dialogStyle: {
            textAlign: 'center'
          },
          btn: '我要签到',
          showPopupBtn: true,
          closeBtn: true,
          callBack: () => {
            jtWebtrends1.multiTrack(
              'P00000025032',
              '中国移动APP每日签到_翻牌子未签到弹窗_我要签到'
            )
          }
        },
        // 未获奖弹窗
        noWinPrize: {
          title: '温馨提示',
          content: '谢谢参与~',
          tips: '您还可以参与以下活动哦',
          dialogStyle: {
            alignItems: 'center',
            textAlign: 'center',
            justifyContent: 'center',
            display: 'flex'
          },
          btn: '',
          showPopupBtn: false,
          showBanner: true,
          showTask: true,
          callBack: () => {
            this.$emit('changeShowGradePopup')
            // Webtrends1.multiTrack('210910_STCHWLHD_JHY_TC_BKCYTC_ZDL')
          }
        },
        // 获奖弹窗
        winPrize: {
          title: '恭喜您',
          content: '',
          tips: '',
          dialogStyle: {
            alignItems: 'center',
            textAlign: 'center',
            justifyContent: 'center',
            display: 'flex'
          },
          btn: '我知道了',
          isPrize: true,
          showPopupBtn: true,
          showBanner: false,
          showTask: true,
          callBack: () => {
            this.$emit('changeShowGradePopup')
            // const chama = this.usedCount === 3 ? '220613_QDGB_JHY_WFPJHHJTC_CKJP' : '220613_QDGB_JHY_FPZHJTC_CKJP'
            // jtWebtrends1.multiTrack(
            //   'P00000025031',
            //   '中国移动APP每日签到_翻牌子获奖弹窗_查看奖品'
            // )
            // this.$router.push({ name: 'Prize' })
          }
        },
        // 分享弹窗
        sharePopup: {
          title: '分享成功',
          content: `<p class="shallow">恭喜您<br></p><p>获得了<span class="red">${this.shareNum}次翻牌机会</span><br>快去试试手气吧！</p>`,
          dialogStyle: {
            textAlign: 'center'
          },
          btn: '知道了',
          showPopupBtn: true,
          closeBtn: true,
          callBack: () => {
            jtWebtrends1.multiTrack(
              'P00000025039',
              '中国移动APP每日签到_好友助力成功弹窗_知道了'
            )
          }
        }
      },
      popupParams: {},
      banner: {},
      task: {}
    }
  },
  props: {
    showPopup: {
      type: Boolean,
      default: false
    },
    // 当天浏览任务状态
    visitStatus: {
      type: Boolean,
      default: false
    },
    currentDate: {
      type: Number,
      default: 0
    },
    popupType: {
      type: String,
      default: 'noChange'
    },
    // 今日已用翻牌次数
    usedCount: {
      type: Number,
      default: 0
    },
    shareNum: {
      type: Number,
      default: 0
    },
    // 浏览任务
    browseTask: {
      type: Object,
      default() {
        return {}
      }
    },
    prizeParams: {
      type: Object,
      default() {
        return {}
      }
    }
  },
  watch: {
    currentDate: {
      handler(val) {
        const index = new Date(val).getDay()
        this.banner = BANNER_LIST[index]
      },
      immediate: true
    },
    popupType: {
      handler(val) {
        this.popupParams = this.popupParamsList[val]
      },
      immediate: true
    },
    shareNum: {
      handler() {
        if (this.popupType === 'sharePopup') {
          this.popupParams.content = `<p class="shallow">恭喜您<br></p><p>获得了<span class="red">${this.shareNum}次翻牌机会</span><br>快去试试手气吧！</p>`
        }
      },
      immediate: true
    },
    browseTask: {
      handler(val) {
        if (val.block) {
          this.task = val.block[0]
        }
        // console.log('this.task', this.task)
      },
      immediate: true,
      deep: true
    }
  },
  mounted() {},
  methods: {
    // 按钮事件
    btnFun() {
      this.$emit('closePopup')
      this.$emit('update:showPopup', false)
      this.popupParams.callBack && this.popupParams.callBack()
    },
    // 关闭弹窗
    hide() {
      this.$emit('closePopup')
      this.$emit('update:showPopup', false)
      if(this.popupType === 'winPrize' || this.popupType === 'noWinPrize') {
        this.popupParams.callBack && this.popupParams.callBack()
      }
    },
    // banner 跳转
    jumpview(item) {
      const chama =
        this.popupType === 'winPrize' ? item.chama.fpzP : item.chama.fpNP
      const code = chama.split(',')
      jtWebtrends1.multiTrack(code[0], code[1])
      goUrl(item.url)
    },
    // 做浏览任务
    doTask() {
      jtWebtrends1.multiTrack(
        'P00000025034',
        '中国移动APP每日签到_翻牌子弹窗_任务二_去查询'
      )

      this.$emit('doTask')
    },
    // 分享助力
    fpzShare() {
      jtWebtrends1.multiTrack(
        'P00000025033',
        '中国移动APP每日签到_翻牌子弹窗_分享好友'
      )
      let shareSponsor = sessionStorage.getItem('shareSponsor')
      if (!shareSponsor) {
        getSponsor({})
          .then((res) => {
            const { result, sponsor } = res
            if (result === SUCCESS_CODE) {
              shareSponsor = sponsor
              sessionStorage['set' + 'Item']('shareSponsor', sponsor)
            }
          })
          .catch(() => {})
          .finally(() => {
            this.checkVer() && this.shareWXMi(shareSponsor)
          })
      } else {
        this.checkVer() && this.shareWXMi(shareSponsor)
      }
    },
    // 分享前检查版本(安卓820版本提示升级弹窗)
    checkVer() {
      const version = sessionStorage.getItem('version')
      const stp = version.split('.')
      const version_top = '8'
      const version_middle = '2'
      const version_bottom = '0'
      const isAnd = CHANNEL.isAndroid()
      if (stp[0] === version_top && stp[1] === version_middle && stp[2] === version_bottom && isAnd) {
        this.hide()
        this.$emit('showUpgrade')
        return false
      } else {
        return true
      }
    }
  }
}
</script>

<style lang="scss" scoped>
@import '~@/projects/checkin-new/styles/mixin.scss';
.common-popup {
  position: fixed;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 15;
  &-main {
    width: 600px;
    min-height: 281px;
    border-radius: 24px;
    position: absolute;
    left: 50%;
    top: 47%;
    transform: translate(-50%, -50%);
  }
  &__dec {
    position: relative;
    top: -80px;
    @include background(600px, 194px, 'turn-card/bg-popup.png');
  }
  &-top {
    background-color: #ffffff;
    padding-bottom: 20px;
    border-radius: 24px;
  }
  &-content {
    @include box-text(32px, #000000, 48px);
    width: 100%;
    height: 50%;
    overflow-y: auto;
    padding: 0 10px;
    font-weight: bold;
    ::v-deep .shallow {
      font-weight: normal;
      font-size: 36px;
      line-height: 48px;
      letter-spacing: 0px;
      color: #000000;
      margin-bottom: 30px;
    }
    ::v-deep .red {
      color: #ff5426;
      line-height: 70px;
    }
  }
  &__title {
    text-align: center;
    font-size: 50px;
    font-weight: bold;
    font-stretch: normal;
    line-height: 48px;
    letter-spacing: 0px;
    color: #000000;
    position: relative;
    top: -35px;
  }
  &-prize {
    font-size: 32px;
    font-weight: bold;
    font-stretch: normal;
    letter-spacing: 0px;
    color: #000000;
    margin: 0 auto;
    text-align: center;
    span {
      color: #fe4d45;
    }
    .prize__text {
      margin: 0 auto 20px;
    }
    .prize__bg {
      width: 449px;
      height: 220px;
      margin: 0 auto 20px;
      @include flex(center, center);
      img {
        width: 200px;
        height: 200px;
      }
    }
  }
  &__btn {
    position: relative;
    @include btn();
    margin: 30px auto;
  }
  &__tips {
    width: 480px;
    margin: 0 auto;
    font-size: 28px;
    text-align: center;
  }
  &__banner {
    margin-top: 20px;
    img {
      width: 547px;
      height: 160px;
    }
    .tips {
      font-size: 24px;
      font-weight: normal;
      font-stretch: normal;
      line-height: 36px;
      letter-spacing: 0px;
      color: #666666;
    }
  }
  .close {
    width: 79px;
    height: 79px;
    background: url('~@/projects/checkin-new/components/AspDialog/img/close.png');
    background-size: 100% 100%;
    position: absolute;
    bottom: -110px;
    left: 50%;
    transform: translateX(-50%);
  }
  .task {
    // width: 600px;
    background-color: #ffffff;
    border-radius: 24px;
    position: relative;
    margin-top: 12px;
    padding: 20px 25px;
    font-size: 24px;
    font-weight: normal;
    font-stretch: normal;
    line-height: 36px;
    letter-spacing: 0px;
    color: #666666;
    &::before {
      content: '';
      width: 575px;
      height: 57px;
      display: inline-block;
      @include bg('turn-card/huan.png');
      position: absolute;
      left: 50%;
      transform: translateX(-50%);
      top: -32px;
    }
    &__tips {
      margin: 20px auto;
      width: 477px;
      font-family: PingFang-SC-Bold;
      font-size: 30px;
      font-weight: normal;
      font-stretch: normal;
      line-height: 40px;
      letter-spacing: 0px;
      color: #333333;
    }
    &-item {
      display: flex;
      align-items: center;
      margin: 20px auto 10px;
      min-height: 120px;
      background: rgba(#fcb57c, 0.14);
      border-radius: 12px;
      padding: 20px;
      img {
        width: 86px;
        height: 86px;
        display: inline-block;
        margin-right: 20px;
      }
      &-center {
        text-align: left;
        flex: 1;
      }
      &__title {
        font-size: 32px;
        font-weight: bold;
        font-stretch: normal;
        line-height: 36px;
        letter-spacing: 0px;
        color: #000000;
        margin-bottom: 5px;
        @include ellipsis(1);
      }
      &__btn {
        width: 110px;
        height: 48px;
        background-image: linear-gradient(10deg, #ff4945 0%, #ff846d 100%),
          linear-gradient(#ff9962, #ff9962);
        background-blend-mode: normal, normal;
        box-shadow: 0px 4px 8px 0px rgba(255, 60, 60, 0.34);
        border-radius: 24px;
        font-size: 24px;
        font-weight: normal;
        font-stretch: normal;
        line-height: 48px;
        letter-spacing: 0px;
        color: #ffffff;
        margin-left: 6px;
      }
      &:nth-child(3) {
        background: rgba(#89c0fd, 0.14);
        .task-item__btn {
          width: 110px;
          height: 48px;
          background-image: linear-gradient(10deg, #4facfe 0%, #00f2fe 100%),
            linear-gradient(#ff9962, #ff9962);
        }
      }
    }
    &-item2 {
      width: 550px;
      height: 120px;
      border-radius: 12px;
      background: rgba(#89c0fd, 0.14);
      overflow: hidden;
      margin: 20px auto 10px;
      img {
        width: 100%;
        height: 100%;
      }
    }
  }
}
.popup-manyidu-tips {
  min-width: fit-content;
  width: 80%;
  margin: auto;
  border-radius: 15px;
  line-height: 34px;
  padding: 5px 15px;
  font-size: 21px;
  background: #fdf1ee;
  color: #f3544a;
  margin: 10px auto;
  text-align: center;
}
</style>
