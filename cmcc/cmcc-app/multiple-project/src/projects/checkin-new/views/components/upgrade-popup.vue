<template>
  <van-popup v-model="show" round :close-on-click-overlay="false">
    <div class="box">
      <div class="title">温馨提示</div>
      <div class="line"></div>
      <div class="text">很抱歉，您需要升级到最新版本才支持分享小程序功能，建议您点击下方按钮直接下载升级哦，期待新版本给您带来新惊喜~</div>
      <div class="btn-sure" @click="down">立即下载</div>
      <div class="close" @click="closePopup"></div>
    </div>
  </van-popup>
</template>

<script>
export default {
  data() {
    return {
      show: false
    }
  },
  methods: {
    showPopup() {
      this.show = true
    },
    closePopup() {
      this.show = false
    },
    down() {
      location.href = 'http://down.app.coc.10086.cn/downfile/apk/ChinaMobile.apk'
    }
  }
}
</script>

<style lang="scss" scoped>
.box {
  position: relative;
  width: 624px;
  height: 417px;
  .title {
    display: flex;
    justify-content: center;
    font-size: 40px;
    color: #000000;
    font-weight: bold;
    margin-top: 36px;
  }
  .line {
    margin-top: 36px;
    margin-left: 30px;
    width: 564px;
    height: 1px;
    background: #e5e5e5;
  }
  .text {
    font-size: 28px;
    line-height: 38px;
    width: 528px;
    margin: 0 auto;
    color: #333333;
    margin-top: 40px;
  }
  .btn-sure {
    width: 564px;
    height: 90px;
    font-weight: 500;
    font-size: 32px;
    text-align: center;
    line-height: 90px;
    color: #ffffff;
    background: linear-gradient(-90deg, #007eff 0%, #2892ff 100%);
    border-radius: 18px;
    margin: 0 auto;
    margin-top: 50px;
  }
  .close {
    position: absolute;
    bottom: -100px;
    left: 290px;
    width: 64px;
    height: 64px;
    background: url('../../assets/index/X.png') no-repeat center center;
    background-size: 100% 100%;
  }
}
</style>
