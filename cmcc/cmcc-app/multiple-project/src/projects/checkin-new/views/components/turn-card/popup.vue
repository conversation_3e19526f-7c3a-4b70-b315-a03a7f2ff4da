<template>
  <section class="my-popup" v-if="showPopup" @touchmove.prevent>
    <div class="my-popup-main">
      <div class="my-popup-title">
        <p>{{ title }}</p>
        <span class="my-popup__close" @click.stop="closePopup"></span>
      </div>
      <slot></slot>
    </div>
  </section>
</template>

<script>
export default {
  props: {
    // 标题`
    title: {
      type: String,
      default: ''
    },
    showPopup: {
      type: Boolean,
      default: false
    }
  },
  methods: {
    closePopup() {
      this.$emit('closePopup')
    }
  }
}
</script>

<style lang="scss" scoped>
@import "~@/projects/checkin-new/styles/mixin.scss";
.my-popup {
  position: fixed;
  top: 0;
  left: 0;
  bottom: 0;
  right: 0;
  width: 100%;
  height: 100vh;
  background: rgba($color: #000000, $alpha: 0.5);
  z-index: 99;
  &-main {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    max-height: 80%;
    box-sizing: border-box;
    margin: 0 auto;
    text-align: center;
    background: #fff;
    padding: 30px;
    border-radius: 24px 24px 0px 0px;
  }
  &-title {
    width: 100%;
    font-size: 16px;
    text-align: left;
    font-size: 32px;
    font-weight: bold;
    font-stretch: normal;
    line-height: 46px;
    letter-spacing: 0px;
    color: #000000;
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
  }
  &__close {
    display: block;
    @include background(25px, 25px, "index/close_x.png");
    font-weight: bold;
    color: #333333;
  }
}
</style>
