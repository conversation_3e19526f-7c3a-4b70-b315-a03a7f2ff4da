<!--
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON> <PERSON>hen<PERSON><PERSON><PERSON>@aspirecn.com
 * @Date: 2025-02-25 14:47:53
 * @LastEditors: zhen<PERSON><PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2025-04-29 10:34:38
 * @FilePath: \family-network-2\src\components\transfer-jttj\popup.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <div v-if="showFamilyUpdate" class="sj-popup">
    <div class="sj-popup__contain">
      <p class="contain-title">家庭圈我的圈子升级提醒</p>
      <p class="contain-desc">尊敬的用户，我的圈子功能全新升级啦~现在点击升级组建圈子成功，您可获赠<span>1GB流量卡券</span>，升级成功即刻发放奖励，该功能不收取任何费用，是否升级?</p>
      <div class="contain-btns">
        <div class="contain-btns__btn contain-btns__close" @click="close">暂不升级</div>
        <div class="contain-btns__btn contain-btns__update" @click="goUpdate">确认升级</div>
      </div>
    </div>
  </div>
</template>
<script>
export default {
  props: {
    showFamilyUpdate: {
      type: Boolean,
      default: false
    }
  },
  methods: {
    close() {
      this.$emit('update:showFamilyUpdate', false)
    },
    goUpdate() {
      this.$router.push('/update-family')
      this.$emit('update:showFamilyUpdate', false)
    }
  }
}
</script>
<style lang="scss" scoped>
.sj-popup {
  position: fixed;
  width: 100vw;
  height: 100vh;
  left: 0;
  top: 0;
  background: rgba(0,0,0,.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 100;
  &__contain {
    position: relative;
    width: 624px;
    min-height: 474px;
    background: #FFFFFF;
    border-radius: 18px;
    padding: 30px;
    .contain-title {
      font-size: 40px;
      line-height: 52px;
      color: #000000;
      font-weight: bold;
      border-bottom: 1px solid rgba(0,0,0,.2);
      padding-bottom: 35px;
    }
    .contain-desc {
      font-size: 28px;
      color: #333333;
      line-height: 40px;
      padding: 26px 31px;
      text-align: left;
      span {
        color: #F81E05;
      };
    }
    .contain-btns {
      display: flex;
      justify-content: space-between;
      padding-top: 22px;
      .contain-btns__btn {
        width: 564px;
        background-image: linear-gradient(270deg, #FF548F 0%, #FF7062 100%);
        height: 90px;
        border-radius: 18px;
        font-size: 32px;
        line-height: 90px;
        color: #FFFFFF;
      }
      .contain-btns__close, .contain-btns__update {
        width: 270px;
      }
      .contain-btns__close {
        background: #F6F6F6;
        color: #666666;
      }
    }
  }
}
</style>
