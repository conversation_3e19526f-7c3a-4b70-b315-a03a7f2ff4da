<template>
  <div v-if="showFullCheckinPopup && FullCheckinPopupImgurl" class="full-checkin">
    <div class="main">
      <div class="btn" @click="close"></div>
      <img class="close" src="../../assets/index/X.png" alt="" @click="close">
    </div>
  </div>
</template>
<script>
export default {
  props: {
    showFullCheckinPopup: {
      type: Boolean,
      default: false
    },
    FullCheckinPopupImgurl: {
      type: String,
      default: ''
    }
  },
  mounted() {
    console.log(this.showFullCheckinPopup, this.FullCheckinPopupImgurl)
  },
  methods: {
    close() {
      this.$emit('update:showFullCheckinPopup', false)
    }
  }
}
</script>
<style lang="scss" scoped>
.full-checkin {
  position: fixed;
  inset: 0;
  background: rgba(0,0,0,0.7);
  width: 100vw;
  height: 100vh;
  z-index: 11111;
  .main {
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-51.5%, -50%);
    width: 1097px;
    height: 1157px;
    background: url('../../assets/index/full-main-bg.png') no-repeat center center;
    background-size: 100% 100%;
    .btn {
      position: absolute;
      bottom: 220px;
      left: 50%;
      transform: translateX(-50%);
      width: 500px;
      height: 100px;
    }
    .close {
      position: absolute;
      left: 50%;
      bottom: 90px;
      width: 44px;
      height: 44px;
    }
  }
}
</style>