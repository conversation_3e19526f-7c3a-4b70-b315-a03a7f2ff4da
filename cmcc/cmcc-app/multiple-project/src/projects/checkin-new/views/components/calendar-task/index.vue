<template>
  <div class="bq-task" :class="{'show': show}">
    <div class="bq-task-bg" @click="close"></div>
    <div class="bq-task-content">
      <div class="contain">
        <p class="contain-title"></p>
        <p class="contain-desc">补签卡仅当月有效哦~</p>
        <div class="contain-close" @click="close"></div>
        <ul v-if="(actsignTasks && actsignTasks.length) || (netageTask && netageTask.url)" class="contain-list">
          <template v-for="(item, index) in [netageTask, ...actsignTasks]">
            <li v-if="item.url" class="contain-list__item">
              <img class="item-left" :src="item.imgurl" alt="">
              <div class="item-middle">
                <p class="item-middle__one">{{ item.title }}</p>
                <p class="item-middle__two">{{ item.subtitle }}</p>
              </div>
              <div class="item-right" v-if="actsignTaskStatuslist[index] && actsignTaskStatuslist[index][`task${index + 1}`] === 1" @click="goUrl(item)">已完成</div>
              <div class="item-right" v-else-if="index === 0" @click="goCompleteTask(index, item)">去领取</div>
              <div class="item-right" v-else @click="goCompleteTask(index, item)">去浏览</div>
            </li>
          </template>
        </ul>
      </div>
    </div>
  </div>
</template>
<script>
import { mapState } from 'vuex';
import { setSignCardTaskStatus } from '../../../api/index';
import { newWebview } from '../../../../../../../../../bjapp-model/vue2/js/jt-app-ability';
import CHANNEL from '../../../../../../../../../bjapp-model/vue2/js/channel';
export default {
  props: {
    show: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      timer: null
    }
  },
  computed: {
    ...mapState({
      actsignTasks: state => state.actsignTasks,
      actsignTaskStatuslist: state => state.actsignTaskStatuslist,
      netageTask: state => state.netageTask
    })
  },
  destroyed() {
    clearTimeout(this.timer)
  },
  methods: {
    goCompleteTask(index, item) {
      if(index !== 0) {
        setSignCardTaskStatus({type: 'task' + (index + 1)}).then(res => {
          if(res.result === 0) {
            this.timer = setTimeout(() => {
              this.$emit('querySignCardNum')
              this.$emit('queryTaskStatus')
            }, 1500)
          }
        })
        this.goUrl(item)
      } else if(CHANNEL.isIOS()) {// 网龄专区的任务，需要监听任务完成情况，ios使用pageshow，安卓使用监听storgae
        window.location.href = item.url
      } else {
        this.goUrl(item)
      }
    },
    goUrl(item) {
      newWebview(item.url)
    },
    close() {
      this.$emit('update:show', false)
    }
  }
}
</script>
<style lang="scss" scoped>
.bq-task {
  position: fixed;
  left: 0;
  bottom: 0;
  width: 100vw;
  height: 100vh;
  opacity: 0;
  overflow: hidden;
  transition: all .5s;
  z-index: -5;
  &-bg {
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
  }
  &-content {
    position: absolute;
    left: 0;
    bottom: 0;
    width: 100%;
    height: 0;
    z-index: 1;
    transition: all .5s;
    &::before {
      content: '';
      display: inline-block;
      position: absolute;
      width: 100vw;
      height: 200px;
      top: -81px;
      left: 0;
      background: url('../../../assets/calendar/task-before.png') no-repeat center center;
      background-size: 100% 100%;
      z-index: -1;
    }
    &::after {
      content: '';
      display: inline-block;
      position: absolute;
      width: 298px;
      height: 221px;
      top: -165px;
      left: 200px;
      background: url('../../../assets/calendar/task-after.png') no-repeat center center;
      background-size: 100% 100%;
    }
    .contain {
      position: relative;
      height: 927px;
      border-radius: 50px 50px 0 0;
      padding: 53px 30px 0;
      background-image: linear-gradient(90deg, #ffe9e4 0%, #fff5ee 50%, #ffedf4 100%),  linear-gradient(#fdae58,  #fdae58);
      &-title {
        width: 341px;
        height: 57px;
        margin: auto;
        background: url('../../../assets/calendar/task-title.png') no-repeat center center / contain;
      }
      &-desc {
        width: 500px;
        height: 40px;
        line-height: 40px;
        font-size: 26px;
        color: #ffffff;
        text-align: center;
        margin: 7px auto auto;
        background-image: linear-gradient(-90deg, #ffeff2, #f4574c, #f4574c, #ffeae4);
      }
      &-close {
        position: absolute;
        right: 35px;
        top: 53px;
        width: 46px;
        height: 46px;
        background: url('../../../assets/calendar/close.png') no-repeat center center / contain;
      }
      &-list {
        padding-bottom: 47px;
        height: calc(100% - 100px);
        overflow: auto;
        overflow-x: hidden;
        &__item {
          width: 692px;
          height: 164px;
          background-color: #ffffff;
          border-radius: 30px;
          margin: 18px auto auto;
          padding: 17px;
          display: flex;
          align-items: center;
          .item-left {
            width: 130px;
            height: 130px;
            margin-right: 27px;
          }
          .item-middle {
            flex: 1;
            text-align: left;
            &__one {
              font-size: 36px;
              line-height: 45px;
              color: #ff4a5d;
              font-weight: bold;
            }
            &__two {
              font-size: 26px;
              line-height: 45px;
              color: #ff8a13;
            }
          }
          .item-right {
            width: 136px;
            height: 65px;
            background-image: linear-gradient(-48deg,  #ff485e 0%,  #ff8a13 100%);
            box-shadow: 0px 8px 25px 0px  rgba(216, 204, 255, 0.53);
            border-radius: 32px;
            font-size: 30px;
            line-height: 65px;
            color: #ffffff;
            text-align: center;
          }
        }
      }
    }
  }
  &.show {
    opacity: 1;
    z-index: 10;
    .bq-task-content {
      height: 927px;
    }
  }
}
</style>
