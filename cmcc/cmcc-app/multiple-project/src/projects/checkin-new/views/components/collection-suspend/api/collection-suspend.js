import request from '@/utils/request'
const keyword = 'ActMindCard2022'
const basePath = 'ActMindCard2022'
const channel = 'JT'

// 获取满意度弹窗展示状态接口
export function queryUserGradePopUp (params) {
  return request({
    url: `/${basePath}/queryUserGradePopUp/${channel}/${keyword}`,
    method: 'get',
    params: params
  })
}
// 设置本月不展示状态
export function setUserGradePopUpState (params) {
  return request({
    url: `/${basePath}/queryUserGradePopUp/${channel}/${keyword}`,
    method: 'post',
    params: params
  })
}
// 评分接口
export function grade(params) {
  return request({
    url: `/${basePath}/grade/${channel}/ActMindScore2023`,
    method: 'post',
    params: params
  })
}
