<template>
  <div class="calendar">
    <!-- <recentPrize :recentPrizeDate="recentPrizeDate" /> -->
    <p class="calendar-title"></p>
    <div class="calendar-bk">
      <p class="calendar-bk__title">您有{{ signCardNum }}张补签卡</p>
      <p class="calendar-bk__desc">补签卡仅当月有效哦~</p>
      <div  class="calendar-bk__btn" @click="showBQTask">领取补签卡</div>
    </div>
    <div class="calendar-main">
      <div class="calendar-main__title">
        {{ year }}年{{ month + 1 }}月
      </div>
      <checkinTop :showTitle="false" haveToken />
      <div class="calendar-main__desc">每月第1、3、5次签到可获得10MB、50MB、100MB流量哦~</div>
      <ul class="calendar-main__week">
        <li v-for="(item, index) in calendarTitleArr" :key="index" class="week-item">{{ item }}</li>
      </ul>
      <ul class="calendar-main__view">
        <!-- 动态设置背景颜色 -->
        <li v-for="(item, index) in visibleCalendar" :key="index" :class="['date-item', {'date-item--now': item.isCurrentDay}, {'date-item--checkin': item.signInDate}, {'date-item--future': item.isFutureDate}, {'date-item--opacity': !item.isCurrentMonth}]">
          <!-- 非本月-占位-透明度为0 -->
          <span v-if="!item.isCurrentMonth">
            {{ item.day }}
          </span>
          <!-- 当月有奖励的展示 -->
          <div v-else-if="item.futurePrizeData.prizeObj" class="date-item__prize">
            <img :src="item.futurePrizeData.prizeObj.prizeImg" alt="" />
            <!-- <p>{{ item.futurePrizeData.prizeObj.prizeDesc }}</p> -->
          </div>
          <!-- 当月无奖励的展示 -->
          <div v-else class="date-item__num" @click="signInRepair(item)">
            <span></span>
            <p>{{ item.isCurrentDay ? '今天' : item.day }}</p>
          </div>
        </li>
      </ul>
    </div>
    <p class="calendar-tips">
      ·补签成功后不再补发翻牌次数<br />
      ·当月满签可参与满签大抽奖赢5GB
    </p>
    <div class="calendar-back" @click="closeCalendar">返回</div>
    <AspDialogTwo :params.sync="popupParams" />
    <qdTask :show.sync="showTask" @queryTaskStatus="queryTaskStatus" @querySignCardNum="querySignCardNum"/>
  </div>
</template>

<script>
import { mapState, mapActions } from 'vuex'
import jtWebtrends1 from '@/utils/jtWebtrends'
import CHANNEL from '../../../../../../../bjapp-model/vue2/js/channel'
import { getNewDate, getDate, formatDate, dateFormat, listGetHavePrize, getTenFormatNum } from '../utils/utils.js'
import { repairSign, querySignCardTaskStatus, getSignIn } from '../api/index'
import { getcheckinResult } from '../utils/checkin'
import AspDialogTwo from '../components/AspDialog/index-two.vue'
import qdTask from './components/calendar-task/index.vue'
import checkinTop from './components/checkin/index-top.vue'
// import recentPrize from './components/checkin/recent-prize.vue'

export default {
  components: {
    AspDialogTwo,
    qdTask,
    checkinTop,
    // recentPrize
  },
  data() {
    return {
      calendarTitleArr: ['日', '一', '二', '三', '四', '五', '六'],
      year: 0,
      month: 0,
      day: 0,
      popupParams: {
        show: false
      },
      showTask: false,
      localListen: () => {}
    }
  },
  computed: {
    ...mapState({
      signInList: state => state.signInList,
      futurePrizeList : state => state.futurePrizeList,
      currentDate: state => state.currentDate,
      recentPrizeDate: state => state.recentPrizeDate,
      misdnmask: state => state.misdnmask,
      isSignIn: state => state.isSignIn,
      signCardNum: state => state.signCardNum,
    }),
    visibleCalendar() {
      const calendatArr = []
      const { year, month } = getNewDate(getDate(this.year, this.month, 1))
      const currentFirstDay = getDate(year, month, 1)
      const weekDay = currentFirstDay.getDay() // 获取当前月第一天星期几
      let startTime = null
      let length = 42
      if (weekDay === 0) {// 当月第一天是星期天
        startTime = currentFirstDay - 0 * 24 * 60 * 60 * 1000
        length = 35
      } else {
        startTime = currentFirstDay - weekDay * 24 * 60 * 60 * 1000
      }
      for (let i = 0; i < length; i++) {// 为了页面整齐排列
        const tempI = {
          date: new Date(startTime + i * 24 * 60 * 60 * 1000),
          year: new Date(startTime + i * 24 * 60 * 60 * 1000).getFullYear(),
          month: new Date(startTime + i * 24 * 60 * 60 * 1000).getMonth() + 1,
          day: new Date(startTime + i * 24 * 60 * 60 * 1000).getDate()
        }
        calendatArr.push({
          ...tempI,
          isCurrentDay: this.isCurrentDay(tempI),
          signInDate: this.signInDate(tempI),
          isFutureDate: this.isFutureDate(tempI),
          isCurrentMonth: this.isCurrentMonth(tempI.date),
          futurePrizeData: this.futurePrizeData(tempI)
        })
      }
      return calendatArr
    },
  },
  mounted() {
    window.scrollTo(0, 0)
    this.isDoTaskRefresh()
  },
  destroyed() {
    window.removeEventListener(CHANNEL.isAndroid() ? 'storage' : 'pageshow', this.localListen)
  },
  beforeRouteLeave(to, from, next) {
    this.popupParams = { show: false }
    this.showTask = false
    next()
  },
  activated() {
    const currentDate_ = new Date(this.currentDate)
    this.year = currentDate_.getFullYear()
    this.month = currentDate_.getMonth()
    this.day = currentDate_.getDate()
    this.queryTaskStatus()
  },
  methods: {
    ...mapActions(['updateIsSign', 'updateSignInList', 'updateSignCardNum', 'updateActsigntaskstatuslist']),
    // 根据返回来判断是否刷新任务-仅限网领专区
    isDoTaskRefresh() {
      const refreshFn = (e, type) => {
        if(e.key === 'BJSTWLZQ' && e.newValue === 'QDBQRW') {// 北京手厅网龄-签到补签任务
          this.queryTaskStatus()
          localStorage.removeItem('BJSTWLZQ')
          window.removeEventListener(type, this.localListen)
        }
      }
      if(CHANNEL.isAndroid()) {
        this.localListen = (e) => {
          refreshFn(e, 'storage')
        }
        window.addEventListener('storage', this.localListen)
      } else {
        this.localListen = (e) => {
          if (e.persisted) {
            refreshFn({key: 'BJSTWLZQ', newValue: String(localStorage.getItem('BJSTWLZQ'))}, 'pageshow')
          }
        }
        this.localListen = window.addEventListener('pageshow', this.localListen)
      }
    },
    // 查询补签卡
    querySignCardNum() {
      getSignIn({ token: this.userToken }).then((res) => {
        const { result, signInList, isSignIn, signCardNum } = res
        if (result === 0) {
          this.updateSignInList(signInList)
          this.updateIsSign(isSignIn)
          this.updateSignCardNum(signCardNum)
        }
      })
    },
    // 查询任务状态
    queryTaskStatus() {
      querySignCardTaskStatus({}).then(res => {
        this.updateActsigntaskstatuslist(res && res.list || [])
      })
    },
    // 是否是当前月
    isCurrentMonth(date) {
      const { year: currentYear, month: currentMonth } = getNewDate(getDate(this.year, this.month, 1))
      const { year, month } = getNewDate(date)
      return currentYear === year && currentMonth === month
    },
    // 是否是当天
    isCurrentDay(date) {
      const dateTime = date.year + '-' + formatDate(date.month) + '-' + formatDate(date.day)
      const today = dateFormat(this.currentDate, 'yyyy-MM-dd')
      return dateTime === today
    },
    // 是否已签到
    signInDate(date) {
      const dateTime = date.year + '-' + formatDate(date.month) + '-' + formatDate(date.day)
      let isCheckin = false
      this.signInList.forEach((item) => {
        if (item.signInDate === dateTime) {
          isCheckin = true
        }
      })
      return isCheckin
    },
    // 是否是未来日期
    isFutureDate(date) {
      return date.day > this.day
    },
    // 即将获得奖品
    futurePrizeData(date) {
      const dateTime = date.year + '-' + formatDate(date.month) + '-' + formatDate(date.day)
      let futurePrizeObj = {}
      this.futurePrizeList.forEach((item) => {
        if (item.date === dateTime) {
          futurePrizeObj = item
        }
      })
      return futurePrizeObj
    },
    // 返回
    closeCalendar() {
      jtWebtrends1.multiTrack('P00000025015', '中国移动APP每日签到_日历弹窗_关闭')
      window.history.go(-1)
    },
    // 唤醒微信小程序-订阅提醒
    openWXMi() {
      this.launchwxmini({
        username: 'gh_4dd6b7e7ef37',
        path: `pagesAct/checkin-st/subscribe?from=app&&apptoken=${sessionStorage.getItem('userToken')}&misdnmask=${this.misdnmask}`
      })
    },
    // 展示任务弹窗
    showBQTask() {
      this.showTask = true
    },
    // 补签-二次确认
    signInRepair(item) {
      if (!item.isFutureDate && !item.isCurrentDay && !item.signInDate) {
        const commonParams = {
            show: true,
            title: '温馨提示',
            icon: 'lingdang',
            imgurl: require('../assets/calendar/bk-lef-icon.png')
          }
        if(this.signCardNum <= 0) {
          this.popupParams = {
            ...commonParams,
            content: '您还没有补签卡',
            btnText: '获取补签卡',
            desc: 'Tips：完成指定任务可获得补签卡哦~',
            callBack: () => {
              this.popupParams = { show: false }
              this.showTask = true
            }
          }
        } else {
          this.popupParams = {
            ...commonParams,
            content: '请确认是否使用1张补签卡',
            btnText: '确认补签',
            desc: 'Tips：确认补签后不可修改哦~',
            callBack: () => {
              this.doRepairCheckin(`${item.year}-${getTenFormatNum(item.month)}-${getTenFormatNum(item.day)}`)
            }
          }
        }
      }
    },
    // 补签
    doRepairCheckin(repairDate) {
      this.$loading.waitShow(1000)
      repairSign({ token: this.userToken, repairDate: repairDate })
        .then((res) => {
          let { result, prizelist } = res
          if (getcheckinResult(result)) {
            this.updateSignCardNum(this.signCardNum - 1)
            if (prizelist && prizelist.length > 0 && String(prizelist[0].prizeType) !== '5') {
              this.updateSignInList([...this.signInList, { signInDate: repairDate, signInPrize: prizelist[0] }])
              this.popupParams = {
                show: true,
                title: '补签成功',
                content: '获得' + prizelist[0].prizeName + '奖励',
                imgurl: prizelist[0].imgurl,
                desc: '奖励已为您放入我的奖品啦~',
                btnText: '知道了'
              }
            } else {
              this.updateSignInList([...this.signInList, { signInDate: repairDate }])
              this.popupParams = { show: false }
              this.$toast('补签成功')
            }
            this.querySignCardNum()
          }
        })
        .catch((err) => {
          this.popupParams = { show: false }
          getcheckinResult(err.result)
        })
        .finally(() => {
          this.$loading.waitClose()
          })
    }
  }
}
</script>

<style lang="scss" scoped>
@import "~@/projects/checkin-new/styles/mixin.scss";
.calendar {
  background-image: linear-gradient(90deg, rgb(241,72,70) 0%, rgb(255,155,98) 100%);
  min-height: 100vh;
  padding-bottom: 30px;
  overflow-x: hidden;
  &-title {
    position: relative;
    height: 130px;
    z-index: 0;
    &::before {
      content: '';
      display: inline-block;
      position: absolute;
      left: 59px;
      top: -110px;
      width: 764px;
      height: 406px;
      background: url('../assets/calendar/top-bg.png') no-repeat center center;
      background-size: 100% 100%;
      z-index: -1;
    }
  }
  &-bk {
    position: relative;
    width: 698px;
    height: 139px;
    background-image: linear-gradient(45deg, #feeceb, #fef3e5);
    box-shadow: 0px 0px 10px 0px #febcbd;
    border-radius: 34px;
    margin: 46px auto 0;
    padding-left: 157px;
    padding-top: 25px;
    &::before {
      position: absolute;
      left: 17px;
      top: 6px;
      content: '';
      display: inline-block;
      width: 128px;
      height: 128px;
      background: url('../assets/calendar/bk-lef-icon.png') no-repeat center center;
      background-size: 100% 100%;
    }
    &__title {
      font-family: 'HYYAKUHEIW';
      font-weight: bold;
      font-size: 36px;
      line-height: 50px;
      color: #5e3131;
      text-align: left;
    }
    &__desc {
      font-size: 26px;
      line-height: 36px;
      color: #ff8a13;
      text-align: left;
    }
    &__btn {
      position: absolute;
      right: 30px;
      top: 30px;
      width: 212px;
      height: 88px;
      text-align: center;
      font-size: 26px;
      line-height: 74px;
      color: #ffffff;
      background: url('../assets/calendar/btn.png') no-repeat center center;
      background-size: 100% 100%;
    }
  }
  &-main {
    position: relative;
    width: 698px;
    // min-height: 853px;
    margin: 40px auto 0;
    text-align: center;
    background: url('../assets/calendar/rili-bg.png') no-repeat center center;
    background-size: 100% 100%;
    background-color: #ffffff;
    box-shadow: 0px 0px 10px 0px #febcbd;
    border-radius: 34px;
    padding: 44px 37px 37px;
    &::before, &::after {
      position: absolute;
      content: "";
      display: inline-block;
      width: 14px;
      height: 48px;
      top: -24px;
      background-image: linear-gradient(180deg,
        #fefbc8 0%,
        #ffb2c2 100%,
        #ffcdb4 100%,
        #ffe8a6 100%,
        #86b4b5 100%,
        #0c80c4 100%),
      linear-gradient(
        #ffffff,
        #ffffff);
      border-radius: 7px;
    }
    &::before {
      left: 92px;
    }
    &::after {
      right: 92px;
    }
    &__title {
      font-size: 40px;
      line-height: 50px;
      color: #832415;
      text-align: left;
      font-family: 'HYYAKUHEIW';
      font-weight: bold;
    }
    &__qdtx {
      position: absolute;
      top: 38px;
      right: 37px;
      width: 188px;
      height: 62px;
      line-height: 62px;
      font-size: 24px;
      color: #ffffff;
      background: url('../assets/index/qdtx-btn.png') no-repeat center center / contain;
    }
    &__desc {
      font-size: 20px;
      line-height: 36px;
      letter-spacing: -2px;
      color: #ffba73;
      text-align: left;
      margin-top: 10px;;
    }
    &__week {
      display: flex;
      height: 50px;
      line-height: 50px;
      border: none;
      margin-top: 30px;
      .week-item {
        width: 14.285%;
        text-align: center;
        font-weight: 400;
        font-size: 26px;
        color: #999999;
        letter-spacing: 2px;
        font-weight: bold;
        &:nth-child(1), &:nth-last-child(1) {
          color: #832415;
        }
      }
    }
    &__view {
      display: flex;
      flex-wrap: wrap;
      margin: auto;
      .date-item {
        width: 14.285%;
        height: 120px;
        box-sizing: border-box;
        position: relative;
        display: flex;
        justify-content: center;
        align-items: center;
        &__prize {
          img {
            width: 91px;
            height: 98px;
          }
        }
        &__num {
          width: 65px;
          height: 90px;
          background-image: linear-gradient(0deg, #f4594d 0%, #ff9860 100%);
          border-radius: 10px;
          overflow: hidden;
          p {
            font-size: 26px;
            line-height: 26px;
            color: #ffffff;
            transform: translateY(-2px);
          }
          span {
            display: block;
            margin: 0 auto;
            margin-top: 5px;
            @include background(54px, 54px, "calendar/icon-b.png");
          }
        }
        &.date-item--now {
          .date-item__num {
            background-image: linear-gradient(0deg,  #bcbcbc 0%,  #dadada 100%);
            span {
                @include background(54px, 54px, "calendar/icon-hd.png");
            }
          }
        }
        &.date-item--checkin {
          .date-item__num {
            background-image: linear-gradient(0deg, #f4594d 0%, #ff9860 100%);
            span {
                @include background(54px, 54px, "calendar/icon-dd.png");
            }
          }
        }
        &.date-item--future {
          .date-item__num {
            background-image: linear-gradient(0deg,  #bcbcbc 0%,  #dadada 100%);
            span {
              @include background(54px, 54px, "calendar/icon-hd.png");
            }
          }
        }
        &.date-item--opacity {
          height: 0;
          opacity: 0;
          overflow: hidden;
        }
      }
    }
  }
  &-tips {
    font-size: 22px;
    line-height: 36px;
    text-align: left;
    color: #ffd8d3;
    margin: 20px 0 0 40px;
  }
  &-back {
    width: 574px;
    height: 119px;
    line-height: 115px;
    font-size: 44px;
    letter-spacing: 9px;
    color: #ffffff;
    margin: 33px auto auto;
    background: url('../assets/checkin/btn.png') no-repeat center center / contain;
  }
  ::v-deep .checkin-top__qdtx {
    top: 35px;
  }
}
</style>
