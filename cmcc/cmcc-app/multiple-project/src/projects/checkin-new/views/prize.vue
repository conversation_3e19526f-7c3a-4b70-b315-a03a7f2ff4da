<template>
  <div class="prize">
    <div class="prize-bg"></div>
    <section class="prize-list">
      <div class="title">我的奖品记录</div>
      <div class="illustration"></div>
      <div class="prize-list-content">
        <div v-if="prizeInfo.length === 0" class="no-list">暂无奖品信息~</div>
        <ul v-for="(item, index) in prizeInfo" :key="index">
          <li class="item">
            <div class="item-left">
              <div
                :class="['checkin', isCheckin(item.keyword) ? '' : 'card']"
              ></div>
              <img :src="getImgUrl(item)" alt="" />
            </div>
            <div class="item-right">
              <p class="item-right__name">{{ item.prizeName }}</p>
              <p class="item-right__time">
                中奖时间：{{ dateFormat(item.prizeTime, 'yyyy-MM-dd') }}
              </p>
              <div
                class="item-right__rule"
                @click="openRule(item.prizeType, item.prizeId, item)"
              ></div>
            </div>
            <div v-if="item.btnName" class="item-btn">
              {{ item.btnName }}
              <prize-btn :item="item"></prize-btn>
            </div>
          </li>
        </ul>
      </div>
    </section>
    <div class="prize-tips">
      <div class="prize-tips__title">温馨提示</div>
      <div class="prize-tips__content">
        本页面仅展示近三月奖品记录，详情请至“卡券”中查看。
      </div>
    </div>
    <div class="footer">
      <div class="footer__back" @click.stop="goBack">返回</div>
      <div class="footer__subscription" @click="openWXMi">订阅过期提醒</div>
    </div>
  </div>
</template>

<script>
import prizeBtn from '@/components/prize-btn'
import { getPrizeList, getJTUsageRule } from '../api'
import {
  dateFormat,
  isFlowType,
  getNum,
  getUnit
} from '@/projects/checkin-new/utils/utils'
import { SUCCESS_CODE, IS_YW_JMESS } from '@/projects/checkin-new/config/index'
import { ll_rule, nl_rule, yw_rule, swmd_rule, sw_rule } from '@/projects/checkin-new/config/rule'
import jtWebtrends1 from '@/utils/jtWebtrends'

export default {
  components: {
    prizeBtn
  },
  data() {
    return {
      prizeInfo: [],
      userToken: '',
      isBj: 1
    }
  },
  created() {
    this.userToken = sessionStorage.getItem('userToken')
    this.$loading.show()
  },
  computed: {},
  activated() {
    this.getPrizeInfo()
  },
  beforeRouteLeave(to, from, next) {
    this.$ruleDialog.hide()
    next()
  },
  methods: {
    // 判断签到还是翻牌子奖励
    isCheckin(keyword) {
      if (keyword === 'ActSignIn2023JT' || keyword === 'ActSignIn2022JT') {
        return true
      }
      return false
    },
    // 查询用户中奖记录
    getPrizeInfo() {
      getPrizeList({})
        .then((res) => {
          const { result, isBj, prizelist = [], misdnmask } = res
          sessionStorage['set' + 'Item']('misdnmask', misdnmask)
          if (isBj !== 1) {
            this.$toast(IS_YW_JMESS)
            return
          }
          if (result === SUCCESS_CODE) {
            prizelist.map((item) => {
              item.btnName = '去使用'
              item.img = ''
              item.showGoApp = false
              item.wm =
                'P00000025024,中国移动APP每日签到_中奖记录页_签到奖励_流量_去使用'
              item.wm = this.isCheckin(item.keyword)
                ? 'P00000025024,中国移动APP每日签到_中奖记录页_签到奖励_流量_去使用'
                : 'P00000025025,中国移动APP每日签到_中奖记录页_翻牌子奖励_流量_去使用'

              // 处理签到奖品
              if (item.alias === 'll') item.prizeType = 1
              if (item.alias === 'yw') item.prizeType = 6
              if (String(item.prizeType) === '12') {
                item.wm =
                  'P00000025024,中国移动APP每日签到_中奖记录页_签到奖励_能量_去使用'
                item.wm = this.isCheckin(item.keyword)
                  ? 'P00000025024,中国移动APP每日签到_中奖记录页_签到奖励_能量_去使用'
                  : 'P00000025025,中国移动APP每日签到_中奖记录页_翻牌子奖励_能量_去使用'
              }
              // 处理实物名单奖品
              if (String(item.prizeType) === '2') {
                console.log('我是实物名单', item)
                item.btnName = ''
              }
              // 配置链接
              if (item.bizJTUrl) {
                item.url = item.bizJTUrl
              }
              if (item.prizeName.indexOf('嗨购') > -1) {
                item.imgurl = require('../assets/turn-card/prize-hg.png')
                item.wm = this.isCheckin(item.keyword)
                  ? 'P00000025024,中国移动APP每日签到_中奖记录页_签到奖励_嗨购_去使用'
                  : 'P00000025025,中国移动APP每日签到_中奖记录页_翻牌子奖励_嗨购_去使用'
              } else if (item.prizeName.indexOf('低价流量月月得') > -1) {
                item.imgurl = require('../assets//turn-card/prize-yw-djl.png')
                item.wm = this.isCheckin(item.keyword)
                  ? 'P00000025024,中国移动APP每日签到_中奖记录页_签到奖励_低价流量月月得_去使用'
                  : 'P00000025025,中国移动APP每日签到_中奖记录页_翻牌子奖励_低价流量月月得_去使用'
              } else if (item.prizeName.indexOf('好礼多多') > -1) {
                item.imgurl = require('../assets//turn-card/prize-yw-hldd.png')
                item.wm = this.isCheckin(item.keyword)
                  ? 'P00000025024,中国移动APP每日签到_中奖记录页_签到奖励_好礼多多_去使用'
                  : 'P00000025025,中国移动APP每日签到_中奖记录页_翻牌子奖励_好礼多多_去使用'
              } else if (item.prizeName.indexOf('宽带') > -1) {
                item.imgurl = require('../assets//turn-card/prize-kd.png')
                item.wm = this.isCheckin(item.keyword)
                  ? 'P00000025024,中国移动APP每日签到_中奖记录页_签到奖励_宽带_去使用'
                  : 'P00000025025,中国移动APP每日签到_中奖记录页_翻牌子奖励_宽带_去使用'
              } else if (item.prizeName.indexOf('流量月包折扣') > -1) {
                item.imgurl = require('../assets//turn-card/prize-yw-yb.png')
                item.wm = this.isCheckin(item.keyword)
                  ? 'P00000025024,中国移动APP每日签到_中奖记录页_签到奖励_流量月包折扣_去使用'
                  : 'P00000025025,中国移动APP每日签到_中奖记录页_翻牌子奖励_流量月包折扣_去使用'
              }
            })
            this.prizeInfo = prizelist
          } else {
            sessionStorage['set' + 'Item']('result', result)
          }
        })
        .catch(() => {})
        .finally(() => {
          this.$loading.hide()
        })
    },
    dateFormat: dateFormat,
    isFlowType: isFlowType,
    // 获取类型描述
    getValue(item) {
      let text = getNum(item.prizeName) || ''
      if (isFlowType(item.prizeType)) {
        text = text + getUnit(item.prizeName)
      }
      return text || ''
    },
    // 获取类型描述
    getTypeText(type) {
      if (isFlowType(type)) return '流量'
      return '能量值'
    },
    // 打开使用规则
    openRule(type, prizeId, item) {
      jtWebtrends1.multiTrack(
        'P00000025026',
        '中国移动APP每日签到_中奖记录页_使用规则'
      )
      let ruleText = yw_rule
      if(item.prizeDesc.indexOf('日历') > -1) {
        ruleText = sw_rule
      } else if (isFlowType(type)) {
        ruleText = ll_rule
      } else if (String(type) === '12') {
        ruleText = nl_rule
      } else {
        console.log(type, prizeId)
        getJTUsageRule({ prizeId }).then((res) => {
          if (res.result === 0 && res.usage_rule !== null) {
            ruleText = res.usage_rule
          } else {
            ruleText = swmd_rule
          }
          this.useRulePopup(ruleText)
        })
        return
      }
      this.useRulePopup(ruleText)
    },
    getImgUrl(item) {
      if (item.imgurl) {
        return item.imgurl
      }
      if (item.img) {
        return item.img
      }
      // 配置图片
      if (item.prizeName.indexOf('5MB') > -1) {
        return require('../assets/checkin/prize-ll-5.png')
      }
      if (item.prizeName.indexOf('10MB') > -1) {
        return require('../assets/checkin/prize-ll-10.png')
      }
      if (item.prizeName.indexOf('30MB') > -1) {
        return require('../assets/checkin/prize-ll-30.png')
      }
      if (item.prizeName.indexOf('50MB') > -1) {
        return require('../assets/checkin/prize-ll-50.png')
      }
      if (item.prizeName.indexOf('100MB') > -1) {
        return require('../assets/checkin/prize-ll-100.png')
      }
      if (item.prizeName.indexOf('1GB') > -1) {
        return require('../assets/checkin/prize-ll-1.png')
      }
    },
    goBack() {
      jtWebtrends1.multiTrack(
        'P00000025028',
        '中国移动APP每日签到_中奖记录页_返回'
      )
      this.$router.back()
    },
    // 卡券订阅
    openWXMi() {
      jtWebtrends1.multiTrack(
        'P00000025023',
        '中国移动APP每日签到_中奖记录页_订阅提醒'
      )
      const path =
        'pages/act/coupon/assistance/subscribe?from=app&keyword=ActSignIn2022&misdnmask=' +
        sessionStorage.getItem('misdnmask') +
        '&appToken=' +
        sessionStorage.getItem('userToken') +
        '&backAppUrl=' +
        window.ACT_PATH
      const jsonObject = {
        username: 'gh_4dd6b7e7ef37',
        url: path,
        path
      }
      this.launchwxmini(jsonObject)
    }
  }
}
</script>

<style lang="scss" scoped>
@import '~@/projects/checkin-new/styles/mixin.scss';
.prize {
  height: 100%;
  overflow-y: auto;
  background-color: #fff;
  position: relative;
  &-bg {
    position: absolute;
    top: 0;
    width: 100%;
    height: 313px;
    @include bg('prize/bg-up.png');
    z-index: 0;
  }
  .title {
    position: absolute;
    top: -90px;
    left: 0;
    width: 441px;
    height: 55px;
    font-size: 55px;
    font-weight: bold;
    letter-spacing: 6px;
    color: #ffffff;
  }

  &-list {
    width: 93%;
    height: calc(100% - 420px);
    background-color: #fffdfe;
    margin: 142px auto 0;
    border: 2px solid #fff;
    position: relative;
    padding-top: 43px;
    padding-bottom: 10px;
    background-color: #ffffff;
    box-shadow: 0px 2px 16px 0px rgba(156, 5, 0, 0.1);
    border-radius: 20px;
    padding-top: 40px;
    .illustration {
      position: absolute;
      top: -140px;
      right: 0;
      width: 211px;
      height: 176px;
      @include bg('prize/top.png', 100%, 100%);
    }
    &-content {
      width: 620px;
      height: calc(100% - 10px);
      margin: 0 auto;
      overflow-y: scroll;
      .no-list {
        font-size: 36px;
        color: #c0220b;
        margin-top: 200px;
        text-align: center;
      }
      .item {
        width: 100%;
        background-color: #fff8f9;
        border-radius: 20px;

        @include flex(flex-start);
        align-items: center;
        justify-content: space-evenly;
        margin: 20px 0px 20px;
        position: relative;
        &-left {
          overflow: hidden;
          position: relative;
          width: 180px;
          height: 140px;
          border-radius: 8px;
          background-color: #ffe9de;
          @include flex(center, center);
          font-family: PingFangSC-Regular;
          font-size: 28px;
          font-weight: bold;
          font-stretch: normal;
          color: #f3544b;
          z-index: 1;
          .checkin {
            position: absolute;
            width: 100%;
            height: 100%;
            z-index: -1;
            @include bg('prize/checkin.png');
          }
          .card {
            @include bg('prize/card.png');
          }
          .num {
            font-family: DIN-Bold;
            font-size: 45px;
            font-stretch: normal;
            letter-spacing: 0px;
            line-height: 60px;
            color: #f3544b;
          }
          img {
            max-width: 80%;
            max-height: 80%;
          }
        }
        &-right {
          flex: 1;
          height: 130px;
          display: flex;
          // align-items: center;
          flex-direction: column;
          justify-content: space-around;
          text-align: left;
          margin-left: 20px;
          padding: 10px 0;
          &__name {
            color: #000;
            font-size: 28px;
            font-weight: bold;
          }
          &__time {
            font-size: 22px;
            color: #999999;
          }
          &__rule {
            width: 131px;
            height: 32px;
            // @include bg("prize/rule.png");
            @include bg('prize/prize-rule.png');
          }
        }
        &-btn {
          width: 130px;
          height: 60px;
          font-size: 22px;
          text-align: center;
          line-height: 52px;
          border: none;
          color: #ffffff;
          position: relative;
          top: 10px;
          right: 15px;
          @include bg('prize/btn-bg.png');
        }
      }
    }
  }
  &-tips {
    height: 100px;
    font-size: 24px;
    line-height: 36px;
    text-align: left;
    color: #666666;
    margin: 15px 20px 15px 55px;
    &__title {
      font-weight: bold;
      color: #000000;
    }
  }
  .footer {
    display: flex;
    justify-content: space-evenly;
    align-items: center;
    width: 100%;
    height: 136px;
    color: #ffffff;
    font-size: 32px;
    line-height: 80px;
    letter-spacing: 4px;
    box-shadow: 0px 0px 16px 0px rgba(161, 161, 161, 0.35);
    position: absolute;
    bottom: 0;
    &__back {
      width: 304px;
      height: 90px;
      @include bg('prize/back.png');
    }
    &__subscription {
      width: 304px;
      height: 90px;
      font-size: 32px;
      @include bg('prize/subscription.png');
    }
  }
}
</style>
