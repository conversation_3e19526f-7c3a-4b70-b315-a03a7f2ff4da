<!--
 * @Author: z<PERSON><PERSON><PERSON><PERSON> zheng<PERSON><PERSON>@aspirecn.com
 * @Date: 2025-03-03 14:22:12
 * @LastEditors: zhengwen<PERSON> <EMAIL>
 * @LastEditTime: 2025-03-28 17:38:25
 * @FilePath: \task-tree\src\views\update-family.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <div class="update">
    <img class="update-img" src="../assets/common/icon-fj.png" alt="">
    <p class="update-title">加载中，请稍后...</p>
    <p class="update-desc">稍等一下，马上就好~</p>
  </div>
</template>
<script>
import { agreeUpgrade } from '../api/index'
import { newWebview } from '../../../../../../../bjapp-model/vue2/js/jt-app-ability'
import { mapMutations, mapState } from 'vuex'
const errText = '活动太火爆了，请稍后再试~'
export default {
  activated() {
    this.agreeUpgrade()
  },
  computed: {
    ...mapState({
      isMigrate: state => state.isMigrate,
      fmlUpgradeMsg: state => state.fmlUpgradeMsg
    })
  },
  methods: {
    ...mapMutations(['UPDATE_ISMIGRATE']),
    goJTTJ() {
      newWebview(this.fmlUpgradeMsg.url)
      setTimeout(() => {
        this.$router.go(-1)
      }, 1000)
    },
    agreeUpgrade() {
      if (this.isMigrate === 1) {
        this.goJTTJ()
        return
      }
      agreeUpgrade({}).then((res) => {
        if (res.result === '0') {
          this.goJTTJ()
          this.UPDATE_ISMIGRATE(1)
        } else {
          this.$toast(res.errmsg || errText)
        }
      }).catch((err) => {
        this.$toast(err.errmsg || errText)
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.update {
  display: flex;
  min-height: 100vh;
  width: 100vw;
  justify-content: center;
  align-items: center;
  flex-direction: column;
  background: #FFFFFF;
  &-img {
    width: 299px;
    height: 300px;
    margin-bottom: 40px;
  }
  &-title {
    font-size: 32px;
    line-height: 40px;
    color: #000000;
    margin-bottom: 18px;
  }
  &-desc {
    font-weight: 0;
    font-size: 24px;
    color: #999999;
  }
}
</style>
