<template>
  <div v-if="show" class="loading">
    <div class="content">
      <img :src="path" />
      <!--<p>加载中...</p>-->
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      show: false,
      path: require('./img/loading.gif')
    }
  },
  created() {}
}
</script>

<style lang="scss" scoped>
.loading {
  position: fixed;
  left: 0;
  top: 0;
  right: 0;
  bottom: 0;
  background: #000;
  z-index: 1001;
  .content {
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    color: #fff;
    p {
      font-size: 30px;
      padding-top: 10px;
      text-align: center;
    }
    img {
      display: block;
      width: 400px;
      height: 400px;
      margin: 0 auto;
    }
  }
}
</style>
