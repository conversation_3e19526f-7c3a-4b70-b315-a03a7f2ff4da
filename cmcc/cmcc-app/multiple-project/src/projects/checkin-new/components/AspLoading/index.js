import AspLoadingComponents from './index.vue'

const AspLoading = {}
AspLoading.install = Vue => {
    const AspLoadingConstructor = Vue.extend(AspLoadingComponents)
    const instance = new AspLoadingConstructor()
    instance.$mount(document.createElement('div'))
    document.body.appendChild(instance.$el)
    Vue.prototype.$aspLoading = {
        show () {
            instance.show = true
        },
        hide () {
            instance.show = false
        }
    }
}
export default AspLoading
