<template>
  <div v-if="params.show" class="asp-dialog-two">
    <div class="asp-dialog-two-content">
      <div class="asp-dialog-two-content__title">
        <span class="icon-dui" :class="params.icon"></span>
        {{ params.title }}
      </div>
      <p v-if="params.content" class="asp-dialog-two-content__content">{{ params.content }}</p>
      <div v-if="params.imgurl" class="asp-dialog-two-content__imgurl">
        <img :src="params.imgurl" alt="" />
      </div>
      <p v-if="params.desc" class="asp-dialog-two-content__desc">{{ params.desc }}</p>
      <div class="asp-dialog-two-content__btn" @click="btnClick">{{ params.btnText || '知道了' }}</div>
      <div class="asp-dialog-two-content__close" @click="hide"></div>
    </div>
  </div>
</template>
<script>
export default {
  props: {
    params: {
      type: Object,
      default: () => {}
    }
  },
  methods: {
    btnClick() {
      if(this.params.callBack) {
        this.params.callBack()
      } else {
        this.hide()
      }
    },
    hide() {
      this.$emit('update:params', { ...this.params, show: false })
    }
  }
}
</script>
<style lang="scss" scoped>
.asp-dialog-two {
  position: fixed;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 15;
  overflow: hidden;
  &-content {
    width: 600px;
    min-height: 666px;
    text-align: center;
    position: absolute;
    left: 50%;
    top: 47%;
    transform: translate(-50%, -50%);
    background-color: #ffffff;
    border-radius: 24px;
    &__title {
      width: 100%;
      height: 150px;
      background: url("~@/projects/checkin-new/components/AspDialog/img/bg-title.png");
      background-size: 100% 100%;
      font-size: 50px;
      font-weight: bold;
      line-height: 150px;
      letter-spacing: 0px;
      text-align: center;
      color: #ffffff;
      margin-bottom: 10px;
      border-radius: 24px 24px 0 0;
      background-color: #ffffff;
      display: flex;
      justify-content: center;
      align-items: center;
      text-shadow: 3px 2px #fcc3b6;
      .icon-dui {
        display: inline-block;
        width: 123px;
        height: 121px;
        background: url("~@/projects/checkin-new/components/AspDialog/img/dui.png");
        background-size: 100% 100%;
        margin-right: 10px;
        position: relative;
        top: 10px;
        &.lingdang {
          background: url("~@/projects/checkin-new/components/AspDialog/img/lingdang.png") no-repeat center top;
          background-size: 89px 94px;
        }
        &.liwu {
          background: url("~@/projects/checkin-new/components/AspDialog/img/liwu.png") no-repeat center -10px;
          background-size: 119px 116px;
        }
      }
    }
    &__content {
      text-align: center;
      font-size: 36px;
      line-height: 48px;
      color: #333333;
      margin-top: 50px;
      font-weight: bold;
    }
    &__imgurl {
      margin: 10px auto auto;
      img {
        height: 160px;
      }
    }
    &__desc {
      margin: 10px auto auto;
      width: 508px;
      height: 84px;
      line-height: 84px;
      text-align: center;
      background-color: #fff6e6;
      border-radius: 10px;
      font-size: 30px;
      color: #fda800;
    }
    &__btn {
      margin: 29px auto auto;
      width: 330px;
      height: 72px;
      line-height: 72px;
      text-align: center;
      background-image: linear-gradient(9deg,rgba(255, 73, 69, 1) 0%,#ff846d 100%), linear-gradient(#ff9962, #ff9962);
      box-shadow: 0px 4px 8px 0px  rgba(255, 60, 60, 0.34);
      border-radius: 36px;
      font-size: 32px;
      color: #ffffff;
    }
    &__close {
      width: 79px;
      height: 79px;
      background: url("~@/projects/checkin-new/components/AspDialog/img/close.png");
      background-size: 100% 100%;
      position: absolute;
      bottom: -110px;
      left: 50%;
      transform: translateX(-50%);
    }
  }
}
</style>
