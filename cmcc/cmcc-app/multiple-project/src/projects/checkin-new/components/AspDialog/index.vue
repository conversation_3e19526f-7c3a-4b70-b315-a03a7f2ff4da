<template>
  <div v-if="show" class="common-popup" @touchmove.prevent>
    <div class="common-popup-main" :class="{'have-prize-two': params.prizeObjTwo}" v-if="!params.isSimple">
      <div class="common-popup__title">
        <span class="icon-dui"></span>
        {{ params.title }}
      </div>
      <div v-if="params.closeBtn" class="close" @click.stop="hide" />
      <div class="common-popup-content" :class="params.styleClass">
        <div :style="params.dialogStyle ? params.dialogStyle : ''" @click="imgClick($event)" v-html="params.content" />
        <!-- 双奖励 -->
        <div class="common-popup-prize" v-if="params.prizeObjTwo">
          <p class="top-desc">{{ params.topDesc }}</p>
          <div class="prize__main--1">
            <div class="prize__img--1 first">{{ params.prizeObj.prizeName }}</div>
            <div  class="prize__img--1">{{ params.prizeObjTwo.prizeName }}</div>
          </div>
        </div>
        <!-- 签到单奖励奖励 -->
        <div class="common-popup-prize" v-else-if="params.type==='prizePopup'">
          <p class="top-desc">{{ params.topDesc }}</p>
          <div class="prize__main--1">
            <div class="prize__img--1 first1">{{ params.prizeObj.prizeName }}</div>
          </div>
        </div>
        <!-- 单个奖励 -->
        <div class="common-popup-prize" v-else-if="params.prizeObj">
          <p class="top-desc">{{ params.topDesc }}</p>
          <div class="prize__main">
            <img :src="params.prizeObj.imgurl" alt="" class="prize__img" />
            <!-- <img v-if="params.prizeObjTwo" :src="params.prizeObjTwo.imgurl" alt="" class="prize__img" /> -->
          </div>
          <p class="desc" v-if="!params.noShowDesc">{{ params.desc || ('恭喜您获得签到奖励' + params.prizeObj.prizeName) }}</p>
        </div>
      </div>
      <div class="common-popup__btn" @click="btnFun" v-if="params.showBtn">
        {{ params.btn }}
      </div>
      <div class="common-popup__banner" v-if="banner.url">
        <img :src="banner.imgurl" alt="" @click="jumpview(banner)" />
      </div>
      <p v-if="params.prizeObj && params.prizeObj.prizeType !== 5" class="popup-manyidu-tips">感谢您的参与，我们期待您的10分好评！<br />祝您生活愉快！</p>
    </div>
    <div v-else class="simple">
      <div class="simple__title">{{ params.title }}</div>
      <div v-if="params.closeBtn" class="simple__close" @click.stop="hide"></div>
      <div class="common-popup-content" :class="params.styleClass">
        <div :style="params.dialogStyle ? params.dialogStyle : ''" @click="imgClick($event)" v-html="params.content" />
      </div>
      <div class="common-popup__btn" @click="btnFun" v-if="params.showBtn">
        {{ params.btn }}
      </div>
    </div>
  </div>
</template>

<script>
import { BANNER_LIST } from '@/projects/checkin-new/config/index'
import jtWebtrends1 from '@/utils/jtWebtrends'
import { newWebview } from '../../../../../../../../bjapp-model/vue2/js/jt-app-ability'

export default {
  data() {
    return {
      show: false,
      params: {},
      banner: {}
    }
  },
  watch: {
    'params.currentTime': {
      handler(val) {
        if (val) {
          const day = new Date(this.params.currentTime).getDay()
          const index = day === 0 ? 6 : day - 1
          const bannerlist = JSON.parse(sessionStorage.getItem('BANNER_LIST') || '[]')
          this.banner = bannerlist[index] || {}
          this.banner.chama = BANNER_LIST[index].chama
        }
      },
      immediate: true,
      deep: true
    }
  },
  created() {},
  methods: {
    btnFun() {
      this.show = false
      this.params.callBack && this.params.callBack()
    },
    hide() {
      this.show = false
      this.params.closeCallBack && this.params.closeCallBack()
    },
    imgClick(e) {
      if (e.target.className.indexOf('banner') > -1) {
        this.show = false
        this.params.imgClickCallBack && this.params.imgClickCallBack()
      }
    },
    // banner 跳转
    jumpview(item) {
      this.params.imgClickCallBack && this.params.imgClickCallBack()
      const chama = this.params.prizeObj ? item.chama.qdP : item.chama.qdNP
      const code = chama.split(',')
      jtWebtrends1.multiTrack(code[0], code[1], { nextUrl: item.url })
      newWebview(item.url)
    }
  }
}
</script>

<style lang="scss" scoped>
@import "~@/projects/checkin-new/styles/mixin.scss";
.common-popup {
  position: fixed;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 15;
  overflow: hidden;
  &-main {
    width: 600px;
    background-color: #ffffff;
    border-radius: 24px;
    max-height: 80vh;
    text-align: center;
    position: absolute;
    left: 50%;
    top: 47%;
    transform: translate(-50%, -50%);
    padding-bottom: 30px;
  }
  &-content {
    @include box-text(32px, #000000, 48px);
    width: 100%;
    max-height: 55vh;
    overflow-y: auto;
    padding: 20px;
    min-height: 99px;
    padding: 3% 5.5%;
  }
  &__title {
    width: 100%;
    height: 150px;
    background: url("~@/projects/checkin-new/components/AspDialog/img/bg-title.png");
    background-size: 100% 100%;
    font-size: 50px;
    font-weight: normal;
    font-stretch: normal;
    line-height: 150px;
    letter-spacing: 0px;
    text-align: center;
    color: #ffffff;
    margin-bottom: 10px;
    border-radius: 24px 24px 0 0;
    background-color: #ffffff;
    @include flex(center, center);
    .icon-dui {
      display: inline-block;
      width: 123px;
      height: 121px;
      background: url("~@/projects/checkin-new/components/AspDialog/img/dui.png");
      background-size: 100% 100%;
      margin-right: 10px;
      position: relative;
      top: 10px;
    }
  }
  &__btn {
    position: relative;
    @include btn();
    margin: 30px auto;
  }
  &-prize {
    text-align: center;
    margin: 10px auto 0;
  }
  .prize {
    &__main {
      display: flex;
      justify-content: space-evenly;
    }
    &__img {
      width: 200px;
      height: 200px;
    }
    &__main--1 {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
    }
    &__img--1 {
      width: 533px;
      height: 150px;
      line-height: 150px;
      background: url('./img/banner.png') no-repeat center center;
      background-size: 100% 100%;
      font-size: 36px;
      color: #f3544b;
      text-align: center;
      font-weight: bold;
      padding-left: 180px;
      padding-right: 30px;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
      &.first {
        margin-bottom: 20px;
      }
      &.first1 {
        margin-bottom: -20px;
      }
    }
    .desc {
      margin-top: 17px;
    }
    .top-desc {
      margin-bottom: 17px;
    }
  }
  &__banner {
    width: 547px;
    height: 160px;
    margin: 30px auto 0;
    img {
      width: 100%;
      height: 100%;
    }
  }
  .close {
    width: 79px;
    height: 79px;
    background: url("~@/projects/checkin-new/components/AspDialog/img/close.png");
    background-size: 100% 100%;
    position: absolute;
    bottom: -110px;
    left: 50%;
    transform: translateX(-50%);
  }
  .have-prize-two {
    .common-popup__btn {
      margin: 5px auto;
    }
    .common-popup__banner {
      margin-top: 15px;
    }
    .popup-manyidu-tips {
      margin-top: 20px;
    }
  }
}
.simple {
  max-height: 80vh;
  text-align: center;
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  overflow: hidden;
  width: 600px;
  background-color: #ffffff;
  border-radius: 24px;
  font-size: 32px;
  font-weight: normal;
  font-stretch: normal;
  line-height: 48px;
  letter-spacing: 0px;
  color: #000000;

  &__title {
    font-size: 32px;
    line-height: 100px;
    letter-spacing: 0px;
    font-weight: bold;
    border-bottom: 1px solid rgba(#000000, 0.1);
  }
  &__close {
    position: absolute;
    top: 40px;
    right: 40px;
    width: 25px;
    height: 25px;
    background: url("~@/projects/checkin-new/components/AspDialog/img/close_x.png");
    background-size: 100% 100%;
    font-weight: bold;
    color: #333333;
  }
}
.upappp-pop {
  @include flex(center, center);
}
.rule-pop {
  height: 60%;
  /deep/.text {
    color: #150e0e;
    line-height: 45px;
    span {
      font-weight: bold;
    }
    .tips {
      padding-left: 20px;
      color: #444;
    }
  }
}
.popup-manyidu-tips {
  min-width: fit-content;
  width: 80%;
  margin: auto;
  border-radius: 15px;
  line-height: 34px;
  padding: 5px 15px;
  font-size: 21px;
  background: #fdf1ee;
  color: #f3544a;
  margin: 10px auto;
  text-align: center;
}
</style>
