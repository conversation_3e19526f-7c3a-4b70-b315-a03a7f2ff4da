<template>
  <section v-if='showLuckyGrid && show' class='index-lucky-grid index-common-model'>
    <div class='index-common-model__content index-lucky-grid__content'>
      <div class='lucky-grid' :width='width' :height='height'>
        <LuckyGrid
          ref='myLucky'
          :width='width'
          :height='height'
          :prizes='prizes'
          :blocks='blocks'
          :buttons='buttons'
          @start='startCallback'
          @end='endCallback'
        />
        <img
          class='ljcj-gif'
          :class="isShowAtn ? 'show-atn' : 'active'"
          src='./image/ljcj.png'
          alt=''
          @click='startCallback'
        />
      </div>
      <div class="close" @click="close">
        ×
      </div>
    </div>
  </section>
</template>

<script>
import { customPrize } from '../../api/index'
import JTFK from '../../utils/JTFK'
import { mapState } from 'vuex'
const cellImg = {
  src: require('./image/item-bg.png'),
  activeSrc: require('./image/active-item-bg.png'),
  width: '100%',
  height: '100%'
}
const xxcyImg = {
  width: '80%',
  top: '5%',
  height: '85%'
}
export default {
  props: {
    show: {
      type: Boolean,
      default: false
    },
    prizelist: {
      type: Array,
      default: () => {}
    }
  },
  data() {
    return {
      result: -1,
      activePrize: [],
      width: '',
      height: '',
      showLuckyGrid: false,
      blocks: [
        {
          borderRadius: '10px',
          imgs: [
            {
              src: require('./image/bg.png'),
              width: '100%',
              height: '100%'
            }
          ]
        }
      ],
      prizes: [
        {
          x: 0,
          y: 0,
          imgs: [],
          index: 0
        },
        {
          x: 1,
          y: 0,
          imgs: [],
          index: 1
        },
        {
          x: 2,
          y: 0,
          imgs: [],
          index: 2
        },
        {
          x: 2,
          y: 1,
          imgs: [],
          index: 3
        },
        {
          x: 2,
          y: 2,
          imgs: [],
          index: 4
        },
        {
          x: 1,
          y: 2,
          imgs: [],
          index: 5
        },
        {
          x: 0,
          y: 2,
          imgs: [],
          index: 6
        },
        {
          x: 0,
          y: 1,
          imgs: [],
          index: 7
        }
      ],
      buttons: [
        {
          x: 1,
          y: 1,
          borderRadius: '5px'
        }
      ],
      defaultStyle: {
        background: '#b8c5f2'
      },
      prizeLoading: false,
      timer: null,
      isShowAtn: true
    }
  },
  computed: {
    ...mapState({
      fingerprintUrl: state => state.fingerprintUrl,
      fingerprintId: state => state.fingerprintId,
    })
  },
  mounted() {
    this.prizes.forEach((item, index) => {
      const active = this.prizelist[index]
      item = Object.assign(item, active)
      // if (active.prizeType === 5) {
      //   item.imgs = [cellImg, {
      //     src: active.imgurl,
      //     ...xxcyImg
      //   }]
      // } else {
      item.imgs = [
        cellImg, {
          src: active.imgurl,
          width: '86%',
          top: '7%',
          height: '86%'
        }
      ]
      // }
    })
    const luckyGridWidth = window.innerWidth * 0.75 - 35
    this.width = luckyGridWidth + 'px'
    this.height = luckyGridWidth + 'px'
    this.showLuckyGrid = true
  },
  destroyed() {
    clearTimeout(this.timer)
  },
  methods: {
    // 点击抽奖按钮会触发star回调
    async startCallback() {
      this.isShowAtn = false
      this.$loading.waitShow()
      let constid = ''
      if(this.fingerprintUrl && this.fingerprintId) {
        constid = await JTFK({ appId: this.fingerprintId, server: this.fingerprintUrl })
      }
      customPrize({ type: 'signFull', constid }).then(res => {
        const { result, prizelist, errmsg } = res
        if (result === 0 && prizelist.length > 0) {
          this.activePrize = prizelist
          const active = this.prizes.filter(item => item.prizeName === prizelist[0].prizeName)
          this.$refs.myLucky.play()
          this.$refs.myLucky.stop((active && active[0].index) || 0)
        } else {
          this.showErrToast(errmsg)
        }
      }).catch(err => {
        this.showErrToast(err.errmsg)
      }).finally(() => {
        this.$loading.waitClose()
      })
    },
    // 抽奖结束会触发end回调
    endCallback() {
      if (this.activePrize.length > 0) {
        // 发奖成功
        this.timer = setTimeout(() => {
          this.$refs.myLucky.init()
          this.isShowAtn = true
          this.close()
          this.$emit('success', this.activePrize[0])
        }, 1000)
      }
    },
    close() {
      this.$refs.myLucky.init()
      this.$emit('update:show', false)
    },
    showErrToast(errmsg) {
      this.close()
      this.$toast(errmsg || '活动太火爆了，请稍后再试~')
    }
  }
}
</script>

<style lang='scss' scoped>
.index-lucky-grid {
  position: fixed;
  left: 0;
  top: 0;
  width: 100vw;
  height: 100vh;
  z-index: 100;
  background: rgba(0,0,0,.5);
  &__content {
    position: absolute;
    left: 50%;
    top: 40%;
    width: 638px;
    height: 935px;
    transform: translate(-50%, -50%);
    background: #ffffff;
    padding: 20px;
    background: url('./image/home-bg.png') no-repeat center center;
    background-size: 100% 100%;
    padding-top: 48.3%;
    .close {
      position: absolute;
      left: 50%;
      bottom: -90px;
      transform: translateX(-50%);
      width: 60px;
      height: 60px;
      border: 1px solid #ffffff;
      font-size: 50px;
      line-height: 60px;
      text-align: center;
      border-radius: 50%;
      color: #ffffff;
    }
  }
}
.lucky-grid {
  position: relative;
  > div {
    margin: auto;
  }
  .ljcj-gif {
    position: absolute;
    top: 34%;
    left: 36.5%;
    width: 27%;
    height: 32%;
    &.show-atn {
      animation: big-small-always 0.5s infinite;
      animation-direction: alternate-reverse;
      animation-delay: 0.1s;
    }
    &.active {
      animation: big-small 0.5s;
    }
  }
}
@keyframes big-small-always {
  0% {
    transform: scale(0.78);
  }
  100% {
    transform: scale(1);
  }
}
@keyframes big-small {
  0% {
    transform: scale(0.7);
  }
  50% {
    transform: scale(1.06);
  }
  100% {
    transform: scale(1);
  }
}
</style>
