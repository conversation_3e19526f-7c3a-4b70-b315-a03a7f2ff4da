<template>
  <div v-if="region && region.block && region.block.length >= 4" class="actsignin2023-icon">
    <van-swipe :autoplay="3000">
      <template v-for="item in list">
        <van-swipe-item v-if="item.length === 4" class="actsignin2023-icon__swipe">
          <img class="swipe-img" v-for="(iItem, iIndex) in item" :src="iItem.imgurl" alt="" :key="iIndex" @click="gotoBannerUrl(iItem)"/>
        </van-swipe-item>
      </template>
    </van-swipe>
  </div>
</template>
<script>
import { newWebview } from '../../../../../../../../bjapp-model/vue2/js/jt-app-ability'
import { checkBjUser } from '@/projects/checkin-new/utils/utils'
export default {
  props: {
    region: {
      type: Object,
      default: () =>{}
    }
  },
  data() {
    return {
      list: []
    }
  },
  watch: {
    region(newVar) {
      if(newVar && newVar.block) {
        this.list = []
        newVar.block.forEach((item, index) => {
          const i = Math.floor(index / 4)
          this.list[i] = this.list[i] || []
          this.list[i].push(item)
        })
      }
    }
  },
  methods: {
    gotoBannerUrl(item) {
      if (checkBjUser()) newWebview(item.url)
    }
  }
}
</script>
<style lang="scss" scoped>
.actsignin2023-icon {
  width: 100%;
  padding: 15px;
  background-color: #fff3f5;
	box-shadow: inset 0px 0px 10px 0px  rgba(217, 48, 36, 0.06);
	border-radius: 17px;
  margin-top: 20px;;
  &__swipe {
    display: flex;
    justify-content: space-between;
    .swipe-img {
      width: 148px;
      height: 148px;
    };
  }
}
</style>
