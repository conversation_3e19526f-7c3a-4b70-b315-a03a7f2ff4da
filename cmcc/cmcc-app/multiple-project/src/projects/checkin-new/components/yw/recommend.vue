<template>
  <section class="recommend">
    <div class="recommend__head"></div>
    <div class="recommend-content">
      <div v-for="(item, index) in recommendList" :key="index" class="recommend-item" @click="goActive(item)">
        <img v-lazy="item.imgUrl" alt="" class="recommend-item__img" />
        <div class="recommend-item__name">{{ item.title }}</div>
      </div>
    </div>
  </section>
</template>

<script>
import { goUrl } from '@/projects/checkin-new/utils/utils'
import { recommendParams } from '@/projects/checkin-new/config/index'
import Webtrends1 from '@/utils/webtrends'

export default {
  props: {
    tuijianInfo: {
      type: Object,
      default() {
        return {}
      }
    },
    // 是否已经办理移动福利社
    isOrder: {
      type: Boolean,
      default: false
    },
    currentDate: {
      type: Number,
      default: 0
    }
  },
  data() {
    return {
      recommendList: []
    }
  },
  watch: {
    tuijianInfo: {
      handler(val) {
        const {
          isNewUser, // 是否新人
          isEinvoice, // 是否有新人专区-电子发票标签
          isVoice, // 语音标签
          isBroadband // 宽带标签
        } = val
        this.recommendList = [
          // 超值购机
          recommendParams.mobileTerminal,
          this.getWZ2(),
          this.getWZ3(isNewUser, isEinvoice),
          this.getWZ4(isVoice, isBroadband)
        ]
      },
      immediate: true,
      deep: true
    },
    isOrder: {
      handler(val) {
        if (val) {
          this.$set(this.recommendList, 1, this.getWZ2())
        }
      },
      immediate: true
    }
  },
  mounted() {},
  methods: {
    goActive(item) {
      Webtrends1.multiTrack(item.chama)
      goUrl(item.url)
    },
    getWZ2() {
      if (!this.isOrder) {
        // 福利社专区
        return recommendParams.welfare
      }
      const date = new Date(this.currentDate).getDate()
      const isSisToday = ['6', '16', '26'].some((item) => item === String(date))
      if (isSisToday) {
        // 充值入口
        return recommendParams.recharge
      }
      // 号卡专区
      return recommendParams.numberCard
    },
    getWZ3(isNewUser, isEinvoice) {
      if (isNewUser) {
        // 新人专区
        return recommendParams.newerArea
      }
      if (isEinvoice) {
        // 电子发票
        return recommendParams.invoice
      }
      // 电子月报
      return recommendParams.monthlyReport
    },
    getWZ4(isVoice, isBroadband) {
      if (isVoice) {
        // 语言业务
        return recommendParams.languageBusiness
      }
      if (isBroadband) {
        // 宽带业务
        return recommendParams.broadbandBusiness
      }
      // 视频营业厅
      return recommendParams.videoBusinessHall
    }
  }
}
</script>

<style lang="scss" scoped>
@import "~@/projects/checkin-new/styles/mixin.scss";
.recommend {
  width: 700px;
  background-color: #ffffff;
  border-radius: 24px;
  margin: 0 auto;
  padding: 20px 35px;
  margin-bottom: 20px;
  &__head {
    @include background(280px, 56px, "index/title.png");
    margin: 0 auto 20px;
    &__icon {
      display: inline-block;
      margin-right: 20px;
    }
  }
  &-content {
    width: 100%;
    @include flex(space-between, center);
    flex-wrap: wrap;
  }
  &-item {
    text-align: center;
    padding: 12px 15px;
    width: 300px;
    background-color: #ffffff;
    box-shadow: 0px 2px 16px 0px rgba(207, 113, 110, 0.14);
    border-radius: 16px;
    margin-bottom: 30px;

    &__img {
      width: 270px;
      height: 124px;
      background-color: #f7f7f7;
      border-radius: 10px;
      margin: 0 auto;
    }
    &__name {
      font-size: 30px;
      font-weight: bold;
      font-stretch: normal;
      line-height: 44px;
      letter-spacing: 0px;
      color: #000000;
      text-align: center;
      font-family: SourceHanSansCN-Medium;
      @include ellipsis(1);
    }
  }
}
</style>
