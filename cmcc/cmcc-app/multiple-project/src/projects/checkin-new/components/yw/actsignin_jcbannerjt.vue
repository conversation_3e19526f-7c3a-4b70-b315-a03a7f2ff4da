<!--
 * @Author: <PERSON><PERSON><PERSON><PERSON> <EMAIL>
 * @Date: 2023-01-05 16:46:33
 * @LastEditors: luobingxin <EMAIL>
 * @LastEditTime: 2024-03-26 14:53:35
 * @FilePath: \my-series\cmcc\cmcc-app\multiple-project\src\projects\checkin-new\views\components\business-floor.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <section class="businessFloor" v-if="businessFloorDataSign.length || businessFloorDataOperate.length">
    <div class="businessFloor__head index-icons"></div>
    <div class="businessFloor__banner">
      <div class="businessFloor__swipe">
        <van-swipe class="my-swipe" :autoplay="3000" indicator-color="white">
          <van-swipe-item v-for="(item,index) in businessFloorDataSign" :key="item.opid">
            <img class="banner" v-lazy="item.imgurl" @click="goUrl(item, index, 'swipe')" />
          </van-swipe-item>
        </van-swipe>
      </div>
    </div>
    <div class="businessFloor__content">
      <div v-for="(item,index) in businessFloorDataOperate" :key="item.opid" class="businessFloor__item" @click="goUrl(item, index)">
        <img v-lazy="item.imgurl" alt="" class="businessFloor__item__img" />
        <div class="businessFloor__item__name">{{ item.title }}</div>
      </div>
    </div>
  </section>
</template>

<script>
import jtWebtrends1 from '@/utils/jtWebtrends'
export default {
  props: {
    businessFloorDataSign: {
      type: Array,
      default() {
        return []
      }
    },
    businessFloorDataOperate: {
      type: Array,
      default() {
        return []
      }
    }
  },
  data() {
    return {}
  },
  mounted() { },
  methods: {
    /**
     * @description: 跳转到对应页面
     * @param {*} item 当前传入的对象
     * @return {*}
     */
    goUrl(item, index, type = '') {
      let event = ''
      let eventName = ''
      if(type === 'swipe') {
        event = 'P00000090564'
        eventName = `中国移动APP每日签到_精彩推荐_轮播运营位${index}_${item.title}`
      } else {
        event = 'P0000009056' + (1 + index)
        eventName = `中国移动APP每日签到_精彩推荐_运营位${index}_${item.title}`
      }
      jtWebtrends1.multiTrack(event, eventName)
      if (!item.url) return
      setTimeout(() => {
        window['loca' + 'tion'].href = item.url
      }, 200)
    }
  }
}
</script>

<style lang="scss" scoped>
@import "~@/projects/checkin-new/styles/mixin.scss";

.businessFloor {
  width: 700px;
  background-color: #ffffff;
  border-radius: 24px;
  margin: 0 auto;
  padding: 20px 35px;
  margin-bottom: 20px;

  &__banner {
    overflow: hidden;
    width: 100%;
    display: block;
    .banner {
      height: 140px;
      border-radius: 20px;
      width: 100%;
      display: block;
    }
  }
  &__head {
    width: 280px;
    height: 56px;
    background-position: -400px -500px;
    // @include background(280px, 56px, "index/title.png");
    margin: 0 auto 20px;

    &__icon {
      display: inline-block;
      margin-right: 20px;
    }
  }

  &__content {
    width: 100%;
    @include flex(space-between, center);
    flex-wrap: wrap;
  }

  &__item {
    text-align: center;
    padding: 12px 15px;
    width: 300px;
    background-color: #ffffff;
    box-shadow: 0px 2px 16px 0px rgba(207, 113, 110, 0.14);
    border-radius: 16px;
    margin-top: 30px;

    &__img {
      width: 270px;
      height: 124px;
      background-color: #f7f7f7;
      border-radius: 10px;
      margin: 0 auto;
    }

    &__name {
      font-size: 30px;
      font-weight: bold;
      font-stretch: normal;
      line-height: 44px;
      letter-spacing: 0px;
      color: #000000;
      text-align: center;
      font-family: SourceHanSansCN-Medium;
      @include ellipsis(1);
    }
  }
}
</style>
