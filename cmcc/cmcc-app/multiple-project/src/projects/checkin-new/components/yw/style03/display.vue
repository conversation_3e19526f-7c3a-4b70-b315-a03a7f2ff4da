<template>
  <!-- 轮播图 -->
  <van-swipe :autoplay="3000" class="style02" @change="onChange">
    <van-swipe-item v-for="image in baList" :key="image.coid">
      <img v-lazy="image.imgurl" class="style02__img" @click="toActive(image)" />
    </van-swipe-item>
  </van-swipe>
</template>

<script>
import { Swipe, SwipeItem, Lazyload } from 'vant'

import style from '../mixins/style'

export default {
  mixins: [style],
  components: {
    [Swipe.name]: Swipe,
    [SwipeItem.name]: SwipeItem,
    [Lazyload.name]: Lazyload
  },
  data() {
    return {
      styleNum: '02',
      current: 0
    }
  },
  mounted() {},
  methods: {
    onChange(index) {
      this.current = index
    }
  }
}
</script>
<style rel="stylesheet/scss" lang="scss" scoped>
.style02 {
  width: 100%;
  height: 200px;
  margin: 0 auto;
  background-color: rgba($color: #feefea, $alpha: 0.7);
  border-radius: 16px;
  position: relative;
  /deep/.van-swipe {
    &__indicator {
      width: 9px;
      height: 8px;
      background-image: linear-gradient(10deg, #c8c8c8 0%, #e7e7e7 100%), linear-gradient(#ff4a18, #ff4a18);
      background-blend-mode: normal, normal;
      border-radius: 4px;
      margin: 0 5px;
      opacity: 1;
      &--active {
        width: 16px;
        height: 8px;
        background-image: linear-gradient(10deg, #ff4945 0%, #ff846d 100%), linear-gradient(#ff4a18, #ff4a18);
        background-blend-mode: normal, normal;
        border-radius: 4px;
      }
    }
    &__indicators {
      bottom: 15px;
    }
  }
  &__img {
    width: 92%;
    height: 140px;
    background-color: #ffffff;
    border-radius: 12px;
    margin: 10px auto;
    position: absolute;
    top: 43%;
    left: 50%;
    transform: translate(-50%, -50%);
    img {
      width: 100%;
      height: 100%;
    }
  }
}
</style>
