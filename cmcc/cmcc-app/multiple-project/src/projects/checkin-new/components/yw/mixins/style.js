/*
 * @Author: z<PERSON><PERSON><PERSON><PERSON> zheng<PERSON><PERSON>@aspirecn.com
 * @Date: 2023-03-24 19:08:31
 * @LastEditors: zhengwen<PERSON> <EMAIL>
 * @LastEditTime: 2024-07-16 10:34:08
 * @FilePath: \multiple-project\src\projects\checkin-new\views\components\region\components\mixins\style.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
// import CHANNEL from '@/utils/channel'
import jtWebtrends1 from '@/utils/jtWebtrends'
import { checkBjUser } from '@/projects/checkin-new/utils/utils'
import { newWebview } from '../../../../../../../../../bjapp-model/vue2/js/jt-app-ability'

export default {
  props: {
    baList: {
      type: Array,
      default() {
        return []
      }
    }
  },
  data() {
    return {
    }
  },
  watch: {
  },
  mounted() {
  },
  methods: {
    toActive(item) {
      jtWebtrends1.multiTrack('P00000025021', `中国移动APP每日签到_流量直通车_${item.title}`, { nextUrl: item.url })
      if (!checkBjUser()) return
      newWebview(item.url)
    }
  }
}
