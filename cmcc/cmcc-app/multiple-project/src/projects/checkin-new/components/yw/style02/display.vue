<template>
  <section class="style02">
    <div v-for="(item, index) in baList" :key="index" class="style02-item"  @click="toActive(item)">
      <div class="style02-item__top">
        <img :src="item.imgurl" alt="" />
        <div class="style02-item__rignt">
          <p class="style02-item__title">
            {{ item.title }}
          </p>
          <p class="style02-item__sub">
            {{ item.subtitle }}
          </p>
        </div>
      </div>
      <button class="style02-item__btn">参与</button>
    </div>
  </section>
</template>

<script>
import style from '../mixins/style'

export default {
  mixins: [style],
  data() {
    return {
      styleNum: '02'
    }
  },
  mounted() {},
  methods: {}
}
</script>

<style lang="scss" scoped>
@import "~@/projects/checkin-new/styles/mixin.scss";
.style02 {
  width: 100%;
  margin: 0 auto;
  position: relative;
  margin: 0 auto;
  display: flex;
  justify-content: flex-start;
  align-items: center;
  text-align: center;
  overflow-x: auto;
  margin-top: 15px;
  &-item {
    flex-shrink: 0;
    position: relative;
    overflow: hidden;
    width: 270px;
    height: 160px;
    background-color: #ffffff;
    border-radius: 16px;
    margin-right: 10px;
    padding: 15px;
    &__top {
      display: flex;
    }
    img {
      width: 75px;
      height: 74px;
      border-radius: 12px;
      margin-right: 10px;
    }
    &__rignt {
      flex: 1;
      text-align: left;
    }
    &__title {
      display: block;
      width: 155px;
      font-family: PingFangSC-Medium;
      font-size: 26px;
      font-weight: bold;
      font-stretch: normal;
      line-height: 42px;
      letter-spacing: 0px;
      color: #000000;
      @include ellipsis(1);
    }
    &__sub {
      width: 155px;
      @include ellipsis(1);
      font-size: 10px;
      font-family: AlibabaPuHuiTi-Regular;
      font-size: 20px;
      font-weight: normal;
      font-stretch: normal;
      line-height: 36px;
      letter-spacing: 0px;
      color: rgba($color: #000000, $alpha: 0.79);
    }
    &__btn {
      position: absolute;
      bottom: 25px;
      right: 25px;
      width: 90px;
      height: 36px;
      border-radius: 18px;
      font-family: PingFangSC-Regular;
      font-size: 20px;
      font-weight: normal;
      font-stretch: normal;
      line-height: 36px;
      letter-spacing: 0px;
      color: #ffffff;
      background-image: linear-gradient(10deg, #ff4945 0%, #ff846d 100%), linear-gradient(#ff9962, #ff9962);
      background-blend-mode: normal, normal;
      border: none;
    }
  }
}
</style>
