<template>
  <div class="swiper">
      <div>
        <van-swipe class="my-swipe" :autoplay="3000" indicator-color="white">
          <van-swipe-item v-for="item in topBannerList" :key="item.opid">
            <img
              v-lazy="item.imgurl"
              @click="toWishUrl"
            />
          </van-swipe-item>
        </van-swipe>
      </div>
    </div>
</template>
<script>
import jtWebtrends1 from '@/utils/jtWebtrends'
import { newWebview } from '../../../../../../../../bjapp-model/vue2/js/jt-app-ability'
export default {
  data() {
    return {
      topBannerList: [{
        imgurl: require('@/projects/checkin-new/assets/index/banner.png'),
        opid: 0
      }]
    }
  },
  methods: {
    toWishUrl() {
      jtWebtrends1.multiTrack('P00000026112', '中国移动APP每日签到_领心愿金', { nextUrl: 'https://wx.10086.cn/qwhdhub/qwhdmark/1021122301?touch_id=JTST_P00000003668&yx=JHQD9999999999&channelId=P00000003668' })
      newWebview('https://wx.10086.cn/qwhdhub/qwhdmark/1021122301?touch_id=JTST_P00000003668&yx=JHQD9999999999&channelId=P00000003668')
    },
  }
}
</script>
<style lang="scss" scoped>

.swiper {
  width: 700px;
  background-color: #ffffff;
  border-radius: 24px;
  margin: 0 auto;
  margin-bottom: 20px;
  .my-swipe {
    img {
      width: 700px;
      height: 120px;
      border-radius: 20px;
      display: block;
    }
  }
}
</style>
