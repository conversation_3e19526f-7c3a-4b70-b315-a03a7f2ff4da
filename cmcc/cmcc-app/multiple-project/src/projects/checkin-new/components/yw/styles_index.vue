<template>
  <section class="region" v-if="regionList && regionList.length">
    <div class="region-bg"></div>
    <div v-for="(item, index) in regionList" :key="index" class="region-item">
      <div :is="isWhichCom(item)" :baList="item.block" :num="index" v-if="item.block && item.block.length"></div>
    </div>
  </section>
</template>

<script>
import importCom from './mixins/import-com'
export default {
  mixins: [importCom],
  props: {
    regionList: {
      type: Array,
      default() {
        return []
      }
    }
  },
  created() {
    // console.log(this.regionList)
  },
  components: {},
  computed: {},
  methods: {}
}
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
@import "~@/projects/checkin-new/styles/mixin.scss";
.region {
  position: relative;
  padding: 70px 25px 25px;
  margin: 0 auto;
  border-radius: 24px;
  width: 650px;
  background: url("~@/projects/checkin-new/assets/index/bg-card.png");
  background-size: 100%;
  border-radius: 20px;
  overflow: hidden;
  &-head {
    text-align: left;
    color: #000000;
    font-family: OPPOSans-B;
    font-size: 36px;
    font-weight: bold;
    font-style: italic;
    font-stretch: normal;
    letter-spacing: 0px;
    color: #ffffff;
    margin-bottom: 30px;
  }
  &-item {
    position: relative;
    z-index: 1;
  }
  &-bg {
    position: absolute;
    top: 100px;
    bottom: 0;
    left: 0;
    right: 0;
    background-image: linear-gradient(90deg, rgba(241, 72, 70, 1) 0%, #ff9b62 100%);
    z-index: 0;
  }
}
</style>
