<!--
 * @Author: zheng<PERSON><PERSON> <EMAIL>
 * @Date: 2024-07-15 16:08:40
 * @LastEditors: zhengwenling <EMAIL>
 * @LastEditTime: 2024-07-15 16:13:09
 * @FilePath: \multiple-project\src\projects\checkin-new\components\yw\ActSignIn2022_banner.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE,
-->
<template>
  <div class="banner-swipe">
      <van-swipe class="my-swipe" :autoplay="3000" indicator-color="white">
        <van-swipe-item v-for="item in bannerList" :key="item.opid">
          <img
            class="banner"
            v-lazy="item.imgurl"
            @click="gotoBannerUrl(item)"
          />
        </van-swipe-item>
      </van-swipe>
    </div>
</template>
<script>
import jtWebtrends1 from '@/utils/jtWebtrends'
import { newWebview } from '../../../../../../../../bjapp-model/vue2/js/jt-app-ability'
import { checkBjUser } from '@/projects/checkin-new/utils/utils'
export default {
  props: {
    bannerList: {
      type: Array,
      default: () => {}
    }
  },
  methods: {
    gotoBannerUrl(item) {
      jtWebtrends1.multiTrack('P00000025022', `中国移动APP每日签到_底部banner_${item.title}`,  { nextUrl: item.url })
      if (checkBjUser()) newWebview(item.url)
    }
  }
}
</script>
<style lang="scss" scoped>
.banner-swipe {
  margin-bottom: 30px;
  .banner {
    width: 700px;
    height: 180px;
    border-radius: 24px;
    margin: 0 auto;
    background: #ffffff;
  }
}
</style>
