/*
 * @Author: z<PERSON><PERSON><PERSON><PERSON> <EMAIL>
 * @Date: 2024-07-16 10:28:06
 * @LastEditors: zhengwenling <EMAIL>
 * @LastEditTime: 2024-07-16 10:39:13
 * @FilePath: \multiple-project\src\projects\checkin-new\components\yw\mixins\import-com.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
const modulesFiles = require.context('../', true, /display\.vue$/)
// you do not need `import Style1001 from '../style01/display.vue'`
// it will auto require all vuex module from modules file
const modules = modulesFiles.keys().reduce((modules, modulePath) => {
  const moduleName = modulePath.replace(/^\.\/(.*)\/\w+\.\w+$/, '$1')
  const value = modulesFiles(modulePath)
  modules[moduleName] = value.default

  return modules
}, {})

// console.log(modules, 'modules1')

export default {
  components: modules,
  methods: {
    isWhichCom (item) {
      if (item) {
        return 'style' + item.styleType
      } else { return '' }
    }
  }
}
