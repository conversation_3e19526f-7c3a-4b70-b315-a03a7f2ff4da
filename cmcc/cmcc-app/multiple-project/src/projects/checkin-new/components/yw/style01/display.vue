<template>
   <!-- 轮播图 -->
  <van-swipe :autoplay="3000" class="style01" >
    <van-swipe-item v-for="image in baList" :key="image.coid">
      <img v-lazy="image.imgurl" class="style01__img" @click="toActive(image)" />
    </van-swipe-item>
  </van-swipe>
  <!-- <section class="style01">
    <div v-for="(item, index) in baList" :key="item.id" @click="toActive(item,index,'01')" class="style01__img">
      <img v-lazy="item.imgurl" alt="" />
    </div>
  </section> -->
</template>

<script>
import style from '../mixins/style'

export default {
  mixins: [style],
  data() {
    return {
      styleNum: '01'
    }
  },
  mounted() {},
  methods: {
  }
}
</script>
<style rel="stylesheet/scss" lang="scss" scoped>
.style01 {
  width: 100%;
  margin: 15px auto;
  &__img {
    margin: 0 auto 10px;
    width: 600px;
    height: 160px;
    background-color: #f4f8ff;
    border-radius: 16px;
    overflow: hidden;

    img {
      width: 100%;
      height: 100%;
    }
  }
}
</style>
