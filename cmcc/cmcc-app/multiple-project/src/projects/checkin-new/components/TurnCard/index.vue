<template>
  <div class="atui-turn-card" :style="{ backgroundImage: 'url(' + indexBg + ')' }">
    <ul class="content">
      <li
        v-for="(img, index) in imgList"
        :key="index"
        :class="['content-item', routeCard ? 'card' + img.id : '']"
        @click="doTurnCard(img, index)"
      >
        <!-- 卡片正面图片  -->
        <div class="content-item__img">
          <img
            ref="cardz"
            :src="img.img"
            :class="['card', 'front', turnAround && !img.isTarget ? 'animationBack' : 'animationFrond']"
          />
        </div>

        <!-- 卡片背面背景  -->
        <div
          ref="cardf"
          :class="['card', 'back', turnAround && !img.isTarget ? 'animationFrond' : 'animationBack']"
          :style="{ backgroundImage: 'url(' + backList[index] + ')' }"
        ></div>
      </li>
    </ul>
  </div>
</template>

<script>
import { mapActions, mapState } from 'vuex'
export default {
  name: 'AtuiTurnCard',
  props: {
    indexBg: {
      type: String,
      default: ''
    },
    prizeImg: {
      type: String,
      default: ''
    },
    initImgList: {
      type: Array,
      default() {
        return []
      }
    },
    backList: {
      type: Array,
      default() {
        return []
      }
    },
    show: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      targetImg: {
        id: -1,
        img: '',
        isTarget: false
      },
      routeCard: false,
      turnAround: false,
      showPrizeImg: '',
      imgList: []
    }
  },
  watch: {
    prizeImg(newV, oldV) {
      // console.log('newV', newV)
      // 若有新值，赋值给targetImg的图片img字段
      if (newV) {
        this.targetImg.img = newV
        this.rotateTarget()
      }
    },
    show: {
      handler(newV) {
        if (newV && this.agent) {
          // console.log('触发这里重启动效')
          this.imgList = JSON.parse(JSON.stringify(this.initImgList))
          this.reStartGame()
          this.updateAgent(false)
        }
      }
    }
  },
  computed: {
    ...mapState({
      canClick: (state) => state.canClick,
      agent: (state) => state.agent
    })
  },
  mounted() {
    this.imgList = JSON.parse(JSON.stringify(this.initImgList))
    this.initAnimation()
  },
  methods: {
    ...mapActions(['updateCandoing', 'updateAgent']),
    // 点击翻牌
    doTurnCard(img, index) {
      // console.log(this.canClick, img, index, this.targetImg.img)
      if (!this.canClick) return
      this.targetImg = img
      // 向父级通信，已发起点击，可以请求抽奖逻辑
      this.$emit('doPrize', true)
    },
    // 翻转选中的卡片
    rotateTarget(reset) {
      const index = this.targetImg.id - 1
      if (reset) {
        this.$set(this.imgList[index], 'isTarget', false)
        return
      }
      this.$set(this.imgList[index], 'isTarget', true)
      const timeID3 = setTimeout(() => {
        this.$emit('hadRotate', true)
      }, 800)
      this.toClearTimeID(timeID3)
    },
    // 初始化动画
    initAnimation(isReset = false, resetTime = 0) {
      // console.log('触发==', this.turnAround, this.routeCard)
      // 第二步，动画旋转，遮盖奖品
      const timeID1 = setTimeout(() => {
        this.turnAround = true
      }, 2000)
      // 第三步，动画移动，看似打乱奖品位置
      const timeID2 = setTimeout(() => {
        if (timeID1) clearInterval(timeID1)
        // 3.1 为每个奖品添加动画位移
        this.imgList.forEach((item) => {
          this.routeCard = true
          // 3.1.1 将奖品图替换为空，防止从控制台查看
          // item.img = "http://10.8.13.30:50010/act/demo/turnCard/bor.png"
        })
      }, 2500 - resetTime)
      if (!isReset) {
        const timeID3 = setTimeout(() => {
          // 初始化时动效过后才可以点击，重置时，不操作状态
          this.updateCandoing(true)
          // console.log('初始设置成功')
        }, 4500)
        this.toClearTimeID(timeID3)
      }

      // 第四步，发起监听关闭钩子，清理计时器
      this.toClearTimeID(timeID1)
      this.toClearTimeID(timeID2)
    },
    // 清楚计时器ID
    toClearTimeID(timeID) {
      this.$once('hook:beforeDestroy', () => {
        clearInterval(timeID)
      })
    },
    // 从新开始游戏
    reStartGame() {
      // console.log('重新开始', this.canClick, this.targetImg.img)
      if (this.canClick) return
      if (this.targetImg.img) {
        this.rotateTarget(true)
      }
      // this.updateCandoing(true)
      this.turnAround = false
      this.routeCard = false
      this.initAnimation()
      this.targetImg = {}
      this.$emit('update:prizeImg', '')
    }
  }
}
</script>

<style lang="scss" scoped>
@import "./styles/turn-card.scss";
</style>
