/* ACUI default theme Variables */

// Special comment for theme configurator
// type|skipAutoTranslation|Category|Order
// skipAutoTranslation 1

/* Ratio
-------------------------- */
$--base-width: 100vw !default;

/* Space
-------------------------- */
$--space-margin-base: 8px;

/* Transition
-------------------------- */
$--all-transition: all .3s cubic-bezier(.645,.045,.355,1) !default;
$--fade-transition: opacity 300ms cubic-bezier(0.23, 1, 0.32, 1) !default;
$--fade-linear-transition: opacity 200ms linear !default;
$--md-fade-transition: transform 300ms cubic-bezier(0.23, 1, 0.32, 1), opacity 300ms cubic-bezier(0.23, 1, 0.32, 1) !default;
$--border-transition-base: border-color .2s cubic-bezier(.645,.045,.355,1) !default;
$--color-transition-base: color .2s cubic-bezier(.645,.045,.355,1) !default;

/* Color
-------------------------- */
/// color|1|BrandColor|0
$--color-primary: #FF4300 !default;
/// color|1|BackgroundColor|4
$--color-white: #FFFFFF !default;
/// color|1|BackgroundColor|4
$--color-black: #000000 !default;
$--color-primary-light-1: mix($--color-white, $--color-primary, 10%) !default; /* 53a8ff */
$--color-primary-light-2: mix($--color-white, $--color-primary, 20%) !default; /* 66b1ff */
$--color-primary-light-3: mix($--color-white, $--color-primary, 30%) !default; /* 79bbff */
$--color-primary-light-4: mix($--color-white, $--color-primary, 40%) !default; /* 8cc5ff */
$--color-primary-light-5: mix($--color-white, $--color-primary, 50%) !default; /* a0cfff */
$--color-primary-light-6: mix($--color-white, $--color-primary, 60%) !default; /* b3d8ff */
$--color-primary-light-7: mix($--color-white, $--color-primary, 70%) !default; /* c6e2ff */
$--color-primary-light-8: mix($--color-white, $--color-primary, 80%) !default; /* d9ecff */
$--color-primary-light-9: mix($--color-white, $--color-primary, 90%) !default; /* ecf5ff */
/// color|1|SecondaryColor|1
$--color-success: #67C23A !default;
/// color|1|SecondaryColor|1
$--color-warning: #E6A23C !default;
/// color|1|SecondaryColor|1
$--color-danger: #F56C6C !default;
/// color|1|SecondaryColor|1
$--color-info: #909399 !default;

$--color-success-light: mix($--color-white, $--color-success, 80%) !default;
$--color-warning-light: mix($--color-white, $--color-warning, 80%) !default;
$--color-danger-light: mix($--color-white, $--color-danger, 80%) !default;
$--color-info-light: mix($--color-white, $--color-info, 80%) !default;

$--color-success-lighter: mix($--color-white, $--color-success, 90%) !default;
$--color-warning-lighter: mix($--color-white, $--color-warning, 90%) !default;
$--color-danger-lighter: mix($--color-white, $--color-danger, 90%) !default;
$--color-info-lighter: mix($--color-white, $--color-info, 90%) !default;
/// color|1|FontColor|2
$--color-text-primary: #303133 !default;
/// color|1|FontColor|2
$--color-text-regular: #606266 !default;
/// color|1|FontColor|2
$--color-text-secondary: #909399 !default;
/// color|1|FontColor|2
$--color-text-placeholder: #C0C4CC !default;
/// color|1|BorderColor|3
$--border-color-base: #DCDFE6 !default;
/// color|1|BorderColor|3
$--border-color-light: #E4E7ED !default;
/// color|1|BorderColor|3
$--border-color-lighter: #EBEEF5 !default;
/// color|1|BorderColor|3
$--border-color-extra-light: #F2F6FC !default;

// Background
/// color|1|BackgroundColor|4
$--background-color-base: #f5f7fa !default;

/* Link
-------------------------- */
$--link-color: $--color-primary-light-2 !default;
$--link-hover-color: $--color-primary !default;

/* Border
-------------------------- */
$--border-width-base: 1px !default;
$--border-style-base: solid !default;
$--border-color-hover: $--color-text-placeholder !default;
$--border-base: $--border-width-base $--border-style-base $--border-color-base !default;
/// borderRadius|1|Radius|0
$--border-radius-base: 8px !default;
/// borderRadius|1|Radius|0
$--border-radius-small: 4px !default;
/// borderRadius|1|Radius|0
$--border-radius-circle: 100% !default;

// Box-shadow
/// boxShadow|1|Shadow|1
$--box-shadow-base: 0 2px 4px rgba(0, 0, 0, .12), 0 0 6px rgba(0, 0, 0, .04) !default;
// boxShadow|1|Shadow|1
$--box-shadow-dark: 0 2px 4px rgba(0, 0, 0, .12), 0 0 6px rgba(0, 0, 0, .12) !default;
/// boxShadow|1|Shadow|1
$--box-shadow-light: 0 2px 12px 0 rgba(0, 0, 0, 0.1) !default;

/* Fill
-------------------------- */
$--fill-base: $--color-white !default;

/* Typography
-------------------------- */
$--font-path: 'fonts' !default;
/// fontSize|1|FontSize|0
$--font-size-extra-large: 20px !default;
/// fontSize|1|FontSize|0
$--font-size-large: 18px !default;
/// fontSize|1|FontSize|0
$--font-size-medium: 16px !default;
/// fontSize|1|FontSize|0
$--font-size-base: 14px !default;
/// fontSize|1|FontSize|0
$--font-size-small: 13px !default;
/// fontSize|1|FontSize|0
$--font-size-extra-small: 12px !default;
/// fontWeight|1|FontWeight|1
$--font-weight-blod: 700 !default;
/// fontWeight|1|FontWeight|1
$--font-weight-primary: 500 !default;
/// fontWeight|1|FontWeight|1
$--font-weight-secondary: 100 !default;
/// fontLineHeight|1|LineHeight|2
$--font-line-height-primary: 24px !default;
/// fontLineHeight|1|LineHeight|2
$--font-line-height-secondary: 16px !default;
$--font-color-disabled-base: #bbb !default;
/* Size
-------------------------- */
$--size-base: 14px !default;

/* z-index
-------------------------- */
$--index-normal: 1 !default;
$--index-top: 1000 !default;
$--index-popper: 2000 !default;

/* Disable base
-------------------------- */
$--disabled-fill-base: $--background-color-base !default;
$--disabled-color-base: $--color-text-placeholder !default;
$--disabled-border-base: $--border-color-light !default;

/* Icon
-------------------------- */
$--icon-color: #666 !default;
$--icon-color-base: $--color-info !default;


/* Break-point
--------------------------*/
$--sm: 768px !default;
$--md: 992px !default;
$--lg: 1200px !default;
$--xl: 1920px !default;

$--breakpoints: (
  'xs' : (max-width: $--sm - 1),
  'sm' : (min-width: $--sm),
  'md' : (min-width: $--md),
  'lg' : (min-width: $--lg),
  'xl' : (min-width: $--xl)
);

$--breakpoints-spec: (
  'xs-only' : (max-width: $--sm - 1),
  'sm-and-up' : (min-width: $--sm),
  'sm-only': "(min-width: #{$--sm}) and (max-width: #{$--md - 1})",
  'sm-and-down': (max-width: $--md - 1),
  'md-and-up' : (min-width: $--md),
  'md-only': "(min-width: #{$--md}) and (max-width: #{$--lg - 1})",
  'md-and-down': (max-width: $--lg - 1),
  'lg-and-up' : (min-width: $--lg),
  'lg-only': "(min-width: #{$--lg}) and (max-width: #{$--xl - 1})",
  'lg-and-down': (max-width: $--xl - 1),
  'xl-only' : (min-width: $--xl),
);
