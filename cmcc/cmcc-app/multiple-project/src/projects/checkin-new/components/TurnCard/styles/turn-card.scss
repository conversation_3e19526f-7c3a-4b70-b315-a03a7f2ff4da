@import "mixins/mixins";
@import "common/var";
@import "common/transition";


a,img,button,input,textarea,div{-webkit-tap-highlight-color:rgba(255,255,255,0);}
@include b(turn-card){
    // width: p2v(414px *2);
    // height: p2v(1214px);
    height: 100%;
    position: relative;
    margin: 0 auto;
    display: flex;
    align-items: center;
    justify-content: center;
    font-family: "Avenir", Helvetica, Arial, sans-serif;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    text-align: center;
    background-size: cover;
    overflow: hidden;
    -moz-user-select: none;
    -webkit-user-select: none;
    -ms-user-select: none;
    user-select: none;
    .satrtBtn {
        height: 45px;
        width: 160px;
        position: absolute;
        bottom: 50px;
        background-size: 100% 100%;
        background-repeat: no-repeat;
    }
    .content {
        display: flex;
        justify-content: space-between;
        flex-wrap: wrap;
        width: p2v(680px);
        margin: p2v(0) auto 0;
        &-item {
            width: 200px;
            height: 20vh;
            // margin: 10px 10px 20px;
            margin-bottom: 20px;
            position: relative;
            cursor: pointer;
            &__img {
                height: 100%;
                display: flex;
                align-items: center;
                z-index: 1;
                .front {
                    display: inline-block;
                    //height: auto !important;
                    max-width: 100%;
                    z-index: 2;
                }
                img {
                    position: relative !important;
                }
            }
            .card {
                position: absolute;
                left: 0;
                top: 0;
                width: 100%;
                height: 100%;
                color: #fff;
                line-height: p2v(130px);
                text-align: center;
                cursor: pointer;
                transition: 0.3s ease-in-out;
                backface-visibility: hidden;
                -webkit-backface-visibility: hidden;
            }
            .back {
                background-repeat: no-repeat;
                background-size: 100% 100%;
                z-index: 2;
            }
        }
        .animationFrond {
            transform: rotateY(0deg);
        }
        .animationBack {
            transform: rotateY(180deg);
        }
    }


    $x: 239px;
    $y: 252px;
    // 用数组存储，9个x,y轴的移动坐标
    $moveX: p2v($x) 0 p2v(-$x) p2v($x) 0 p2v(-$x) p2v($x) 0 p2v(-$x);
    $moveY: p2v($y) p2v($y) p2v($y) 0 0 0 p2v(-$y) p2v(-$y) p2v(-$y);
    @for $i from 1 to length($moveX) + 1 {
        .card#{$i}{
            position:relative;
            animation: Mymove+$i 2s;
            -webkit-animation: Mymove+$i 2s;
        }
        @keyframes Mymove#{$i} {
            0% {
                transform: translate(0, 0);
            }
            75% {
                transform: translate(nth($moveX,$i),nth($moveY,$i));
            }
            100% {
                transform: translate(0, 0);
            }
        }
    }
}
