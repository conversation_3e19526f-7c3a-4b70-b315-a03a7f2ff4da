/**
 * Created by linsang on 2019/10/11.
 */
import RuleDialogComponents from './index.vue'

const RuleDialog = {}
RuleDialog.install = Vue => {
  const RuleDialogConstructor = Vue.extend(RuleDialogComponents)
  const instance = new RuleDialogConstructor()
  instance.$mount(document.createElement('div'))
  document.body.appendChild(instance.$el)
  Vue.prototype.$ruleDialog = {
    show(params) {
      /*
        * @param {String} title 标题（跟进具体需求变动，如果字体没要求，参数为文字即可，如果是特效字体，切图，传递id指定title）
        * @param {String} btnWord 按钮文案
        * @param {String} content 内容，可传html标签
        * @param {Function} callBack 按钮回调
        * @param {Object} dialogStyle 自定义内容样式
        * */
      const defaultParams = {
        show: true,
        title: '提示',
        content: '',
        btn: '知道了',
        dialogStyle: null,
        callBack: null,
        closeBtn: false
      }
      instance.params = Object.assign(defaultParams, params)
      instance.show = true
    },
    hide() {
      instance.show = false
    }
  }
}

export default RuleDialog
