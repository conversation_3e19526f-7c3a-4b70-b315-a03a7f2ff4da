<template>
  <div v-if="show" class="rule-dialog">
    <div class="rule-dialog__container">
      <div class="rule-dialog__header">
        <div>{{ params.title }}</div>
      </div>
      <div v-if="params.closeBtn" class="close" @click.stop="hide"></div>
      <div class="content">
        <div :class="`html`" :style="params.dialogStyle ? params.dialogStyle : ''" v-html="params.content"></div>
      </div>
      <div class="rule-dialog__btn" @click="btnFun">{{ params.btn }}</div>
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      show: false,
      params: {}
    }
  },
  computed: {
    isShowWxOpenApp() {
      return this.params.bannerTip && this.isWXBrowser
    }
  },
  watch: {
    show: {
      handler: function (newVar, oldVar) {
        if (newVar) {
          document.getElementsByTagName('body')[0].style.overflow = 'hidden'
        } else {
          document.getElementsByTagName('body')[0].style.overflow = 'auto'
        }
      }
    }
  },
  created() {},
  methods: {
    btnFun() {
      this.show = false
      this.params.callBack && this.params.callBack()
    },
    hide() {
      this.show = false
      this.params.closeCallBack && this.params.closeCallBack()
    }
  }
}
</script>

<style lang="scss" scoped>
@import "~@/projects/checkin-new/styles/mixin.scss";
.rule-dialog {
  position: fixed;
  left: 0;
  top: 0;
  right: 0;
  bottom: 0;
  display: flex;
  // overflow: scroll;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  background: rgba(0, 0, 0, 0.6);
  padding-top: 100px;
  z-index: 999;
  &__header {
    width: 471px;
    height: 117px;
    position: absolute;
    top: -60px;
    left: 50%;
    text-align: center;
    transform: translateX(-50%);
    background: url("./img/t.png") no-repeat center center;
    background-size: 100% 100%;
    padding-top: 7px;
    font-family: PingFang-SC-Bold;
    font-size: 36px;
    font-weight: bold;
    font-stretch: normal;
    line-height: 72px;
    letter-spacing: 4px;
    color: #ffffff;
  }
  &__container {
    position: relative;
    width: 650px;
    z-index: 2;
    padding:70px  30px 50px;
    &::before {
      content: "";
      position: absolute;
      top: 0;
      left: 0;
      bottom: 0;
      right: 0;
      background-color: #fffae0;
      border-radius: 60px;
      border: solid 6px #fa7957;
      z-index: 0;
    }
    .html {
      width: 100%;
      overflow-y: scroll;
      -webkit-overflow-scrolling: touch;
      word-break: break-all;
      text-align: left;
      border-radius: 22px;
      font-family: PingFang-SC-Bold;
      font-size: 32px;
      line-height: 50px;
      color: #ff2f4c;
      /deep/ span {
      font-weight: bold;
      }
      /deep/ .t-indent {
        text-indent: 0.7rem;
      }
      /deep/.spacing {
        color: #4290f5;
        letter-spacing: 7px;
      }
    }
    .content {
      text-align: left;
      max-height: 50vh;
      overflow-y: scroll;
      margin: 30px 30px 0;
      line-height: 56px;
      color: #ca7100;
      font-size: 30px;
      position: relative;
    }
    .close {
      width: 44px;
      height: 44px;
      position: absolute;
      left: 50%;
      transform: translateX(-50%);
      bottom: -88px;
      background: url("./img/close.png") no-repeat center center;
      background-size: 100% 100%;
    }
  }
  &__btn {
    position: relative;
    @include btn();
    margin: 30px auto 0;
  }
}
</style>
