<template>
  <van-overlay :show="showPop" class="update-activity">
    <div class="new-mian">
      <div class="new-mian__btn" @click="showPop = false"></div>
      <div class="new-mian__close" @click="showPop = false"></div>
    </div>
  </van-overlay>
</template>

<script>
import { Overlay } from 'vant'
export default {
  components: {
    [Overlay.name]: Overlay
  },
  props: {
    currentDate: {
      type: Number,
      default: 0
    },
    misdnmask: {
      type: String,
      default: ''
    }
  },
  mounted() {
    const day = new Date(Number(this.currentDate))
    const nowDay = day.getFullYear() + '' + day.getMonth() + '' + day.getDate()
    const s = this.misdnmask.replace('****', '') + nowDay
    console.log(nowDay)
    if (!localStorage.getItem(s)) {
      this.showPop = true
      localStorage.setItem(s, true)
    }
  },
  data() {
    return {
      showPop: false
    }
  }
}
</script>

<style lang="scss" scoped>
@import "~@/projects/checkin-new/styles/mixin.scss";
.update-activity {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0,0,0,.5);
  z-index: 100;

}
.new {
    z-index: 99;

    &-mian {
      @include background(644px, 709px, "update/bg.png");
      position: absolute;
      left: 50%;
      top: 45%;
      transform: translate(-50%, -50%);
      &__btn {
        @include background(355px, 108px, "update/btn.png");
        position: absolute;
        bottom: 90px;
        left: 22%;
        // transform: translateX(-50%);
        animation: button 1s linear alternate infinite;
      }
      &__close {
        position: absolute;
        bottom: -60px;
        left: 50%;
        transform: translateX(-50%);
        @include background(79px, 79px, "index/X.png");
      }
    }
  }
</style>
