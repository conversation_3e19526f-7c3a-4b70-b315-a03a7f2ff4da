import { IS_YW_JMESS } from '@/projects/checkin-new/config/index'
import { newWebview } from '../../../../../../../bjapp-model/vue2/js/jt-app-ability'
import Vue from 'vue'
const vm = new Vue()
/**
 * 日期格式化
 * @param value
 * @param format
 * @returns {*}
 * index:parseTime(new Date(), 'yyyy-MM-dd')
 */
export function dateFormat (value, format) {
  if (typeof value === 'string') {
    value = value.replace(/-/g, '/')
  }
  let t
  if (value) {
    t = new Date(value)
  } else {
    t = new Date()
  }
  const o = {
    'M+': t.getMonth() + 1, // month
    'd+': t.getDate(), // day
    'h+': t.getHours(), // hour
    'm+': t.getMinutes(), // minute
    's+': t.getSeconds(), // second
    'q+': Math.floor((t.getMonth() + 3) / 3), // quarter
    S: t.getMilliseconds() // millisecond
  }
  if (/(y+)/.test(format)) {
    format = format.replace(RegExp.$1, (t.getFullYear() + '').substr(4 - RegExp.$1.length))
  }
  for (const k in o) {
    if (new RegExp('(' + k + ')').test(format)) {
      format = format.replace(RegExp.$1, RegExp.$1.length === 1 ? o[k] : ('00' + o[k]).substr(('' + o[k]).length))
    }
  }
  return format
}

/**
 *  浮点数四舍五入保留小数点
 * @param {Number} usedflow
 * @returns
 */
export function fomatFloat (value, n) {
  var f = Math.round(value * Math.pow(10, n)) / Math.pow(10, n)
  var s = f.toString()
  var rs = s.indexOf('.')
  if (rs < 0) {
    s += '.'
  }
  for (var i = s.length - s.indexOf('.'); i <= n; i++) {
    s += '0'
  }
  return s
}
/**
 *  MB 转 GB
 * @param {Number} usedflow
 * @param {String} type 返回类型
 * @returns
 */
export function converMBToGB (usedflow, type) {
  let result = {}
  if (!usedflow) {
    result = { numValue: 0, unit: 'GB' }
  } else if (typeof usedflow === 'string') {
    result = { numValue: usedflow, unit: '' }
  } else if (usedflow >= 1024) {
    let size = ''
    if (usedflow) {
      size = (usedflow / 1024).toFixed(2)
    }
    const sizestr = fomatFloat(size, 1) + ''
    result = {
      numValue: sizestr,
      unit: 'GB'
    }
  } else {
    result = {
      numValue: usedflow,
      unit: 'MB'
    }
  }
  if (type === 'string') {
    return result.numValue + result.unit
  }
  return result
}

/**
 *  流量单位转换 KB 转 MB GB
 * @param {Number} limit
 * @returns
 */
export function conver (limit) {
  var size = ''
  if (limit) {
    limit = limit * 1
    if (limit < 1 * 1024) {
      // 如果小于1MB转化成KB
      size = parseFloat(limit.toFixed(2)) + 'KB'
    } else if (limit < 1 * 1024 * 1024) {
      // 如果小于1GB转化成MB
      size = parseFloat((limit / 1024).toFixed(2)) + 'MB'
    } else {
      size = parseFloat((limit / (1024 * 1024)).toFixed(2)) + 'GB'
    }
  }
  var sizestr = size + ''
  var len = sizestr.indexOf('.')
  var dec = sizestr.substr(len + 1, 2)
  if (dec === '00') {
    // 当小数点后为00时 去掉小数部分
    return sizestr.substring(0, len) + sizestr.substr(len + 3, 2)
  }
  return sizestr || '0GB'
}

/**
 获取电话掩码
 * @param {Number} tel 电话号码明码
 * @returns
 */
export function getMisdnmask (tel) {
  if (tel) {
    tel = '' + tel
    const reg = /(\d{3})\d{4}(\d{4})/
    return tel.replace(reg, '$1****$2')
  }
}
/* 号码校验 */
export function checkPhone (telephone) {
  const reg = /^\d{11}$/
  // const reg2 = /^[1][3,4,5,6,7,8,9][0-9]{9}$/
  if (!telephone || !reg.test(telephone)) {
    return false
  }
  return true
}
// 去掉字符串中的数字
export function deleteNum (str) {
  if (!str) return ''
  const reg = /[0-9]+/g
  const str1 = str.replace(reg, '').replace('流量', '')
  return str1
}

// 提取字符串中的数字
export function getNum (str) {
  if (!str) return ''
  const reg = /[^\d]/g
  const str1 = str.replace(reg, '')
  return str1
}

// 获取OpenIds
export function getOpenIds (infoArr) {
  let openIds = ''
  if (infoArr.length) {
    let openIdArr = []
    openIdArr = infoArr.map(item => {
      return item.openid
    })
    openIds = openIdArr.join(',')
  }
  return openIds
}

/**
 * 获取当前月天数
 * @param {*} dateTime 当前时间
 * @returns
 */
export function getCurrentDays (dateTime) {
  const currentDate = new Date(dateTime)
  const currentY = currentDate.getFullYear()
  const currentM = currentDate.getMonth() + 1
  const MonthDayNum = new Date(currentY, currentM, 0).getDate() // 计算当月的天数
  // const lastM = new Date(currentY, currentM, 0) // new Date(2020,11,0);//表示是2020/12/1号的前一天，就是2020/11/30这天
  // console.log('当前时间=', currentDate)
  // console.log('天数=', MonthDayNum)
  // // console.log('当前时间的最后一天日期', lastM)
  return MonthDayNum
}
/**
 * 获取年月日
 * @param {*} date 传入时间 Date格式
 * @returns
 */
export const getNewDate = (date) => {
  const year = date.getFullYear()
  const month = date.getMonth()
  const day = date.getDate()
  return {
    year,
    month,
    day
  }
}

/**
 *传年月日转为时间Date时间
 * @param {*} year 年
 * @param {*} month 月
 * @param {*} day 日
 * @returns
 */
export const getDate = (year, month, day) => {
  return new Date(year, month, day)
}

/**
 *  转换为两位数字符串
 * @param {*} date
 * @returns
 */
export const formatDate = (date) => {
  date = Number(date)
  return date < 10 ? `0${date}` : date
}
/**
 *  跳转
 * @param {Object} urlObj 路径对象
 * @returns
 */
export const goUrl = (urlObj) => {
  if (!checkBjUser()) return
  let path = urlObj.master
  if (window.location.port !== '') {
    path = urlObj.gray
  }
  newWebview(path)
  // if (window.leadeon) {
  //   newWebview(path)
  // } else {
  //   location.href = path
  // }
}
// 判断是否流量类型奖品
export function isFlowType(prizeType) {
  return Number(prizeType) === 1 || Number(prizeType) === 8
}
// 匹配流量单位
export function getUnit(str) {
  if (!str) return ''
  const unitArr = ['KB', 'MB', 'GB']
  const unit = unitArr.find(item => {
    return str.indexOf(item) !== -1
  })
  return unit || ''
}
// 判断是否非北京用户
export function checkBjUser() {
  const isBj = sessionStorage.getItem('isBjuser')
  if (String(isBj) === '0') {
    vm.$toast(IS_YW_JMESS)
    return false
  }
  return true
}
/*

*判断是否在同一个月

*date1时间戳

*return:true/false

*/

export function isSameMonth(date1, date2) {
  var mon1 = new Date(date1).getMonth()
  var mon2 = new Date(date2).getMonth()
  return mon2 === mon1
}
// 把非谢谢回顾的奖品往前排列
export function listGetHavePrize(list) {
  if (list) {
    const l = JSON.parse(JSON.stringify(list))
    l.sort((a, b) => {
      if (String(a.prizeType) === '5') {
        return 1
      } else if (String(b.prizeType) === '5') {
        return -1
      }
      return 0
    })
    return l
  }
  return list
}

export function getTenFormatNum(value) {
  return value > 9 ? value : '0' + value
}

