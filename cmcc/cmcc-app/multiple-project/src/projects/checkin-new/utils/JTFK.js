// 集团封控要求
/**
* @type {{ConstID:function(Object,function(event,string):void):void , ...}}
*/

/**
* @async
* @param {Object} options
* @param {String} options.appId 接入方唯一标识
* @param {String} options.server 风险探针服务接口url
* @returns {Promise<string | ''>}
*/
export async function init(options) {
  if(window._zw) {
    return await new Promise(resolve => {
      window._zw.ConstID(options,(e,id) => {
      console.log('打印3', e)
      resolve(id)
    })
    })
  } else{
    return ''
  }
}

export default init