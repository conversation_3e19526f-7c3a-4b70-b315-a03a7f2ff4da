// 据openid数组获取用户头像等信息
import { getUserInfoByOpenId } from '@/api/common'
export const getUserInfoByOpenIdFn = async (openid, helpPoList) => {
  let resultList = []
  await getUserInfoByOpenId({ openid: openid }).then(res => {
    const { result, retlist } = res
    if (result === 0) {
      if (!(helpPoList && helpPoList.length)) {
        resultList = retlist || []
        return
      }
      retlist &&
                retlist.length > 0 &&
                retlist.forEach(item => {
                  const o = item.openid
                  helpPoList.forEach(hItem => {
                    const ho = hItem.openId || hItem.openid
                    if (o === ho) {
                      hItem.avatarUrl = item.avatarUrl
                      hItem.headimgurl = item.headimgurl
                      hItem.nickname = item.nickname
                    }
                  })
                })
      resultList = [...helpPoList]
    }
  })
  return resultList
}
