import Vue from 'vue'
import { IS_YW_JMESS } from '@/projects/checkin-new/config/index'
const vm = new Vue()

export function getcheckinResult(result) {
  let flag = false
  switch (String(result)) {
    case '0':
      flag = true
      break
    case '-100':
    case '-101':
    case '-103':
      vm.$toast('抱歉，活动已结束哦~')
      break
    case '-102':
      vm.$toast('抱歉，活动未开始哦~')
      break
    case '-114':
      vm.$toast('今日已签到~')
      break
    case '-115':
      vm.$toast('您还没有补签卡！')
      break
    case '-200':
    case '-20003':
      vm.$toast(IS_YW_JMESS)
      break
    case '-1':
    case '-60007':
      vm.$toast('活动太火爆了，请稍后再试哦~')
      break
    default:
      break
  }
  return flag
}
