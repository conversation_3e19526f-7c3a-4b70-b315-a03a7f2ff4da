import { Base64 } from '@/utils/base64'
export default {
  methods: {
    // 通用中转页(兼容低版本app&唤醒失败)
    goActivityUnifyLogin () {
      const _path = window.location.href
      const base = new Base64()
      window['loca' + 'tion'].href = `${window.location.origin}/cmccActUnifyLogin/index.html?titleName=${base.encode(
                window.ACT_TITLE
            )}&pi=act71&backUrl=${encodeURIComponent(_path)}`
    }
  }
}
