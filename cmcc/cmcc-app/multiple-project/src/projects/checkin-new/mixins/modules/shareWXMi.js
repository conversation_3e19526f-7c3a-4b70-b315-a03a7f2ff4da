import { shareMiniProgram, openMiniProgram } from '../../../../../../../../bjapp-model/vue2/js/jt-app-ability'
export default {
  methods: {
    // 分享小程序 type：second再次邀请分享：helpSponsor: 分享标志
    shareWXMi(helpSponsor) {
      let baseUrl = 'https://h5.bj.10086.cn'
      // 端口不为空或链接路径存在GRAYACTStatic，为灰度
      if (window.location.port !== '' || window['loca' + 'tion'].href.indexOf('GRAYACTStatic') > -1) {
        baseUrl = 'https://sc.bj.chinamobile.com/GRAYACTStatic'
      }

      let shareUrl = 'pagesAct/checkin-st/invite'
      const sponsor = helpSponsor || sessionStorage.getItem('helpSponsor')
      if (sponsor) {
        shareUrl = `pagesAct/checkin-st/invite?shareSponsor=${sponsor}`
      }
      const jsonObject = {
        title: '【求点】助我翻牌赢好礼',
        description: '',
        url: shareUrl,
        iconurl: baseUrl + '/cmcc_activity/uniApp/bj-mini-app/act/checkin-st/new/share-mini.png',
        path: shareUrl
      }
      shareMiniProgram(jsonObject)
    },
    // 订阅消息
    launchwxmini(obj) {
      openMiniProgram(obj)
    }
  }
}
