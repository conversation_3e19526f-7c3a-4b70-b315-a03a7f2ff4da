<!--
 * @Author: z<PERSON><PERSON><PERSON><PERSON> zhengwen<PERSON>@aspirecn.com
 * @Date: 2023-03-24 19:08:31
 * @LastEditors: zhengwen<PERSON> <EMAIL>
 * @LastEditTime: 2024-07-15 17:50:26
 * @FilePath: \multiple-project\src\projects\checkin-new\App.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <div id="app">
    <keep-alive>
      <router-view />
    </keep-alive>
    <browseBall v-if="showBrowseBall" wm="" envName="中国移动APP_4月打卡_签到" actKeyword="ActSignIn2023JT" />
  </div>
</template>

<script>
import browseBall from '@/components/browse-ball'
import CHANNEL from '../../../../../../bjapp-model/vue2/js/channel'
import jtWebtrends1 from '@/utils/jtWebtrends'

export default {
  name: 'App',
  components: {
    browseBall
  },
  mounted() {
    // 调整微信中的系统字体大小带来的页面变化
    this.initWxFontSize()
    this.showBrowseBall = true
    jtWebtrends1.setGeneralProps()
  },
  data() {
    return {
      showBrowseBall: false
    }
  },
  methods: {
    initWxFontSize() {
      if (CHANNEL.isAndroid() && CHANNEL.isWX()) {
        // Android微信中，借助WeixinJSBridge对象来阻止字体大小调整
        if (typeof window.WeixinJSBridge === 'object' && typeof window.WeixinJSBridge.invoke === 'function') {
          this.handleFontSize()
        } else {
          if (document.addEventListener) {
            document.addEventListener('WeixinJSBridgeReady', this.handleFontSize, false)
          } else if (document.attachEvent) {
            // IE浏览器，非W3C规范
            document.attachEvent('onWeixinJSBridgeReady', this.handleFontSize)
          }
        }
      }
    },
    handleFontSize() {
      // 设置网页字体为默认大小
      window.WeixinJSBridge.invoke('setFontSizeCallback', { fontSize: 0 })
      // 重写设置网页字体大小的事件
      window.WeixinJSBridge.on('menu:setfont', function () {
        window.WeixinJSBridge.invoke('setFontSizeCallback', { fontSize: 0 })
      })
    }
  }
}
</script>

<style lang="scss">
#app {
  font-family: "Avenir", Helvetica, Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  height: 100%;
  max-width: 1080px;
  margin: 0 auto;
}
</style>
