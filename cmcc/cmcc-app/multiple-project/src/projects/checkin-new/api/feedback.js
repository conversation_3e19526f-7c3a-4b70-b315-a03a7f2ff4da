/*
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON> zhen<PERSON><PERSON><PERSON>@aspirecn.com
 * @Date: 2024-03-27 16:43:12
 * @LastEditors: zheng<PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2025-01-07 14:58:37
 * @FilePath: \multiple-project\src\projects\checkin-new\api\feedback.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import request from '@/utils/request'
const keyword = 'ContinuousSignIn2025'
const basePath = 'ActSignIn2023/ContinuousSignIn'
const channel = 'JT'

// 获取连续月签信息接口
export function getSignInFeedback (params) {
  return request({
    url: `/${basePath}/getActivityMain/${channel}/${keyword}`,
    method: 'post',
    params: params
  })
}

// 获取连续月签出奖接口
export function customPrizeFeedback (params) {
  return request({
    url: `/${basePath}/customPrize/${channel}/${keyword}`,
    method: 'post',
    params: params
  })
}
