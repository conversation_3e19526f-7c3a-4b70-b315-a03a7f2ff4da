import request from '@/utils/request'
import { getQueryString } from '@/utils/utils'
const keyword = 'ActSignIn2023JT'
const basePath = 'ActSignIn2023'
const keywordfpz = 'ActSignintoflipJT'
const basePathfpz = 'ActSignintoflip'
const ua = window.navigator.userAgent.toLowerCase()
let channel = 'JT'

if (window.leadeon) {
    channel = 'JT'
}
if (ua.indexOf('micromessenger') > -1) {
    channel = 'WX'
}
if (getQueryString('mm') !== null) {
    channel = 'TUWEN'
}

// 1.1 获取签到信息接口
export function getSignIn(params) {
    return request({
        url: `/${basePath}/getSignIn/${channel}/${keyword}`,
        method: 'post',
        params: params
    })
}
// 1.2签到接口
export function doPrize(params) {
    return request({
        url: `/${basePath}/doPrize/${channel}/${keyword}`,
        method: 'post',
        params: params,
        head: { constid: params.constid }
    })
}

// 1.5 根据栏目id/栏目关键字获取运营位接口
export function operate_unifyH5(params) {
    return request({
        url: '/app/operate_unifyH5',
        method: 'post',
        params: params
    })
}

// 1.6常用功能推荐入口
export function queryTuijianzInfo(params) {
    return request({
        url: `/${basePath}/queryTuijianzInfo/${channel}/${keyword}`,
        method: 'post',
        params: params
    })
}

// 1.7 查询中奖纪录接口
export function getPrizeList(params) {
    return request({
        url: `/${basePath}/queryPrizeList/${channel}/${keyword}`,
        method: 'post',
        params: params
    })
}
// 1.8 翻牌子发奖接口
export function doPrizeFpz(params) {
    return request({
        url: `/${basePathfpz}/doPrize/${channel}/${keywordfpz}`,
        method: 'post',
        params: params
    })
}
// 1.8.2 翻牌子奖励使用规则接口
export function getJTUsageRule(params) {
    return request({
        url: `/${basePathfpz}/getJTUsageRule/${keywordfpz}`,
        method: 'post',
        params: params
    })
}
// 1.9 翻牌子助力接口
export function assistantSponsor(params) {
    return request({
        url: `/${basePathfpz}/assistantSponsorNew/${channel}/${keywordfpz}`,
        method: 'post',
        params: params
    })
}
// 2.0 翻牌子-获得浏览任务机会接口
export function saveVisit(params) {
    return request({
        url: `/${basePathfpz}/saveVisit/${channel}/${keywordfpz}`,
        method: 'post',
        params: params
    })
}
// 2.1 查询翻牌状态接口
export function queryStatus(params) {
    return request({
        url: `/${basePathfpz}/queryStatus/${channel}/${keywordfpz}`,
        method: 'post',
        params: params
    })
}
// 2.1 查询翻牌分享链接状态接口
export function queryShareStatus(params) {
    return request({
        url: `/${basePathfpz}/queryShareStatus/${channel}/${keywordfpz}`,
        method: 'post',
        params: params
    })
}
// 2.1 查询翻牌分享链接状态接口
export function queryIsAssistantInWeiXin(params) {
    return request({
        url: `/${basePathfpz}/queryIsAssistantInWeiXin/${channel}/${keywordfpz}`,
        method: 'post',
        params: params
    })
}
// 1.4获得分享标识接口
export function getSponsor(params) {
    return request({
        url: `/${basePathfpz}/getSponsor/${channel}/${keywordfpz}`,
        method: 'post',
        params: params
    })
}
// 1.4获得分享标识接口
export function deleteNotice(params) {
    return request({
        url: `/${basePathfpz}/deleteNotice/${channel}/${keywordfpz}`,
        method: 'post',
        params: params
    })
}

// 底部banner接口
export function getBanner(params) {
    return request({
        url: `/${basePathfpz}/getBanner/${channel}/${keywordfpz}`,
        method: 'post',
        params: params
    })
}

/* 签到年底回馈接口 */
// 查询用户活动信息
export function drawStatus(params) {
    return request({
        url: '/SignInFeedback/drawStatus/JT/SignInFeedback4JT',
        method: 'get',
        params: params
    })
}

// 抽奖接口
export function SignInDoPrize(params) {
    return request({
        url: '/SignInFeedback/SignInDoPrize/JT/SignInFeedback4JT',
        method: 'get',
        params: params
    })
}

// 获取iframe接口
export function getIframeList(params) {
    return request({
        url: '/cmcc_activity/activityConfig/iframeConfig.json',
        method: 'get',
        params: params
    })
}
// 天降礼包的出奖
export function customPrize(params) {
    return request({
        url: '/ActSignIn2023/customPrize/JT/' + keyword,
        method: 'get',
        params: params
    })
}

// 查询动态奖品配置信息
export function queryAwardInfo(params) {
    return request({
        url: '/ActSignIn2023/queryAwardInfo/JT/' + keyword,
        method: 'get',
        params: params
    })
}

// 天降礼包的查询修改
export function queryTjlbInfo(params) {
    return request({
        url: '/ActSignIn2023/queryTjlbInfo/JT/' + keyword,
        method: 'get',
        params: params
    })
}

// 查询补签卡任务状态接口
export function querySignCardTaskStatus(params) {
    return request({
        url: '/ActSignIn2023/querySignCardTaskStatus/JT/' + keyword,
        method: 'get',
        params: params
    })
}

// 查询补签卡任务状态接口
export function setSignCardTaskStatus(params) {
    return request({
        url: '/ActSignIn2023/setSignCardTaskStatus/JT/' + keyword,
        method: 'get',
        params: params
    })
}

// 补签接口
export function repairSign(params) {
    return request({
        url: '/ActSignIn2023/repairSign/JT/' + keyword,
        method: 'post',
        params: params
    })
}

export function querySubscribeTemplate(params) {
    return request({
        url: '/ActivityUnifyLogin/querySubscribeTemplate/miniProgram/SignIn',
        method: 'post',
        params: {
            schannel: 'jt',
            ...params
        }
    })
}
export function queryFamilyCircleZi(params = {}) {
    params.channel = 'sign'
    return request({
        url: '/app/familyCircle/queryUserCircleZiType',
        method: 'get',
        params: params
    })
}
export function agreeUpgrade(params = {}) {
    params.channel = 'sign'
    return request({
        url: '/app/familyCircle/agreeUpgrade',
        method: 'get',
        params: params
    })
}