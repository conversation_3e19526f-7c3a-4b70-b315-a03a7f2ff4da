<template>
  <div class="cz-box">
    <div class="cz-box-view" v-if="!loading">
      <template v-if="type === 'mango'">
        <cz-tab-item
          v-for="(item, index) in showMangList[showMangListIndex]"
          :type="type"
          :key="index"
          :index="index"
          :reg-exp="item.key"
          :number="item.number"
          :desc="item.tag"
          :checked="
            activeIndex === index &&
            checkNumber === item.number
          "
        />
      </template>
      <template v-else-if="type === 'plate'">
        <div class="cz-box-view-plate" @click="handlePlateSearch">
          <template v-if="showResetPlate">请输入车牌号搜索号码！</template>
          <template v-else>点击输入车牌号搜索号码</template>
        </div>
      </template>
      <!-- v-if="type !== 'mango'" -->
      <template v-else>
          <cz-tab-item
          v-for="(item, index) in list"
          :type="type"
          :key="index"
          :index="index"
          :reg-exp="item.key"
          :number="item.nums[numIndex[index].index]"
          :desc="item.tag"
          :nt="item.nt"
          :checked="
            activeIndex === index &&
            checkNumber === item.nums[numIndex[index].index]
          "
        />
      </template>
    </div>
    <div class="cz-box-loading" v-else>
      <van-loading color="#1989fa" />
    </div>
    <div v-if="type == 'plate'" v-show="showResetPlate" class="cz-box-reset plate" @click="handleReset">关闭</div>
    <div v-else class="cz-box-reset" @click="handleReset">换一换</div>
  </div>
</template>

<script>
// 心动专属
import { queryCustomNumber, queryMangoNumber } from '../../../../api'
import CzTabItem from './cz-tab-item.vue'
import { mapState } from 'vuex'
import _ from 'lodash'
export default {
  props: {
    q: {
      // 请求参数
      type: String,
      default: '',
      required: true
    },
    type: {
      type: String,
      default: ''
    },
    defaultNums: {
      type: Object,
      default: () => {
        return {}
      }
    },
    defaultLucky: {
      type: Object,
      default: () => {
        return {}
      }
    },
    choiceness: {
      type: Array,
      default: () => []
    },
    lucky: {
      type: Array,
      default: () => []
    }
  },
  components: {
    CzTabItem
  },
  data() {
    return {
      list: [],
      numIndex: [],
      loading: true,
      mangoBirthday: '', // 记录生日日期
      mangoYear: '', // 记录生日年份
      mango88List: [], // 88号码
      mangoDefaultList: [], // 66+520+521 号码集
      mangoBirthdayList: [], // 生日日期号码集
      mangoYearList: [], // 生日年份号码集
      SYXCardLsit: [], // 宋亚轩号码
      LYWCardLsit: [], // 刘耀文号码
      DCXCardLsit: [], // 丁程鑫号码
      YHXCardLsit: [], // 严浩翔号码
      HJLCardLsit: [], // 贺峻霖号码
      ZZYCardLsit: [], // 张真源号码
      MJQCardLsit: [], // 马嘉祺号码
      count: '', // 记录有数据的明星个数
      showMangList: [], // 准备展示的芒果卡列表
      showMangListIndex: 0, // 正在展示的芒果卡组index
      active: '',
      showResetPlate: false
    }
  },
  computed: {
    ...mapState({
      checkNumber: (state) => state.checkNumber,
      activeIndex: (state) => state.activeIndex
    })
  },
  created() {
    this.getList()
  },
  methods: {
    handlePlateSearch() {
      this.$emit('stop', 'plate')
      this.showResetPlate = true
    },
    hidePlateSearch() {
      this.showResetPlate = false
    },
    //   { title: '虎年吉祥', q: '2022_2022_22_22_22_22', type: 'bisque' }, params请求入参去重了q
    getList() {
      // const q_ =
      const params = [...new Set(this.q.split('_'))]
      if (this.type === 'plate') {
        this.loading = false
      } else if (this.type === 'mango') { // 获取芒果卡信息
        queryMangoNumber({
          q: params.join('_')
        })
          .then((res) => {
            const mangoData = _.cloneDeep(res.data) // 深拷贝接口芒果卡数据
            for (let i = 0; i < mangoData.length; i++) {
              mangoData[i].nums = []
            }
            let mangoNumberDataList = [] // 将芒果卡的所有号码数据存到一个列表
            this.mangoBirthday = res.data[16].key // 记录生日日期
            this.mangoYear = res.data[17].key // 记录生日年份
            res.data.forEach((item, index) => { // 循环接口数据列表
              mangoNumberDataList = mangoNumberDataList.concat(...item.nums) // 将接口号码数据存起来，跳转芒果卡时获取对应的checkCode
              item.nums.forEach(i => {
                mangoData[index].nums.push(i.number) // 处理数据，mangoData的nums列表改为全是号码的列表，方便处理
              })
            })
            this.$store.commit('UPDATE_MANGOLIST', mangoNumberDataList)
            this.disposeMango(mangoData) // 处理芒果卡数据
            this.loading = false
          })
          .catch(() => {
            this.loading = false
            this.$toast('活动太火爆了，请稍后再试吧~')
          })
      } else {
        // 经典139, 稀有靓号, 精选连号 三种号码去新库查询 ST25052902
        const nt = (['rare', 'rare139', 'blue'].includes(this.type)) ? 'rare' : ''
        queryCustomNumber({
          q: params.join('_'),
          nt
        })
          .then((res) => {
            console.log(params)
            if (res.result === 0) {
              if (params.length === 6) {
                this.initList(res.data, nt)
              } else {
                this.initList(this.initRequestData(res.data), nt)
              }
            }
            this.loading = false
          })
          .catch(() => {
            this.loading = false
            this.$toast('活动太火爆了，请稍后再试吧~')
          })
      }
    },
    // 处理芒果卡数据
    disposeMango(list) {
      const count = this.getStarData(list) // 接口号卡列表数据分组和填充,返回明星号卡情况
      this.showMangList = this.getShowMangList(count) // 通过count去获取展示列表
      console.log('展示的号码芒果卡号码组', this.showMangList)
      // this.showMangList = this.getShowMangoList(mango88List, mangoDefaultList, mangoBirthdayList, mangoYearList)
      // console.log('this.showMangList', this.showMangList)
    },
    // 将接口号卡数据按类别分组
    getStarData(list) {
      console.log('原始号卡数据list', list)
      // mango88List 88号码
      // mangoDefaultList 66+520+521 号码集
      // mangoBirthdayList 生日日期号码集
      // mangoYearList 生日年份号码集
      // SYXCardLsit 宋亚轩号码
      // LYWCardLsit 刘耀文号码
      // DCXCardLsit 丁程鑫号码
      // YHXCardLsit 严浩翔号码
      // HJLCardLsit 贺峻霖号码
      // ZZYCardLsit 张真源号码
      // MJQCardLsit 马嘉祺号码
      // count 记录有数据的明星
      let count = ''
      list.forEach((item) => {
        item.nums.forEach((itemSon) => {
          switch (item.key) {
            case '304':
            case '0304':
              this.SYXCardLsit.push({
                number: itemSon,
                key: item.key,
                tag: item.tag
              })
              break
            case '923':
            case '0923':
              this.LYWCardLsit.push({
                number: itemSon,
                key: item.key,
                tag: item.tag
              })
              break
            case '224':
            case '0224':
              this.DCXCardLsit.push({
                number: itemSon,
                key: item.key,
                tag: item.tag
              })
              break
            case '816':
            case '0816':
              this.YHXCardLsit.push({
                number: itemSon,
                key: item.key,
                tag: item.tag
              })
              break
            case '615':
            case '0615':
              this.HJLCardLsit.push({
                number: itemSon,
                key: item.key,
                tag: item.tag
              })
              break
            case '416':
            case '0416':
              this.ZZYCardLsit.push({
                number: itemSon,
                key: item.key,
                tag: item.tag
              })
              break
            case '1212':
              this.MJQCardLsit.push({
                number: itemSon,
                key: item.key,
                tag: item.tag
              })
              break
            case '88':
              this.mango88List.push({
                number: itemSon,
                key: item.key,
                tag: item.tag
              })
              break
            case '66':
            case '520':
            case '521':
              this.mangoDefaultList.push({
                number: itemSon,
                key: item.key,
                tag: item.tag
              })
              break
            case this.mangoBirthday:
              this.mangoBirthdayList.push({
                number: itemSon,
                key: item.key,
                tag: item.tag
              })
              break
            case this.mangoYear:
              this.mangoYearList.push({
                number: itemSon,
                key: item.key,
                tag: item.tag
              })
              break
          }
        })
      })
      // 如果明星号码只有一个，那就默认池子来补充
      if (this.SYXCardLsit.length === 1) {
        const newitem = this.mangoDefaultList.splice(0, 1)[0]
        this.SYXCardLsit.push(newitem)
      } else if (this.SYXCardLsit.length === 0) {
        count = '1'
      }
      if (this.LYWCardLsit.length === 1) {
        const newitem = this.mangoDefaultList.splice(0, 1)[0]
        this.LYWCardLsit.push(newitem)
      } else if (this.LYWCardLsit.length === 0) {
        count = count + '2'
      }
      if (this.DCXCardLsit.length === 1) {
        const newitem = this.mangoDefaultList.splice(0, 1)[0]
        this.DCXCardLsit.push(newitem)
      } else if (this.DCXCardLsit.length === 0) {
        count = count + '3'
      }
      if (this.YHXCardLsit.length === 1) {
        const newitem = this.mangoDefaultList.splice(0, 1)[0]
        this.YHXCardLsit.push(newitem)
      } else if (this.YHXCardLsit.length === 0) {
        count = count + '4'
      }
      if (this.HJLCardLsit.length === 1) {
        const newitem = this.mangoDefaultList.splice(0, 1)[0]
        this.HJLCardLsit.push(newitem)
      } else if (this.HJLCardLsit.length === 0) {
        count = count + '5'
      }
      if (this.ZZYCardLsit.length === 1) {
        const newitem = this.mangoDefaultList.splice(0, 1)[0]
        this.ZZYCardLsit.push(newitem)
      } else if (this.ZZYCardLsit.length === 0) {
        count = count + '6'
      }
      if (this.MJQCardLsit.length === 1) {
        const newitem = this.mangoDefaultList.splice(0, 1)[0]
        this.MJQCardLsit.push(newitem)
      } else if (this.MJQCardLsit.length === 0) {
        count = count + '7'
      }
      return count
    },
    // 获取展示的芒果卡列表数据
    getShowMangList(count) {
      const resultArr = []
      if (count.length === 7) { // 所有明星无号码数据，用默认池子去填充最多七组展示数据
        // 最多组数(不大于7)
        const maxGroup = Math.floor(this.mangoDefaultList.length / 6) > 7 ? 7 : Math.floor(this.mangoDefaultList.length / 6)
        const arrStore = []
        for (let i = 0; i < maxGroup; i++) {
          const arr = this.mangoDefaultList.splice(0, 2)
          arrStore.push(arr)
        }
        const fillArr = this.disposeTwoThreeRow()
        for (let i = 0; i < maxGroup; i++) {
          resultArr.push([...arrStore.shift(), ...fillArr.shift()])
        }
        console.log('全无明星号码，最大组数：' + maxGroup)
      } else if (count.length === 6) { // 只有一个明星的号码数据，其余明星没有
        console.log('只有一个明星的号码数据，其余明星没有')
        let starArr = [] // 有号卡的明星数据
        let groupStarArr = [] // 第一排数据
        if (this.count.indexOf('1') === -1) {
          starArr = this.SYXCardLsit
        } else if (this.count.indexOf('2') === -1) {
          starArr = this.LYWCardLsit
        } else if (this.count.indexOf('3') === -1) {
          starArr = this.DCXCardLsit
        } else if (this.count.indexOf('4') === -1) {
          starArr = this.YHXCardLsit
        } else if (this.count.indexOf('5') === -1) {
          starArr = this.HJLCardLsit
        } else if (this.count.indexOf('6') === -1) {
          starArr = this.ZYCardLsit
        } else if (this.count.indexOf('7') === -1) {
          starArr = this.MJQCardLsit
        }
        const length1 = Math.ceil(starArr.length / 2)
        for (let i = 0; i < length1; i++) {
          groupStarArr.push(starArr.splice(0, 2))
          if (groupStarArr[i].length === 1) {
            groupStarArr[i].push(this.mangoDefaultList.splice(0, 1)[0])
          }
        }
        if (groupStarArr.length > 7) {
          groupStarArr = starArr.slice(0, 7)
        }
        // 去获取剩下第二排第三排对应数据
        const fillArr = this.disposeTwoThreeRow()
        const length2 = groupStarArr.length
        for (let i = 0; i < length2; i++) {
          resultArr.push([...groupStarArr.shift(), ...fillArr.shift()])
        }
      } else {
        // 去获取剩下第二排第三排对应数据
        const fillArr = this.disposeTwoThreeRow()
        // 将对应的第二排第三排数据补充给对应明星的号卡组
        if (this.SYXCardLsit.length >= 2) {
          this.SYXCardLsit = [...this.SYXCardLsit.splice(0, 2), ...fillArr.shift()]
          resultArr.push(this.SYXCardLsit)
        }
        if (this.LYWCardLsit.length >= 2) {
          this.LYWCardLsit = [...this.LYWCardLsit.splice(0, 2), ...fillArr.shift()]
          resultArr.push(this.LYWCardLsit)
        }
        if (this.DCXCardLsit.length >= 2) {
          this.DCXCardLsit = [...this.DCXCardLsit.splice(0, 2), ...fillArr.shift()]
          resultArr.push(this.DCXCardLsit)
        }
        if (this.YHXCardLsit.length >= 2) {
          this.YHXCardLsit = [...this.YHXCardLsit.splice(0, 2), ...fillArr.shift()]
          resultArr.push(this.YHXCardLsit)
        }
        if (this.HJLCardLsit.length >= 2) {
          this.HJLCardLsit = [...this.HJLCardLsit.splice(0, 2), ...fillArr.shift()]
          resultArr.push(this.HJLCardLsit)
        }
        if (this.ZZYCardLsit.length >= 2) {
          this.ZZYCardLsit = [...this.ZZYCardLsit.splice(0, 2), ...fillArr.shift()]
          resultArr.push(this.ZZYCardLsit)
        }
        if (this.MJQCardLsit.length >= 2) {
          this.MJQCardLsit = [...this.MJQCardLsit.splice(0, 2), ...fillArr.shift()]
          resultArr.push(this.MJQCardLsit)
        }
      }
      return resultArr
    },
    // 用生日号码数据和默认池数据，生成每个展示号码组的第二三排数据，返回列表形式
    disposeTwoThreeRow() {
      const newArr = []
      for (let i = 0; i < 7; i++) {
        newArr.push([])
        // 依次补充3456位置的号卡，如果数据不够补充，则去掉该数组且跳出循环
        if (this.mangoBirthdayList.length > 0) {
          newArr[i].push(this.mangoBirthdayList.splice(0, 1)[0])
        } else if (this.mangoDefaultList.length > 0) {
          newArr[i].push(this.mangoDefaultList.splice(0, 1)[0])
        } else {
          newArr.pop()
          break
        }

        if (this.mangoBirthdayList.length > 0) {
          newArr[i].push(this.mangoBirthdayList.splice(0, 1)[0])
        } else if (this.mangoDefaultList.length > 0) {
          newArr[i].push(this.mangoDefaultList.splice(0, 1)[0])
        } else {
          newArr.pop()
          break
        }

        if (this.mangoYearList.length > 0) {
          newArr[i].push(this.mangoYearList.splice(0, 1)[0])
        } else if (this.mangoDefaultList.length > 0) {
          newArr[i].push(this.mangoDefaultList.splice(0, 1)[0])
        } else {
          newArr.pop()
          break
        }

        if (this.mangoYearList.length > 0) {
          newArr[i].push(this.mangoYearList.splice(0, 1)[0])
        } else if (this.mangoDefaultList.length > 0) {
          newArr[i].push(this.mangoDefaultList.splice(0, 1)[0])
        } else {
          newArr.pop()
          break
        }
      }
      const length = newArr.length
      // 不够配合明星七个组，则重复最后一组
      for (let i = 0; i < 7 - length; i++) {
        newArr.push(newArr[length - 1])
      }
      return newArr
    },
    //  key 出现次数——（key：211/985/生日号等）
    getNumberTime(val, arr) {
      let time = 0
      arr.forEach((item) => {
        if (val === item) {
          time++
        }
      })
      return time
    },
    // 初始化去重（q）返回的数据，及记录相同key出现的次数
    initRequestData(data) {
      const dataKey = {}
      let q = this.q.split('_')
      const list = []
      // 特殊处理，精准号码tab栏是ssm、msm，无法对应号码，提前先给出对应号码段
      if (this.type === 'accurate') {
        q = [data[0].key, data[0].key, data[0].key, data[1].key, data[1].key, data[1].key]
      }
      data.forEach((node) => {
        // （key：211/985/生日号等）
        // key 出现次数（keyTime）
        let keyTime = 0
        keyTime = this.getNumberTime(node.key, q)
        dataKey[node.key] = {
          ...node,
          keyTime,
          length: node.nums.length,
          time: 0 // 记录循环时操作的次数，作用：换一换
        }
      })
      q.forEach((item) => {
        const node = dataKey[item]
        let nums = []
        let index = 0
        if (node.time < node.length) {
          node.time++
          index = node.time - 1
          nums = node.nums
        }
        list.push({
          replaceKey: node.replaceKey || '',
          key: node.key,
          tag: node.tag,
          keyTime: node.keyTime,
          nums,
          index
        })
      })
      // 芒果卡做特殊处理
      return list
    },
    initList(list, nt) {
      const lLen = this.defaultLucky.nums.length
      const dLen = this.defaultNums.nums.length
      const numIndex = [] // 显示的表示
      const data = list.map((node) => {
        const nodeNumsLen = node.nums.length // 号码长度
        // let numIndexItem = {}
        if (nodeNumsLen === 0) {
          // 如果号码长度为空
          let len = dLen // 幸运号长度
          if (lLen > 0) {
            // 如果有幸运号，则代替当前节点
            len = lLen
            node = this.defaultLucky
          } else {
            // 否则使用精选号码
            node = this.defaultNums
          }
          numIndex.push({
            identity: 'random', // 标识
            index: this.getNumsIndex(len),
            max: len
          })
        } else {
          let tmpkey
          switch (node.key) {
            case 'AA':
              tmpkey = /(\d)\1{1}$/
              break
            case 'AAA':
              tmpkey = /(\d)\1{2}$/
              break
            case 'AAAA':
              tmpkey = /(\d)\1{3}/
              break
          }
          if (tmpkey) {
            node.key = tmpkey
          }
          node.nt = nt
          numIndex.push({
            identity: 'ordinary',
            index: node.index || 0,
            max: nodeNumsLen,
            keyTime: node.keyTime,
            nt
          })
        }
        return node
      })
      this.numIndex = numIndex
      this.list = data
    },
    handleReset() {
      this.loading = true
      this.$emit('reset') // 插码和、清除选中、开启自动轮播
      this.showResetPlate = false
      if (this.type === 'mango' && (this.showMangListIndex + 1 === this.showMangList.length || this.showMangList.length === 1)) {
        setTimeout(() => {
          this.showMangListIndex = 0
          this.loading = false
        }, 200)
      } else if (this.type === 'mango') {
        setTimeout(() => {
          this.showMangListIndex++
          this.loading = false
        }, 200)
      } else {
        setTimeout(() => {
          this.numIndex.forEach((node) => {
            if (node.identity === 'random') {
              let index = this.getNumsIndex(node.max)
              // 确保跟上一个随机数不相等
              while (index === node.index) {
                index = this.getNumsIndex(node.max)
              }
              node.index = index
            } else if (node.keyTime > 1) {
              // key 出现两次以上时
              node.index = (node.index + node.keyTime) % node.max
            }
            // 当 index 刚好是最后一位时，重新设置为零
            if (node.index === node.max - 1) {
              node.index = 0
            } else {
              node.index++
            }
          })
          this.loading = false
        }, 800)
      }

      // // console.log(this.numIndex)
    },
    getNumsIndex(length) {
      // 随机获取index
      const index = parseInt(Math.random() * length)
      // // console.log(length, index, '===> index')
      return index !== length ? index : index - 1
    }
  }
}
</script>

<style lang="scss" scoped>
.cz-box {
  position: relative;
  text-align: center;
  padding-top: 14px;
  &-loading {
    height: 480px;
    line-height: 480px;
  }
  &-view {
    position: relative;
    display: flex;
    justify-content: space-between;
    flex-wrap: wrap;
    margin: 0 12px;
    min-height: 480px;
    &-plate {
      background:url('~@/projects/app-card-zone/assets/images/tab/search.png') no-repeat center 30%;
      background-size: 90px auto;
      width: 686px;
      height: 480px;
      margin: 0 auto;
      color:#FFA09D;
      font-size: 24px;
      line-height: 480px;
    }
  }
  &-reset {
    width: 150px;
    opacity: 0.8;
    font-size: 28px;
    font-family: PingFangSC, PingFangSC-Regular;
    font-weight: 400;
    color: #ff453f;
    line-height: 40px;
    // vertical-align: top;
    margin: 0 auto;
    &::before {
      content: "";
      display: inline-block;
      width: 32px;
      height: 32px;
      margin-right: 18px;
      background: url("~@/projects/app-card-zone/assets/images/tab/reset.png")
        no-repeat;
      background-size: 100% auto;
      vertical-align: top;
      margin-top: 4px;
    }
    &.plate {
      &::before {
        background: url("~@/projects/app-card-zone/assets/images/tab/close.png") no-repeat;
        content: "";
        display: inline-block;
        width: 32px;
        height: 32px;
        margin-right: 18px;
        background-size: 100% auto;
        vertical-align: top;
        margin-top: 4px;
      }
    }
  }
}
</style>
