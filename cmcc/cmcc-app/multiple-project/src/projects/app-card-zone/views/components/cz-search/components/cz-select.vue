<template>
  <div class="czs-select">
    <template v-if="type === 'check'">
      <div
        :class="['czs-select-item', { 'active-checked': value !== '' }, type]"
        @click="handleCheckItem"
      >
        <span>{{ label }}</span>
      </div>
    </template>
    <template v-else>
      <div
        :class="[
          'czs-select-item',
          { 'active-rotate': showView },
          { active: value !== '' },
        ]"
        @click="handleItem"
      >
        <span v-if="value"> {{ value }} </span>
        <span v-else>{{ label }}</span>
      </div>
      <!-- 弹出层 -->
      <div v-show="showView" class="czs-select-view">
        <div class="czs-select-tab">
          <div
            v-for="item in options"
            :key="item.value"
            :class="['czs-select-tab__item', { active: item.value === value }]"
            @click="handleCheck(item.value)"
          >
            {{ item.label }}
          </div>
        </div>
        <div class="czs-select-loading" v-if="loading">
          <van-loading color="#1989fa" />
        </div>
      </div>
    </template>
  </div>
</template>

<script>
export default {
  name: 'CzSelect',
  data() {
    return {
      showView: false
    }
  },
  props: {
    loading: Boolean,
    value: {
      type: [String, Number],
      required: true
    },
    label: {
      type: [String, Number],
      default: '标题'
    },
    options: {
      // 条件 格式：[{ label: 'AAA', value: 'AAA' }]
      type: Array,
      default: () => []
    },
    type: {
      // type=check 为点击选择
      type: String,
      default: ''
    },
    checkValue: {
      //  type=check 选中后的值
      type: [String, Number],
      default: ''
    }
  },
  computed: {
    // 获取父组件
    selectGroup() {
      let parent = this.$parent
      let parentName = parent.$options.componentName
      while (parentName !== 'CzSelectGroup') {
        parent = parent.$parent
        parentName = parent.$options.componentName
      }
      return parent
    }
  },
  methods: {
    async handleCheck(value) {
      // 隐藏兄弟的弹出层
      await this.selectGroup.setChildrenHideView()
      const value_ = this.value === value ? '' : value
      this.$emit('input', value_)
      this.handleChange(value_)
    },
    handleCheckItem() {
      // 选中时默认返回label
      const value = this.checkValue ? this.checkValue : this.label
      this.$emit('input', this.value === value ? '' : value)
      this.handleChange(value)
    },
    handleChange(value) {
      // 调用父组件change事件，用于统一处理
      this.selectGroup.handleChange(value)
      this.$emit('change', value)
    },
    async handleItem() {
      if (!this.showView) {
        // 隐藏兄弟的弹出层
        await this.selectGroup.setChildrenHideView()
      }
      this.showView = !this.showView
    }
  }
}
</script>

<style lang="scss" scoped>
.czs-select {
  // position: relative;
  display: inline-block;
  &-loading {
    position: absolute;
    left: 0;
    bottom: 0;
    right: 0;
    top: 0;
    background-color: rgba(255, 255, 255, 0.4);
    display: flex;
    justify-content: center;
    align-items: center;
  }
  &-view {
    position: absolute;
    top: 100%;
    left: 0;
    // margin-left: -329px;
    width: 658px;
    height: 484px;
    background-color: rgba(0, 0, 0, 0.33);
    border-radius: 0 0 16px 16px;
  }
  &-tab {
    display: flex;
    flex-wrap: wrap;
    // justify-content: space-between;
    background-color: white;
    border-radius: 0 0 16px 16px;
    padding: 42px 0 1px;
    &__item {
      width: 120px;
      height: 60px;
      line-height: 60px;
      font-size: 26px;
      font-family: PingFangSC, PingFangSC-Regular;
      font-weight: 400;
      text-align: center;
      color: rgba(0, 0, 0, 0.8);
      background: #f5f5f5;
      border-radius: 30px;
      border: 2px solid #f5f5f5;
      margin-bottom: 36px;
      margin-left: 36px;

      &.active {
        background: white;
        border-color: #f3513b;
        color: #f3513b;
      }
    }
  }
  // &-top {
  //     padding-top: 13px;
  //     height: 80px;
  //     text-align: left;
  //     padding: 0 0 0 10px;
  //     // margin-bottom: 42px;
  // }
  &-item {
    position: relative;
    min-width: 180px;
    padding: 0 10px 0 20px;
    height: 64px;
    line-height: 60px;
    border: 2px solid transparent;
    border-radius: 8px;
    font-size: 28px;
    font-family: PingFangSC, PingFangSC-Regular;
    font-weight: 400;
    text-align: left;
    color: rgba(0, 0, 0, 0.8);
    background: #f5f5f5;
    &:not(.check)::after {
      content: "";
      // float: right;
      display: block;
      position: absolute;
      width: 0;
      height: 0;
      border-style: solid;
      border-width: 0 8px 14px;
      top: 22px;
      right: 20px;
      // margin: 22px 10px 0 14px;
      border-color: transparent transparent rgba(0, 0, 0, 0.5) transparent;
      transform: rotate(0);
      transition: transform 0.3s;
    }
    &.active-rotate {
      &::after {
        transform: rotate(180deg);
      }
    }
    &.active {
      border-color: #f3513b;
      color: #f3513b;
      &::after {
        border-bottom-color: #f3513b;
      }
    }
    &.check {
      min-width: 114px;
    }
    &.active-checked {
      background-color: white;
      border-color: #f3513b;
      color: #f3513b;
    }
  }
}
</style>
