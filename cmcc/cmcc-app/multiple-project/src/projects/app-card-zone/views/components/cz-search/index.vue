<template>
  <div
    :class="[
      'cz-search',
      { active: showList.length === 0 && showClose },
      { 'active-show': showClose },
    ]"
  >
    <div :class="['cz-search-content', { active: showClose }]">
      <div class="cz-search-input">
        <div class="left" :class="searchMode">

          <div class="icon" v-if="searchMode === 'plate'">
            <span :class="searchType === 'petrol' ? 'active' : ''" @click="handleSearchType('petrol')">燃油车</span>
            <span :class="searchType === 'elec' ? 'active' : ''" @click="handleSearchType('elec')">新能源</span>
          </div>
          <div class="icon" v-else>
            <span :class="searchType === 'fuzzy' ? 'active' : ''" @click="handleSearchType('fuzzy')">模糊</span>
            <span :class="searchType === 'exact' ? 'active' : ''" @click="handleSearchType('exact')">精确</span>
          </div>
          <template v-if="searchType === 'fuzzy'">
            <input
              :class="['ipt', { active: searchVal !== '' }]"
              v-model="searchVal"
              type="number"
            />
            <span v-show="showDesc" class="placeholder"
              >请输入您喜欢的数字，如666</span
            >
            <div v-show="showClose" class="close-btn" @click="handleClose()">
              <van-icon name="cross" />
            </div>
          </template>
          <div v-else class="exact-list">
            <span v-if="searchMode === 'plate'" :class="searchMode">京</span>
            <template v-for="(item,key) in exactList">
              <input
                v-show="!searchMode || (searchMode && key > (searchType === 'petrol' ? 4: 3))"
                :key="key"
                v-model="exactList[key]"
                :readonly="key === 0"
                :ref="`numRef`"
                maxlength="1"
                :type="isIos ? 'tel' : 'number'"
                @input="handleExactInput(exactList[key], key, $event)"
                @keydown="handleExactDown(exactList[key], key, $event)"
                @keyup="handleExactUp(exactList[key], key, $event)"
              />
            </template>
            <!-- <input
              v-for="(item,key) in exactList"
              :key="key"
              v-model="exactList[key]"
              :readonly="key === 0"
              :ref="`numRef${key}`"
              maxlength="1"
              :type="isIos ? 'tel' : 'number'"
              @input="handleExactInput(exactList[key], key, $event)"
              @keydown="handleExactDown(exactList[key], key, $event)"
              @keyup="handleExactUp(exactList[key], key, $event)"
            /> -->
          </div>
        </div>
        <div class="btn" @click="getSearchData()">搜索</div>
      </div>
      <cz-select-group
        ref="selectGroup"
        v-show="showClose && searchType === 'fuzzy'"
        @change="handleSelectGroupChange"
      >
        <cz-select
          v-model="checkFour"
          label="不含4"
          check-value="1"
          type="check"
        />
        <cz-select
          v-model="numberRule"
          label="号码规则"
          :options="numberRuleOptions"
        />
        <cz-select
          v-model="numberSegment"
          label="号码段"
          :loading="loadingOption"
          :options="numberSegmentOptions"
        />
      </cz-select-group>
      <div
        :class="['cz-search-view', { active: showList.length < 5 }]"
        v-show="showClose"
      >
        <cz-select-list
          v-if="showList.length > 0"
          :regExp="keyword"
          :exactKeyword="searchType !== 'fuzzy' ? exactList: null"
          :data="showList"
        />
        <div v-else-if="!loading" class="cz-search-empty">
          <img src="../../../assets/images/empty.png" />
          <p>很抱歉，没有找到您想要的号码，建议您换个号码或筛选条件试试吧~</p>
        </div>
      </div>
    </div>
    <div class="cz-search-reset">
      <span v-if="showReset"  @click="handleReset">换一换</span>
      <span v-if="showClose && searchType !== 'fuzzy'"  @click="handleClose(searchMode)" class="close">关 闭</span>
    </div>
    <div class="cz-search-loading" v-show="loading">
      <van-loading color="#1989fa" />
    </div>
  </div>
</template>

<script>
import CzSelect from './components/cz-select.vue'
import CzSelectGroup from './components/cz-select-group.vue'
import CzSelectList from './components/cz-select-list.vue'
import { qureyNumPrefix, searchNum, exactSearchNum } from '../../../api/index'
import { multiTrack } from '../../../utils/utils'
import CHANNEL from '../../../../../utils/channel'

import '../../../assets/font/din.css'
// const keyAllow = [
//   '1', '2', '3', '4', '5', '6', '7', '8', '9', '0', 'Backspace'
// ]
export default {
  components: {
    CzSelectGroup,
    CzSelectList,
    CzSelect
  },
  props: {
    searchMode: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      isIos: CHANNEL.isIOS(),
      searchType: 'fuzzy',
      exactList: ['1', '', '', '', '', '', '', '', '', '', ''],
      searchVal: '',
      numberRule: '',
      numberSegment: '',
      checkFour: '',
      checkNum: '',
      numberRuleOptions: [
        { label: '不限', value: '' },
        { label: 'AABB', value: 'AABB' },
        { label: 'AABA', value: 'AABA' },
        { label: 'ABAB', value: 'ABAB' },
        { label: 'AAAA', value: 'AAAA' },
        { label: 'AAA', value: 'AAA' }
      ],
      numberSegmentOptions: [],
      keyword: '',
      numbers: [],
      numbersLength: 0,
      start: 0,
      end: 5,
      showList: [],
      showClose: false,
      loadingOption: false,
      loading: false,
      status: false // 记录是否在当前组件选中的值
    }
  },
  watch: {
    searchMode: {
      handler(val, old) {
        if (val && !old) {
          this.searchType = 'petrol'
        } else {
          this.searchType = 'fuzzy'
        }
      },
      imediate: true
    }
  },
  computed: {
    checkNumber() {
      return this.$store.state.checkNumber
    },
    showReset() {
      // const length = this.showList.length
      // console
      // && this.start + 6 < this.numbersLength
      if (
        this.showClose &&
        this.numbersLength > 5 &&
        this.showList.length > 0
      ) {
        return true
      }
      return false
    },
    showDesc() {
      const searchVal = this.searchVal
      if (searchVal) {
        return false
      }
      return true
    }
  },
  methods: {
    handleSearchType(type) {
      this.searchType = type
      if (this.searchType === 'exact') {
        multiTrack('P00000126256', '中国移动APP_集团号卡专区聚合页_精确搜索')
      } else {
        multiTrack('P00000126257', '中国移动APP_集团号卡专区聚合页_模糊搜索')
      }
      this.handleClose(this.searchMode)
    },
    handleExactUp(item, index, $event) {
      console.log([$event.key, $event.code, item.length], 'up输入事件')
    },
    handleExactDown(item, index, $event) {
      console.log([$event.key, $event.code, item.length], 'down输入事件')
      if ($event.key === 'Backspace' && index > 1) {
        if (this.exactList[index]) {
          this.$set(this.exactList, index, '')
        } else {
          setTimeout(() => {
            this.$refs.numRef[index - 1].focus()
          }, 100)
        }
        return false
      }
    },
    handleExactInput(item, index, $event) {
      if (isNaN(item)) {
        item = ''
      } else if (item.toString().length > 1) {
        item = item[0]
      }
      this.$set(this.exactList, index, item)
      if (item.toString() && index < 10) {
        console.log([index, this.$refs.numRef], 'input')
        this.$refs.numRef[index + 1].focus()
      }
    },
    qureyNumPrefix() {
      this.loadingOption = true
      qureyNumPrefix()
        .then((res) => {
          if (res.result === 0) {
            const numberSegmentOptions = res.data.map((value) => {
              return { label: value, value }
            })
            numberSegmentOptions.unshift({ label: '不限', value: '' })
            this.numberSegmentOptions = numberSegmentOptions
          }
          this.loadingOption = false
        })
        .catch(() => {
          this.loadingOption = false
        })
    },
    handleSelectGroupChange(e) {
      // // console.log(e)
      // this.searchVal = ''
      this.getSearchData(true)
    },
    hideSelectMc() {
      this.$refs.selectGroup && this.$refs.selectGroup.setChildrenHideView()
    },
    handleClose(target) {
      if (this.loading) {
        return
      }

      if (this.status) {
        this.status = false
        this.$store.commit('CLEAN_USERCHECKDATA') // 重置仓库数据
      }

      this.searchVal = ''
      this.numberRule = ''
      this.numberSegment = ''
      this.checkFour = ''
      this.hideSelectMc()
      this.showClose = false
      this.exactList = ['1', '', '', '', '', '', '', '', '', '', '']
      if (close) {
        this.$emit('close', target)
      }
    },
    handleReset() {
      this.loading = true
      multiTrack('P00000025403', '中国移动APP_集团号卡专区聚合页_换一换')

      setTimeout(() => {
        const numbersLength = this.numbersLength
        // let end = this.end + 5
        // if (end > numbersLength) {
        //     end = end % numbersLength
        // }
        this.start = (this.start + 5) % numbersLength
        this.end = (this.end + 5) % numbersLength
        // this.end = end
        this.getShowList()
        // console.log(this.start, this.end)
        if (this.checkNumber) {
          this.$store.commit('CLEAN_USERCHECKDATA') // 重置仓库数据
        }
        this.loading = false
      }, 800)
    },
    getShowList() {
      const numbers = this.numbers
      if (this.start < this.end) {
        this.showList = numbers.slice(this.start, this.end)
      } else {
        const head = numbers.slice(this.start, this.numbersLength)
        const footer = numbers.slice(0, this.end)
        this.showList = [].concat(head, footer)
      }
    },
    getSearchData(isCheck = false) {
      if (this.searchType === 'exact' && this.exactList.join('') === '1') {
        this.$toast('请输入您要搜索的内容~')
        return
      }
      if (this.searchType === 'fuzzy' && !isCheck && !this.searchVal) {
        this.$toast('请输入您要搜索的内容~')
        return
      }
      multiTrack('P00000025415', '中国移动APP_集团号卡专区聚合页_搜索')
      if (this.loading) return
      this.showClose = true
      this.loading = true
      this.hideSelectMc()
      this.$emit('search')
      if (this.searchType !== 'fuzzy') {
        this.exactSearch()
      } else {
        this.normalSearch()
      }
    },
    exactSearch() {
      exactSearchNum({
        q: this.exactList.join(',')
      }).then((res) => {
        this.loading = false
        if (res.result === 0) {
          const { numbers } = res.data
          this.start = 0
          this.end = 5
          this.numbers = numbers
          this.numbersLength = numbers.length
          this.getShowList()
        }
      }).catch(() => {
        this.loading = false
        this.$toast('活动太火爆了，请稍后再试吧~')
      })
    },
    normalSearch() {
      // del4 不含4 query false integer(int32)
      // 1：不含4，其他不
      // 限
      // keyword 关键字 query false string 例：88
      // prefix 前缀，号
      // 段
      // query false string 例：138
      // tag 标签 query false string 例：AAA
      // const tag = this.numberRule
      searchNum({
        del4: this.checkFour,
        keyword: this.searchVal,
        prefix: this.numberSegment,
        tag: this.numberRule
      })
        .then((res) => {
          this.loading = false
          if (res.result === 0) {
            const { keyword, numbers } = res.data
            if (!keyword && this.numberRule) {
              this.keyword = this.getKeyword(this.numberRule)
            } else if (!keyword && this.numberSegment) {
              this.keyword = this.numberSegment
            } else {
              this.keyword = keyword
            }
            this.start = 0
            this.end = 5
            this.numbers = numbers
            this.numbersLength = numbers.length
            this.getShowList()
          }
        })
        .catch(() => {
          this.loading = false
          this.$toast('活动太火爆了，请稍后再试吧~')
        })
    },
    getKeyword(keyword) {
      let key = keyword
      switch (keyword) {
        case 'AA':
          key = /(\d)\1{1}/
          break
        case 'AAA':
          key = /(\d)\1{2}/
          break
        case 'AAAA':
          key = /(\d)\1{3}/
          break
        case 'ABAB':
          key = /(\d\d)\1{1}/
          break
        case 'AABB':
          key = /(\d)\1{1}(\d)\2{1}/
          break
        case 'AABA':
          key = /(\d)\1{1}\d\1/
          break
      }
      return key
    }
  },
  mounted() {
    // this.getSearchData()
    this.qureyNumPrefix()
  }
}
</script>

<style lang="scss" scoped>
.cz-search {
  position: absolute;
  left: 16px;
  top: 20px;
  right: 16px;
  // padding-top: 20px;
  z-index: 1;
  &.active-show {
    bottom: 20px;
  }
  &.active {
    background: #ffffff;
    border-radius: 16px;
  }
  &-loading {
    position: absolute;
    left: 0;
    bottom: 0;
    right: 0;
    top: 0;
    background-color: rgba(255, 255, 255, 0.6);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1;
  }
  &-empty {
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    transform: translateY(-50%);
    img {
      width: 320px;
      height: 178px;
    }
    p {
      opacity: 0.8;
      width: 446px;
      line-height: 40px;
      font-size: 28px;
      text-align: center;
      margin: 36px auto 0;
    }
  }

  &-content {
    padding: 20px 0 0;
    border-radius: 16px;
    // background: #ffffff;
    &.active {
      background: #ffffff;
    }
  }
  &-view {
    position: relative;
    padding-top: 28px;
    margin-bottom: 10px;
    height: 484px;
    &.active {
      height: 538px;
    }
  }
  &-input {
    overflow: hidden;
    margin: 0 16px 0 8px;
    .left {
      float: left;
      position: relative;
      width: 520px;
      height: 64px;
      background-color: #ffe8e8;
      border-radius: 32px;
      padding: 0 20px 0 150px;
      overflow: hidden;
      z-index: 0;
      &.plate {
        padding-left: 180px;
        font-size: 40px;
        .icon {
           width: 164px;
          span {
            width: 100px;
          }
        }
      }
      .placeholder {
        position: absolute;
        font-size: 24px;
        font-family: PingFangSC, PingFangSC-Regular;
        font-weight: 400;
        text-align: left;
        color: rgba($color: #000000, $alpha: 0.4);
        line-height: 64px;
        left: 150px;
        top: 0;
        z-index: -1;
      }
      .icon {
        // margin: 0px 12px 0 16px;
        position: absolute;
        left: 0;
        top: 0;
        width: 140px;
        height: 64px;
        display: flex;
        // background: url("~@/projects/app-card-zone/assets/images/search.png") no-repeat;
        background-size: 100% auto;
        font-size: 24px;
        font-weight: 400;
        & :first-child {
            border-radius: 32px 0 0  32px;
        }
        & :last-child {
          border-radius: 0 32px 32px 0;
        }
        span{
          display: flex;
          justify-content: center;
          height: 64px;
          line-height: 64px;
          width: 70px;
          background: white;
          &.active {
            background: red;
            color: white
          }
        }
        &.plate {
          width: 164px;
          span {
            width: 100px;
          }
        }
      }
    }
    .ipt {
      width: 100%;
      font-size: 36px;
      font-family: PingFangSC, PingFangSC-Medium;
      font-weight: 500;
      text-align: left;
      color: #ff413b;
      height: 64px;
      // line-height: 0;
      // line-height: 64px;
      background-color: transparent;
      border: none;
      vertical-align: top;
      // &.active {
      //     font-size: 36px;
      // }
    }

    .close-btn {
      position: absolute;
      color: #ffe8e8;
      font-size: 20px;
      width: 28px;
      height: 28px;
      text-align: center;
      line-height: 30px;
      border-radius: 50%;
      background-color: rgba(255, 64, 58, 0.5);
      right: 26px;
      top: 16px;
    }
    .btn {
      width: 98px;
      height: 64px;
      background: linear-gradient(270deg, #ff403a 98%, #ff716c 2%);
      border-radius: 46px;
      margin-left: auto;
      font-size: 28px;
      font-family: PingFangSC, PingFangSC-Medium;
      font-weight: 500;
      color: #ffffff;
      line-height: 64px;
    }
    .exact-list {
      display: flex;
      flex-wrap: wrap;
      width:100%;
      justify-content: space-between;
      align-items: center;
      height: 64px;
      input {
        width: 28px;
        height: 54px;
        background: #ffffff;
        border: 2px solid #ffc8c8;
        border-radius: 8px;
        font-size: 30px;
        padding: 0;
        text-align: center;
        font-family: 'din_condensedbold';
      }
    }
  }
  &-reset {
    width: 400px;
    display: flex;
    justify-content: space-evenly;
    align-items: center;
    opacity: 0.8;
    height: 40px;
    font-size: 28px;
    font-family: PingFangSC, PingFangSC-Regular;
    font-weight: 400;
    color: #ff453f;
    line-height: 40px;
    margin: 0 auto;
    span {
      &::before {
        content: "";
        display: inline-block;
        width: 32px;
        height: 32px;
        margin-right: 18px;
        background: url("~@/projects/app-card-zone/assets/images/tab/reset.png")
          no-repeat;
        background-size: 100% auto;
        vertical-align: top;
        margin-top: 4px;
      }
      &.close {
        &::before {
          background-image: url("~@/projects/app-card-zone/assets/images/tab/close.png");
        }
      }
    }
  }
}
</style>
