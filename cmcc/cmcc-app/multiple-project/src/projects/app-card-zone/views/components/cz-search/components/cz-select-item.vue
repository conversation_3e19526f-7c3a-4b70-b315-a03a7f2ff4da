<template>
  <div :class="['czs-item', { active: checked }]" @click="handleXw">
    <div class="czs-item-number" v-html="text"></div>
    <div class="czs-item-btn">选我</div>
  </div>
</template>

<script>
import { multiTrack } from '../../../../utils/utils'
export default {
  props: {
    number: {
      type: [String, Number],
      default: '',
      required: true
    },
    regExp: {
      validator: function (value) {
        // 这个值必须匹配
        const isRegExp = (value) => {
          return Object.prototype.toString.call(value) === '[object RegExp]'
        }
        return isRegExp(value) || typeof value === 'string'
      },
      required: true
    },
    exactKeyword: {
      type: Array,
      default() {
        return null
      }
    }
  },
  computed: {
    checkNumber() {
      return this.$store.state.checkNumber
    },
    checked() {
      return this.number === this.checkNumber
    },
    text() {
      if (this.exactKeyword) {
        return this.number.split('').map((item, key) => {
          if (this.exactKeyword[key] === item) {
            return `<span class='color-red'>${item}</span>`
          }
          return item
        }).join('')
      } else {
        const [head, middle, footer] = String(this.number)
          .replace(this.regExp, (a) => ` ${a} `)
          .split(' ')
        return `${head}<span class='color-red'>${middle}</span>${footer}`
      }
    }
  },
  methods: {
    handleXw() {
      multiTrack('220902_HKZQWXC_JHY_XZHM')
      if (this.number !== this.checkNumber) {
        this.$store.commit('UPDATE_CHECKNUMBER', this.number) // 更新仓库中的号码
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.czs-item {
  width: 590px;
  height: 72px;
  line-height: 72px;
  border: 2px solid transparent;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 26px;
  margin: 0 auto 20px;
  border-radius: 10px;
  &.active {
    border-color: #f3513b;
  }
  &-number {
    font-size: 28px;
    color: rgba(0, 0, 0, 0.8);
  }
  &-btn {
    width: 88px;
    height: 40px;
    line-height: 40px;
    font-size: 22px;
    text-align: center;
    background: linear-gradient(270deg, #ff403a 98%, #ff716c 2%);
    color: white;
    border-radius: 43px;
  }
}
</style>
