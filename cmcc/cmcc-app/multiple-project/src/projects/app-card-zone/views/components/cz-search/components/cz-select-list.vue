<template>
  <div class="czs-list">
    <cz-select-item
      v-for="item in data"
      :key="item"
      :number="item"
      :regExp="regExp"
      :exactKeyword="exactKeyword"
    />
  </div>
</template>

<script>
import CzSelectItem from './cz-select-item.vue'
export default {
  components: {
    CzSelectItem
  },
  props: {
    regExp: {
      validator: function (value) {
        // 这个值必须匹配
        const isRegExp = (value) => {
          return Object.prototype.toString.call(value) === '[object RegExp]'
        }
        return isRegExp(value) || typeof value === 'string'
      },
      required: true
    },
    exactKeyword: {
      type: Array,
      default() {
        return null
      }
    },
    data: {
      type: Array,
      default: () => []
    }
  }
}
</script>
