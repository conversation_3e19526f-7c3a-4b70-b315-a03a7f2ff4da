<template>
  <div class="cz-tab">
    <van-tabs animated v-model="active" swipeable @change="changeTab" @click="clickTab">
      <van-tab v-for="(item, index) in tabs" :key="index" :title="item.title">
        <cz-tab-view
          :q="item.q"
          :type="item.type"
          :active="active"
          :default-nums="defaultNums"
          :default-lucky="defaultLucky"
          @reset="handleReset"
          @stop="handelStop"
          @start="autoPlay"
        />
      </van-tab>
    </van-tabs>
  </div>
</template>

<script>
import { getUrlKey } from '@/utils/utils'
import CzTabView from './components/cz-tab-view.vue'
import { multiTrack } from '../../../utils/utils'
import { mapState } from 'vuex'

let time
export default {
  props: {
    // 幸运号 88 66
    defaultLucky: {
      type: Object,
      default: () => {
        return {}
      }
    },
    // 移动精选
    defaultNums: {
      type: Object,
      default: () => {
        return {}
      }
    },
    // tab参数信息
    tabs: {
      type: Array,
      default: () => {
        return []
      }
    }
  },
  components: {
    CzTabView
  },
  data() {
    return {
      active: 0,
      openAutoPlay: true
    }
  },
  computed: {
    ...mapState({
      checkNumber: (state) => state.checkNumber,
      activeIndex: (state) => state.activeIndex
    })
  },
  watch: {
    // 监听用户的选择，若用户选择号码则暂停自动滑动，若用户未选择号码，开启自动滑动
    activeIndex(val) {
      if (val !== '') {
        this.clearAutoPlay()
      } else {
        this.autoPlay()
      }
    }
  },
  mounted() {
    const { tab } = this.$route.query
    const queryTab = tab || getUrlKey('tab')
    if (queryTab) {
      this.active = Number(queryTab)
      this.openAutoPlay = false
    } else {
      this.autoPlay()
    }
  },
  destroyed() {
    this.clearAutoPlay()
  },
  methods: {
    changeTab() {
      if (this.activeIndex !== '') {
        this.$store.commit('CLEAN_USERCHECKDATA') // 清理仓库数据
      }
      this.clearAutoPlay()
      this.autoPlay()
    },
    clickTab(index, title) {
      this.$emit('stop')
      const i = 25414 - this.active
      multiTrack(`P000000${i}`, `中国移动APP_集团号卡专区聚合页_TAB${this.active + 1}_${title}`)
    },
    autoPlay() {
      if (!time && this.openAutoPlay) {
        const max = this.tabs.length || 5
        time = setInterval(() => {
          if (this.active < max) {
            this.active++
          } else {
            this.active = 0
          }
          this.$store.commit('CLEAN_USERCHECKDATA') // 清理仓库数据
        }, 4000)
      }
    },
    handleReset() {
      multiTrack('P00000025403', '中国移动APP_集团号卡专区聚合页_换一换')
      this.$emit('stop')
      this.changeTab()
    },
    clearAutoPlay() {
      if (time) {
        clearInterval(time)
        time = null
      }
    },
    handelStop(target) {
      console.log(target, 'handelStop')
      this.clearAutoPlay()
      if (target) {
        this.$emit('stop', target)
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.cz-tab {
  position: relative;
  z-index: 0;
  margin: 0 20px;
  ::v-deep {
    .van-tabs__nav {
      background-color: transparent;
    }
    .van-tabs__line {
      // opacity: 0;
      width: 46px;
      height: 16px;
      background: url("~@/projects/app-card-zone/assets/images/tab/line.png")
        no-repeat;
      background-size: 100% auto;
      bottom: 40px;
    }
    .van-tabs__wrap {
      height: 88px;
    }
    .van-tab {
      // height: 28px;
      opacity: 0.8;
      font-size: 24px;
      font-family: PingFangSC, PingFangSC-Regular;
      font-weight: 400;
      color: rgba(0, 0, 0, 0.8);
      &.van-tab--active {
        font-size: 28px;
        font-family: PingFangSC, PingFangSC-Medium;
        font-weight: bold;
        color: #ff403a;
      }
    }
  }
}
</style>
