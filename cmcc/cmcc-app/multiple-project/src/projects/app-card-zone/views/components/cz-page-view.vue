<template>
  <transition name="fade">
    <div v-if="showView" class="cz-page-view">
      有<span class="color-red">{{
        checkNumber !== "" ? checkedNum : num
      }}</span
      >人{{ checkNumber !== "" ? checkedDesc : desc }}
    </div>
  </transition>
</template>

<script>
let time
let delay
export default {
  props: {
    min: {
      type: [Number, String],
      default: 18
    },
    max: {
      type: [Number, String],
      default: 60
    },
    checkedMax: {
      type: [Number, String],
      default: 4
    },
    desc: {
      type: String,
      default: '正在浏览移动靓号'
    },
    checkedDesc: {
      type: String,
      default: '正在查看您选中的号码'
    }
  },
  watch: {
    checkNumber(val) {
      this.clearDelay()
      if (val) {
        const num = this.getNum(this.checkedMax)
        this.showView = !!num
        this.checkedNum = num
      } else {
        this.showView = false
      }
      this.initView()
    }
  },
  data() {
    return {
      num: 0,
      checkedNum: 0,
      showView: false
    }
  },
  computed: {
    checkNumber() {
      return this.$store.state.checkNumber
    }
  },
  methods: {
    initView(time = 2000) {
      this.clearAutoPlay()
      delay = setTimeout(() => {
        if (!this.checkNumber) {
          this.showView = true
        }
        this.autoPlay()
      }, time)
    },
    autoPlay() {
      time = setInterval(() => {
        const checkedMax = this.getNum(this.checkedMax)
        this.num = this.getNum(this.max, this.min)
        this.showView = checkedMax !== 0 ? !this.showView : false
        this.checkedNum = checkedMax
      }, 5000)
    },
    clearAutoPlay() {
      clearInterval(time)
      time = null
    },
    clearDelay() {
      clearTimeout(delay)
      delay = null
    },
    getNum(max = 1, min = 0) {
      // 随机获取index
      // const maxNum = min > 0? max - min
      const num = parseInt(Math.random() * (max - min)) + min
      return num !== max ? num : num - 1
    }
  },
  created() {
    // if (this.fixed) {
    this.num = this.getNum(this.max, this.min)
    this.checkedNum = this.getNum(this.checkedMax)
    // }
  },
  destroyed() {
    this.clearAutoPlay()
    this.clearDelay()
  }
}
</script>

<style lang="scss" scoped>
.cz-page-view {
  position: fixed;
  right: 0;
  top: 234px;
  min-width: 376px;
  height: 64px;
  line-height: 64px;
  background: rgba(0, 0, 0, 0.6);
  border-radius: 100px 0px 0px 100px;
  font-size: 28px;
  font-family: PingFangSC, PingFangSC-Regular;
  font-weight: 400;
  text-align: right;
  color: #ffffff;
  padding: 0 28px 0 34px;
  z-index: 1;
}
</style>
