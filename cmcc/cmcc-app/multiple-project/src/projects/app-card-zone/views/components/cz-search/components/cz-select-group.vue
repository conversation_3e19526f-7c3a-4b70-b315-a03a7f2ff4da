<template>
  <div class="czs-select-group">
    <slot></slot>
  </div>
</template>

<script>
export default {
  name: 'CzSelectGroup',
  componentName: 'CzSelectGroup',
  methods: {
    setChildrenHideView() {
      return new Promise((resolve) => {
        const children = this.$children
        for (let i = 0; i < children.length; i++) {
          this.$children[i].showView = false
        }
        resolve()
      })
    },
    handleChange(e) {
      this.$emit('change', e)
    }
  }
}
</script>

<style lang="scss" scoped>
.czs-select-group {
  position: relative;
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;
  padding: 26px 22px 0;
  z-index: 1;
}
</style>
