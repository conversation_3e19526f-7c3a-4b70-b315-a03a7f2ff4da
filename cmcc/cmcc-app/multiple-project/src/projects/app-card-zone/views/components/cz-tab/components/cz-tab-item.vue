<template>
  <div :class="['cz-tab-item', { checked }, type]" @click="clickItem">
    <div class="cz-tab-item__desc">
      <span>{{ desc }}</span>
    </div>
    <div class="cz-tab-item__text">
      <p v-html="text"></p>
    </div>
  </div>
</template>

<script>
import { multiTrack } from '../../../../utils/utils'
import { mapState } from 'vuex'
export default {
  props: {
    number: {
      type: [String, Number],
      default: '',
      required: true
    },
    regExp: {
      validator: function (value) {
        // 这个值必须匹配
        const isRegExp = (value) => {
          return Object.prototype.toString.call(value) === '[object RegExp]'
        }
        return isRegExp(value) || typeof value === 'string'
      },
      required: true
    },
    index: {
      type: [Number, String],
      default: ''
    },
    checked: Boolean,
    type: {
      type: String,
      default: ''
    },
    active: {
      type: [Number, String],
      default: ''
    },
    desc: {
      type: String,
      default: '心动号'
    },
    nt: {
      type: String,
      default: ''
    }
  },
  computed: {
    ...mapState({
      checkNumber: (state) => state.checkNumber,
      checkType: (state) => state.checkType,
      activeIndex: (state) => state.activeIndex
    }),
    text() {
      const [head, middle, footer] = String(this.number)
        .replace(this.regExp, (a) => ` ${a} `)
        .split(' ')
      return `${head}<span class='color-red'>${middle || ''}</span>${footer}`
    }
  },
  methods: {
    clickItem() {
      if (this.activeIndex === this.index && this.checkNumber === this.number) { // 点击同一个号码，取消选定效果，清理仓库数据
        this.$store.commit('CLEAN_USERCHECKDATA')
      } else { // 选定号码，将号码信息更新到仓库
        const i = 25414 - this.active
        multiTrack('P000000' + i, '中国移动APP_集团号卡专区聚合页_TAB' + (this.active + 1) + '选中号码')
        this.$store.commit('UPDATE_USERCHECKDATA', {
          checkNumber: this.number,
          checkType: this.type,
          desc: this.desc,
          activeIndex: this.index,
          nt: this.nt
        })
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.cz-tab-item {
  position: relative;
  width: 304px;
  height: 140px;
  background: url("~@/projects/app-card-zone/assets/images/tab/pink.png")
    no-repeat;
  background-size: 100% auto;
  margin-bottom: 20px;
  text-align: right;
  &__desc {
    font-size: 18px;
    color: white;
    margin: 26px 24px 18px auto;
    height: 26px;
    line-height: 26px;
    font-family: PingFangSC, PingFangSC-Medium;
    font-weight: 500;
    color: #ffffff;
    span {
      display: inline-block;
      background: linear-gradient(90deg, #ff5c45, #ff5c45 49%, #fea469);
      border-radius: 12px 12px 12px 0px;
      padding: 0 13px 0 9px;
      vertical-align: top;
    }
  }

  &__text {
    width: auto;
    font-size: 40px;
    text-align: center;
    font-family: PingFangSC, PingFangSC-Medium;
    font-weight: bold;
    color: rgba(0, 0, 0, 0.8);
    line-height: 52px;
  }
  &.checked {
    background-image: url("~@/projects/app-card-zone/assets/images/tab/pink-active.png");
  }

  &.mango {
    background-image: url("~@/projects/app-card-zone/assets/images/tab/mango.png");
    &.checked {
      background-image: url("~@/projects/app-card-zone/assets/images/tab/mango-active.png");
    }
  }

  &.golden {
    background-image: url("~@/projects/app-card-zone/assets/images/tab/golden.png");
    &.checked {
      background-image: url("~@/projects/app-card-zone/assets/images/tab/golden-active.png");
    }
  }

  &.rare {
    background-image: url("~@/projects/app-card-zone/assets/images/tab/back.png");
    &.checked {
      background-image: url("~@/projects/app-card-zone/assets/images/tab/back-active.png");
    }
  }
  &.rare139 {
    background-image: url("~@/projects/app-card-zone/assets/images/tab/rare139.png");
    &.checked {
      background-image: url("~@/projects/app-card-zone/assets/images/tab/rare139-active.png");
    }
  }

  &.blue {
    background-image: url("~@/projects/app-card-zone/assets/images/tab/blue.png");
    &.checked {
      background-image: url("~@/projects/app-card-zone/assets/images/tab/blue-active.png");
    }
  }

  &.azure {
    background-image: url("~@/projects/app-card-zone/assets/images/tab/azure.png");
    &.checked {
      background-image: url("~@/projects/app-card-zone/assets/images/tab/azure-active.png");
    }
  }

  &.bisque {
    background-image: url("~@/projects/app-card-zone/assets/images/tab/bisque.png");
    &.checked {
      background-image: url("~@/projects/app-card-zone/assets/images/tab/bisque-active.png");
    }
  }

  &.accurate {
    background-image: url("~@/projects/app-card-zone/assets/images/tab/bisque.png");
    &.checked {
      background-image: url("~@/projects/app-card-zone/assets/images/tab/bisque-active.png");
    }
  }

  &.violet {
    background-image: url("~@/projects/app-card-zone/assets/images/tab/violet.png");
    &.checked {
      background-image: url("~@/projects/app-card-zone/assets/images/tab/violet-active.png");
    }
  }
}
</style>
