<template>
  <div class="cz-view" :style="`padding-bottom: ${aiBottom}rem`">
    <div class="cz-scroll">
      <wx-infomation v-if="isWxLogin" :misdnmask="misdnmask"></wx-infomation>
      <div class="cz-banners" :style="{ backgroundColor: subiconColor }">
        <img :src="banners.imgurl" @click="handleBanner" />
      </div>
      <div class="cz-content">
        <div class="cz-content-menu">
          <div
            v-for="(item, index) in menuList"
            :key="item.name"
            class="cz-content-menu-item"
            @click="handleItem(item, index)"
          >
            <img :src="item.url" />
            <p>{{ item.name }}</p>
          </div>
        </div>
        <div class="cz-content-box">
          <cz-search
            @search="handleSearch"
            @close="handleCloseSearch"
            :search-mode="searchMode"
          />
          <cz-tab
            v-if="showTab"
            :class="{ 'hide-tab': hideTab }"
            ref="czTab"
            :tabs="tabs"
            :default-nums="defaultNums"
            :default-lucky="defaultLucky"
            @stop="handleStop"
          />
        </div>
      </div>
    </div>
    <div class="cz-fixed">
      <div
        :class="['cz-fixed-btn', { active: checkNumber !== '' }]"
        @click="handleBydj"
      >
        {{ checkNumber === "" ? "选择靓号" : "立即领取" }}，包邮到家
      </div>
    </div>
    <cz-page-view ref="pageView"/>
  </div>
</template>

<script>
// import CryptoJS from 'crypto-js'
import wxInfomation from '../components/wx-information/index.vue'
import CzSearch from './components/cz-search/index.vue'
import CzTab from './components/cz-tab/index.vue'
import CzPageView from './components/cz-page-view.vue'
import { queryDefaultNum } from '../api'
import { Base64 } from '@/utils/base64'
import { operate_unifyH5 } from '@/api/common'
import { menuList } from '@/projects/app-card-zone/config/index'
import { encrypt, multiTrack, setUserId } from '../utils/utils'
import CHANNEL from '../../../../../../../bjapp-model/vue2/js/channel'
import { judgeLoginAndLogin } from '../../../../../../../bjapp-model/vue2/js/login'
import { cmcc_getUserStatus } from '../../../../../../../bjapp-model/vue2/js/login/jt-login'
import { mapState } from 'vuex'

export default {
  name: 'Home',
  components: {
    CzPageView,
    CzSearch,
    CzTab,
    wxInfomation
  },
  data() {
    return {
      banners: {},
      subiconColor: 'transparent',
      defaultNums: {},
      defaultLucky: {},
      showTab: false,
      hideTab: false,
      phone: '',
      menuList: menuList,
      tabs: [
        { title: '心动专属', q: '520_521_1314_sr_ssm_msm', type: '' },
        { title: '经典139', q: '139_139_139_139_139_139', type: 'rare139' },
        { title: '发达靓号', q: '168_188_668_988_998_688', type: 'violet' },
        { title: '富贵188', q: '188_188_188_188_188_188', type: 'rare' },
        { title: '精选连号', q: 'AA_AA_AA_AAA_AAA_AAA', type: 'blue' },
        { title: '吉祥如意', q: '2024_2024_24_24_24_24', type: 'bisque' },
        { title: '学霸号', q: '985_985_985_985_985_985', type: 'azure' },
        { title: '车牌靓号', q: '', type: 'plate' }
        // { title: '芒果卡专享', q: '304_0304_923_0923_224_0224_816_0816_615_0615_416_0416_1212_66_520_521_sr_sy', type: 'mango' },
      ],
      isWxLogin: false,
      misdnmask: '',
      sjEncMisdn: '',
      isJTAPP: CHANNEL.isJTAPP(),
      isIos: CHANNEL.isIOS(),
      searchMode: ''
    }
  },
  computed: {
    ...mapState({
      checkNumber: (state) => state.checkNumber,
      nt: (state) => state.nt,
      checkType: (state) => state.checkType,
      desc: (state) => state.desc,
      mangoList: (state) => state.mangoList,
      aiBottom: (state) => state.aiBottom
    })
  },
  mounted() {
    console.log('测试一下')
    console.log('CHANNEL.isJTAPP()', CHANNEL.isJTAPP())

    this.$loading.show()
    if (sessionStorage.getItem('userToken')) {
      this.init()
      if (CHANNEL.isWX()) {
        this.isWxLogin = true
      }
    } else if (CHANNEL.isJTAPP()) {
      console.log('CHANNEL.isJTAPP()')
      cmcc_getUserStatus().then(res => {
        if (res) {
          judgeLoginAndLogin(null, null, (token) => {
            if (token) {
              sessionStorage['set' + 'Item']('userToken', token)
              this.init()
            }
          })
        } else {
          this.init()
        }
      })
    } else {
      let token = ''
      this.wxAuthBase()
        .then(res => {
        // 免登录成功
          token = res.token
          // console.log(res, 'res.wxinfo')
          sessionStorage['set' + 'Item']('misdnmask', res.misdnmask)
          sessionStorage['set' + 'Item']('userToken', token)
          // console.log('token', res)
        }).finally(() => {
          if (!token) {
            // console.log('token', token, this.tabs[0])
            this.$set(this.tabs, 0, { title: '心动专属', q: '520_521_1314_520_521_1314', type: '' })
          } else {
            this.isWxLogin = true
          }
          this.init()
        })
    }
  },
  methods: {
    init() {
      this.queryDefaultNum()
      this.getBanner()
    },
    handleStop(target) {
      console.log(target)
      this.searchMode = target
    },
    // 选择靓号
    handleBydj() {
      multiTrack('P00000025404', '中国移动APP__集团号卡专区聚合页_选择靓号包邮到家')
      if (this.checkNumber && this.checkType === 'mango') {
        console.log('准备一下，即将跳转芒果卡页面')
        let checkCode = ''
        this.mangoList.forEach((item) => {
          if (item.number === this.checkNumber) {
            checkCode = item.checkCode
          }
        })
        this.$router.push({
          name: 'mangoCard',
          query: {
            checkNumber: this.checkNumber,
            checkCode
          }
        })
      // 修改跳转逻辑，nt是rare的走信号库
      // } else if (this.checkNumber && (this.desc === '稀有靓号' || this.desc === '富贵188')) {
      } else if (this.checkNumber && this.nt === 'rare') {
        const sourceId = CHANNEL.isJTAPP() ? 'JT' : 'WT'
        let url = `https://service.bj.10086.cn/m/num/num/order/jx/prepareSimcardSessionJxhNew.action?serial=${this.checkNumber}&sourceId=${sourceId}`
        if (this.sjEncMisdn) {
          url = `${url}&loginMobilePhoneNo=${encodeURIComponent(this.sjEncMisdn)}` // 添加商机跳转参数
        }
        location['hr' + 'ef'] = url
      } else if (this.checkNumber && this.checkType === 'accurate') {
        multiTrack('P00000025404', '中国移动APP__集团号卡专区聚合页_选择靓号包邮到家', { nextUrl: `https://service.bj.10086.cn/m/num/num/prettyNum/showWnfkFontPage.action?serial=${encodeURIComponent(encrypt(this.checkNumber))}&channelName=jtapp&sourceId=JT` })
        const base = new Base64()
        // app侧跳重定向页面
        let url = `${window.ACT_PATH}#/redirect?serial=${base.encode(this.checkNumber)}&from=accurate`
        if (this.phone) {
          url = `${url}&mainSerial=${this.phone}`
        }
        if (this.sjEncMisdn) {
          url = `${url}&loginMobilePhoneNo=${encodeURIComponent(this.sjEncMisdn)}` // 添加商机跳转参数
        }
        console.log('精准号码跳转到其他地址')
        window.$APPABILITY.newWebview(url)
      } else if (this.checkNumber) {
        multiTrack('P00000025404', '中国移动APP__集团号卡专区聚合页_选择靓号包邮到家', { nextUrl: `https://service.bj.10086.cn/m/num/num/prettyNum/showFontPage.action?serial=${encodeURIComponent(encrypt(this.checkNumber))}&channelName=jtapp&sourceId=JT` })

        // 检查用户授权
        // if (!this.$_bjapp_userInfoCheck()) {
        //   // console.log('用户授权校验失败')
        //   return
        // }
        const base = new Base64()
        // mainSerial  主号码
        // serial  选择号码
        // ${window.ACT_PATH}#
        // if () {}
        // http://10.9.11.131:7080/app-card-zone/

        // app侧跳重定向页面
        if (this.isJTAPP) {
          let url = `${window.ACT_PATH}#/redirect?serial=${base.encode(this.checkNumber)}`
          if (this.phone) {
            url = `${url}&mainSerial=${this.phone}`
          }
          if (this.sjEncMisdn) {
            url = `${url}&loginMobilePhoneNo=${encodeURIComponent(this.sjEncMisdn)}` // 添加商机跳转参数
          }
          window.$APPABILITY.newWebview(url)
        } else {
        // 微信侧直接跳转
          const mainSerial = this.phone
          const serial = this.checkNumber
          const num = encodeURIComponent(encrypt(serial))
          const url = new URL(`https://service.bj.10086.cn/m/num/num/prettyNum/showFontPage.action?serial=${num}&channelName=jtapp&sourceId=JT`)
          if (this.sjEncMisdn) {
            url.searchParams.set('loginMobilePhoneNo', this.sjEncMisdn) // 添加商机跳转参数
          }
          if (mainSerial) {
            const phone = encrypt(base.decode(mainSerial))
            url.searchParams.set('mainSerial', phone) // 添加mainSerial参数
          }
          window['loca' + 'tion'].href = url.toString()
        }
      }
    },
    // 获取运营位数据
    getBanner() {
      const userToken = sessionStorage.getItem('userToken')
      operate_unifyH5({
        token: userToken,
        cateKeyword: 'numbercard_banner_JT', // numbercard_banner
        behaviorCode: '10831',
        explain: '底部banner',
        os: 'ios',
        ver: 'bjservice_ios_8.2.0'
      }).then((res) => {
        const [item] = res.region
        if (item) {
          const [block] = item.block
          this.banners = block
          this.subiconColor = item.subicon_color
        }
      })
    },
    // 点击banner
    handleBanner() {
      const { url } = this.banners
      if (!url) {
        return
      }
      multiTrack('P00000025420', '中国移动APP_集团号卡专区聚合页_顶部轮播图', { nextUrl: url })

      if (this.isJTAPP) {
        window.$APPABILITY.newWebview(url)
      } else {
        window['loca' + 'tion'].href = url
      }
    },
    // 点击icon
    handleItem({ pageId, appHref, wxHref }, index) {
      const i = 25419 - index
      multiTrack(`P000000${i}`, '中国移动APP_集团号卡专区聚合页_ICON' + (index + 1), { nextUrl: this.isJTAPP ? appHref : wxHref })

      if (this.isJTAPP) {
        if (pageId === 'vedio-service') {
          // 跳转视频客服
          window.$APPABILITY.openService({ sceneEntry: '323' })
        } else {
          window.$APPABILITY.newWebview(appHref || '')
        }
        //  else if(pageId === 'charge') {
        //   window.$APPABILITY.goNativePage(this.isIos ? 'CN00052' : 'CN00123')
        // }
      } else {
        location.href = wxHref
      }
    },
    // 点击搜索按钮，隐藏tab栏组件，展示搜索组件
    handleSearch() {
      this.hideTab = true
      this.$store.commit('CLEAN_USERCHECKDATA') // 重置仓库数据
      this.$refs.czTab && this.$refs.czTab.clearAutoPlay()
    },
    // 点击关闭按钮，隐藏搜索组件，展示tab栏组件
    handleCloseSearch(target) {
      this.$store.commit('CLEAN_USERCHECKDATA') // 重置仓库数据
      this.hideTab = false
      if (target) {
        this.searchMode = target
      } else {
        this.$refs.czTab && this.$refs.czTab.autoPlay()
        this.searchMode = ''
      }
    },
    queryDefaultNum() {
      queryDefaultNum({ c: 1, _: new Date().getTime() })
        .then((res) => {
          if (res.mobile) {
            setUserId(res.mobile, res.jtEncMisdn)
          }
          if (res.maskMisdn) {
            this.misdnmask = res.maskMisdn
          }
          if (res.sjEncMisdn) {
            this.sjEncMisdn = res.sjEncMisdn
          }
          if (res.result === 0) {
            const defaultNums = res.data.find(item => item.key === '')
            const eleven = res.data.find(item => item.key === '11') || { nums: [] }
            // const [six, eight, defaultNums] = res.data
            this.defaultLucky = {
              key: /11/,
              nums: [].concat(eleven.nums),
              tag: '吉祥靓号'
            }
            if (Number(res.jzFlag)) {
              this.$set(this.tabs, 1, { title: '0元10GB', q: 'ssm_ssm_ssm_msm_msm_msm', type: 'accurate' })
            }
            this.defaultNums = defaultNums
            this.showTab = true
            const base = new Base64()
            this.phone = base.encode(String(res.encrypt))
            console.log(res.encrypt, this.phone)
            this.$refs.pageView.initView()
            this.$loading.hide()
          } else {
            this.$toast('活动太火爆了，请稍后再试吧~')
          }
        })
        .catch(() => {
          this.$loading.hide()
          this.$toast('活动太火爆了，请稍后再试吧~')
        }).finally(() => {
          multiTrack('P00000025421', '中国移动APP_集团号卡专区聚合页_首页')
        })
    }
  }
}
</script>

<style lang="scss" scoped>
.cz {
  &-view {
    position: relative;
    height: 100vh;
    // background: white url('~@/projects/app-card-zone/assets/images/banner.png') no-repeat;
    // background-size: 100% auto;
    // padding-bottom: 162px;
    display: flex;
    flex-direction: column;
    .hide-tab {
      opacity: 0;
    }
  }

  &-fixed {
    // position: fixed;
    // bottom: 0;
    // left: 0;
    // right: 0;
    width: 100%;
    height: 162px;
    background: #ffffff;
    box-shadow: 0px -4px 8px 0px rgba(0, 0, 0, 0.04);
    &-btn {
      width: 690px;
      height: 90px;
      line-height: 90px;
      text-align: center;
      background: url("~@/projects/app-card-zone/assets/images/anniu.png")
        no-repeat;
      background-size: 100%;
      color: #ffffff;
      border-radius: 46px;
      margin: 30px auto 0;
      font-size: 32px;
      font-family: PingFangSC, PingFangSC-Medium;
      font-weight: 500;
      opacity: 0.3;
      &.active {
        opacity: 1;
        animation: free_download 0.5s linear alternate infinite;
      }
    }
  }
  &-scroll {
    position: relative;
    flex: 1;
    overflow: auto;
  }
  &-banners {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    min-height: 312px;
    img {
      width: 100%;
      // min-height: 156px;
    }
  }
  &-content {
    // flex: 1;
    // margin-top: 274px;
    position: relative;
    top: 274px;
    margin-bottom: 30px;
    border-radius: 38px 38px 0 0;
    background-color: white;
    overflow: hidden;
    &-menu {
      display: flex;
      justify-content: space-between;
      margin: 40px 44px;
      &-item {
        width: 116px;
        img {
          width: 116px;
          height: 116px;
          margin-bottom: 4px;
        }
        p {
          font-size: 28px;
        }
      }
    }
    &-box {
      position: relative;
      padding-top: 106px;
      min-height: 760px;
      margin: 0 30px 0;
      border-radius: 22px;
      background: white url("~@/projects/app-card-zone/assets/images/box.png")
        no-repeat;
      background-size: 100% auto;
      overflow: hidden;
    }
  }
}
</style>
