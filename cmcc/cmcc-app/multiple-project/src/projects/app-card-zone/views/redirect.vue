<!--
 * @Author: zhan<PERSON><PERSON> <EMAIL>
 * @Date: 2023-01-03 14:18:09
 * @LastEditors: luobingxin <EMAIL>
 * @LastEditTime: 2024-05-15 17:50:34
 * @FilePath: \my-series\cmcc\cmcc-app\multiple-project\src\projects\app-card-zone\views\redirect.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <div></div>
</template>
<script>
import { encrypt } from '../utils/utils'
import { Base64 } from '@/utils/base64'

export default {
  created() {
    // mainSerial  主号码
    // serial  选择号码
    const base = new Base64()
    const { mainSerial, serial, from = '', loginMobilePhoneNo } = this.$route.query
    const num = encodeURIComponent(encrypt(base.decode(serial)))
    let url = `https://service.bj.10086.cn/m/num/num/prettyNum/showFontPage.action?serial=${num}&channelName=jtapp&sourceId=JT`
    if (from) {
      console.log('跳转到精准号码地址')
      url = `https://service.bj.10086.cn/m/num/num/prettyNum/showWnfkFontPage.action?serial=${num}&channelName=jtapp&sourceId=JT`
    }
    if (mainSerial) {
      const phone = encodeURIComponent(encrypt(base.decode(mainSerial)))
      url = `${url}&mainSerial=${phone}`
    }
    if (loginMobilePhoneNo) {
      url = `${url}&loginMobilePhoneNo=${encodeURIComponent(loginMobilePhoneNo)}`
    }
    window.location.replace(url)
  },
  methods: {
  }
}
</script>
