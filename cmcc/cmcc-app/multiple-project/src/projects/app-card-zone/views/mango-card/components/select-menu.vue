<template>
  <div class="warp">
    <section class="phone">
      您选择的号码
      <div>{{ checkNumber }}</div>
    </section>
    <section class="menu">
      <div class="select-option">
        <van-swipe :loop="false" :width="width" :show-indicators="false">
          <van-swipe-item>
            <div :class="['select-option__one', menu === 0 ? 'selected-one' : '']" @click="checkOneMenu"></div>
          </van-swipe-item>
          <van-swipe-item>
            <div :class="['select-option__two', menu === 1 ? 'selected-two' : '']" @click="checkTwoMenu"></div>
          </van-swipe-item>
        </van-swipe>
        <div class="indicator">
          <span :class="[menu === 0 ? 'active' : 'default']"></span>
          <span :class="[menu === 1 ? 'active' : 'default']"></span>
        </div>
      </div>
      <div class="menu-rule" @click="openMenuRule"></div>
    </section>
    <van-popup v-model="show" position="bottom" :style="{ height: '80%',background: '#ffeed3', paddingBottom: aiBottom + 'rem' }" round>
      <div class="rule">
        <div class="close" @click="closeMenuRule"></div>
      </div>
    </van-popup>
  </div>
</template>

<script>
import { multiTrack } from '../../../utils/utils'
export default {
  props: {
    menu: {
      type: Number,
      default: 0
    },
    aiBottom: {
      type: Number,
      default: 0
    }
  },
  data() {
    return {
      checkNumber: this.$route.query.checkNumber, // 用户选择的号码
      show: false, // 展示芒果卡规则
      width: Number(document.body.clientWidth) * 0.8 // 轮播图图片的宽度为可视化宽度的80%
    }
  },
  mounted() {
    multiTrack('P00000063358', '中国移动APP_动感地带芒果卡_主页_59元')
  },
  methods: {
    // 选择套餐一
    checkOneMenu() {
      this.$emit('update:menu', 0)
      multiTrack('P00000063358', '中国移动APP_动感地带芒果卡_主页_59元')
    },
    // 选择套餐二
    checkTwoMenu() {
      multiTrack('P00000063359', '中国移动APP_动感地带芒果卡_主页_79元')
      this.$emit('update:menu', 1)
    },
    // 选择的套餐
    // onChange(index) {
    //   if (index) {
    //     multiTrack('P00000063359', '中国移动APP_动感地带芒果卡_主页_79元')
    //   } else {
    //     multiTrack('P00000063358', '中国移动APP_动感地带芒果卡_主页_59元')
    //   }
    //   this.$emit('update:menu', index)
    // },
    // 打开规则说明
    openMenuRule() {
      multiTrack('P00000063360', '中国移动APP_动感地带芒果卡_主页_查看套餐资费及规则说明')
      this.show = true
    },
    // 关闭规则说明
    closeMenuRule() {
      this.show = false
    }
  }
}
</script>

<style lang="scss" scoped>
.warp {
  width: 100%;
  height: 870px;
  background: linear-gradient(180deg,#feaa40 0%, #feeed5 54%);
  overflow: hidden;
  .phone {
    width: 700px;
    height: 200px;
    background: #ffffff;
    border-radius: 28px;
    margin: 24px auto;
    font-size: 32px;
    color: #000000;
    padding: 40px 40px;
    text-align: left;
    div {
      padding-top: 30px;
      font-size: 60px;
    }
  }
  .menu {
    position: relative;
    width: 700px;
    height: 600px;
    background: url('../image/menu-bg.png') no-repeat center center;
    background-size: 100% 100%;
    margin: 0 auto;
    overflow: hidden;
    .select-option {
      height: 370px;
      min-width: 700px;
      margin-top: 100px;
      &__one {
        width: 600px;
        height: 370px;
        background: url('../image/option-one.png') no-repeat center center;
        background-size: 96% 100% !important;
      }
      .selected-one {
        background: url('../image/option-one-selected.png') no-repeat center center;
        background-size: 96% 100% !important;
      }
      &__two {
        width: 600px;
        height: 370px;
        background: url('../image/option-two.png') no-repeat center center;
        background-size: 96% 100% !important;
      }
      .selected-two {
        background: url('../image/option-two-selected.png') no-repeat center center;
        background-size: 96% 100% !important;
      }
    }
    .indicator {
      width: 100px;
      height: 12px;
      position: absolute;
      bottom: 120px;
      left: 50%;
      transform: translateX(-50%);
      z-index: 1000;
      .active {
        display: inline-block;
        width: 40px;
        height: 12px;
        background: #ffffff;
        border-radius: 6px;
        margin-left: 6px;
      }
      .default {
        width: 12px;
        height: 12px;
        background: #ffffff;
        border-radius: 6px;
        opacity: 0.6;
        display: inline-block;
        margin-left: 6px;
      }
    }
    .menu-rule {
      width: 392px;
      height: 42px;
      background: url('../image/menu-rule.png') no-repeat center center;
      background-size: 100% 100%;
      margin: 70px auto 0;
    }
  }
  .rule {
    position: relative;
    margin-top: 20px;
    width: 100vw;
    height: 6247px;
    background: url('../image/rule.png') no-repeat center center;
    background-size: 100% 100%;
    .close {
      position: absolute;
      top: 20px;
      right: 20px;
      width: 52px;
      height: 52px;
      background: url('../image/close.png') no-repeat center center;
      background-size: 100% 100%;
    }
  }
}
</style>
