<template>
  <div class="wrap" :style="`padding-bottom: ${aiBottom}rem`">
    <p class="title">你的动感地带芒果卡订单已受理</p>
    <p class="text-one">需实名制审核</p>
    <p class="text-two">订单校验审核</p>
    <p class="text-three">短信通知受理结果</p>
    <p class="text-four">成功后，专人或快递人员上门帮你激活号卡，需本人身份证</p>
  </div>
</template>
<script>
export default {
  props: {
    aiBottom: {
      type: Number,
      default: 0
    }
  }
}
</script>
<style lang="scss" scoped>
.wrap {
  position: fixed;
  top: 0;
  left: 0;
  bottom: 0;
  right: 0;
  width: 100vw;
  height: 100vh;
  background: #f5f5f5;
  font-size: 32px;
  padding-top: 80px;
  text-align: left;
  line-height: 44px;
  z-index: 1111;
  .title {
    position: relative;
    margin-left: 120px;
    font-size: 40px;
    font-weight: 600;
    line-height: 56px;
    color: #111111;
  }
  .title::before {
    content: '';
    position: absolute;
    top: -10px;
    left: -92px;
    width: 80px;
    height: 80px;
    background: url('../image/icon-check.png') no-repeat center center;
    background-size: 100% 100%;
  }
  .title::after {
    content: '';
    position: absolute;
    top: 85px;
    left: -52px;
    width: 2px;
    height: 64px;
    background-color: #1e91fe;
  }
  .text-one {
    margin-left: 120px;
  }
  .text-two {
    margin-left: 120px;
    margin-top: 50px;
    position: relative;
  }
  .text-two::before{
    content: '';
    position: absolute;
    top: 15px;
    left: -62px;
    width: 22px;
    height: 22px;
    background: #e2e2e2;
    border-radius: 50%;
  }
  .text-two::after{
    content: '';
    position: absolute;
    top: 56px;
    left: -52px;
    width: 2px;
    height: 42px;
    background-color: #e2e2e2;
  }
  .text-three {
    position: relative;
    margin-left: 120px;
    margin-top: 54px;
  }
  .text-three::before{
    content: '';
    position: absolute;
    top: 10px;
    left: -62px;
    width: 22px;
    height: 22px;
    background: #e2e2e2;
    border-radius: 50%;
  }
  .text-three::after{
    content: '';
    position: absolute;
    top: 56px;
    left: -52px;
    width: 2px;
    height: 42px;
    background-color: #e2e2e2;
  }
  .text-four {
    position: relative;
    width: 540px;
    margin-left: 120px;
    color: #ffa861;
    margin-top: 56px;
  }
  .text-four::before{
    content: '';
    position: absolute;
    top: 20px;
    left: -62px;
    width: 22px;
    height: 22px;
    background: #e2e2e2;
    border-radius: 50%;
  }
}
</style>
