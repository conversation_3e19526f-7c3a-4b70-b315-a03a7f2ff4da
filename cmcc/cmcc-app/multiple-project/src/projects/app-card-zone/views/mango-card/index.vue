<template>
  <div class="mango" :style="`padding-bottom: ${dbBottom + aiBottom}rem`">
    <select-menu :menu.sync="menu" :aiBottom="aiBottom" />
    <message-form :menu="menu" :aiBottom="aiBottom" @submit="submit" />
    <submit-success v-if="isSubmitSuccess" :aiBottom="aiBottom"/>
    <fail-popup :showfailPopup.sync="showfailPopup" :aiBottom="aiBottom"/>
  </div>
</template>

<script>
import selectMenu from './components/select-menu.vue'
import messageForm from './components/message-form.vue'
import submitSuccess from './components/success.vue'
import failPopup from './components/fail-popup'
import { orderMangoCard } from '../../api/index'
import { multiTrack } from '../../utils/utils'
import { mapState } from 'vuex'
export default {
  components: {
    selectMenu,
    messageForm,
    submitSuccess,
    failPopup
  },
  props: {},
  data() {
    return {
      number: this.$route.query.checkNumber || '',
      checkCode: this.$route.query.checkCode || '',
      isSubmitSuccess: false, // 是否提交成功
      showfailPopup: false, // 是否展示错误弹窗
      menu: -1,
      dbBottom: 40 / 75
    }
  },
  computed: {
    ...mapState({
      aiBottom: (state) => state.aiBottom
    })
  },
  mounted() {
    multiTrack('P00000063357', '中国移动APP_动感地带芒果卡_主页')
  },
  methods: {
    submit(messageObj) {
      this.$loading.show()
      if (this.menu) {
        messageObj.productCode = '500000050617'
      } else {
        messageObj.productCode = '500000050616'
      }
      messageObj.orderMobile = this.number
      messageObj.numberCheckCode = this.checkCode
      messageObj.name = messageObj.certificateName
      console.log('查看提交内容', messageObj)
      orderMangoCard(messageObj).then((res) => {
        if (Number(res.code) === 0) { // 提交成功
          this.isSubmitSuccess = true
          multiTrack('P00000063363', '中国移动APP_动感地带芒果卡_下单成功页')
        } else if (String(res.code) === '0110492062' || String(res.code) === '0800000317') {
          this.$toast('别急，您尚未年满16周岁，还不能申请电话卡哦！')
        } else if (String(res.code) === '0900000101') {
          this.$toast('号码已被占用，请更换号码再次尝试')
        } else if (String(res.code) === '0800000202') {
          this.$toast('手机号码不正确')
        } else if (String(res.code) === '0110493006') {
          this.$toast('非常抱歉,根据国家实名制相关规定，您名下的电话卡数量已达上限，暂时无法申请新号哦！')
        } else if (String(res.code) === '0800000201' || String(res.code) === '0110493A11') {
          this.$toast('非常抱歉,身份证号码不正确')
        } else if (String(res.code) === '0800000310') {
          this.$toast('非常抱歉,每位用户每月最多申请2次，喜欢也不能贪心哦~~')
        } else if (String(res.code) === '200004000') {
          this.$toast('非常抱歉,信息与身份证不匹配~~')
        } else {
          this.showfailPopup = true
          multiTrack('P00000063364', '中国移动APP_动感地带芒果卡_下单失败页')
        }
      })
        .finally(() => {
          this.$loading.hide()
        })
    }
  }
}
</script>

<style lang="scss" scoped>
.mango {
  width: 100vw;
  min-height: 100vh;
  background: #f5f5f5;
  padding-bottom: 40px;
}
</style>
