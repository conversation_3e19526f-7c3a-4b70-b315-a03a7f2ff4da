<template>
  <van-form ref="form" :show-error="false" :show-error-message="false">
    <section class="identify">
      <div class="identify__title">请填写身份信息</div>
      <div class="identify__text">请正确填写便于国家实名制审核通过</div>
      <van-field
        v-model.trim="messageObj.certificateName"
        label="姓名"
        placeholder="填写正确姓名"
        maxlength="10"
        size="large"
        :rules="checkoutRuleObj.certificateNameRule"
      />
      <van-field
        v-model="messageObj.certificateNumber"
        label="身份证号"
        message="填写正确18位身份证号"
        placeholder="填写正确18位身份证号"
        maxlength="18"
        size="large"
        :rules="checkoutRuleObj.certificateNumberRule"
      />
    </section>
    <section class="address">
      <div class="address__title">请填写配送信息</div>
      <div class="address__text">温馨提示：北京市外地址无法配送</div>
      <van-field
        v-model="messageObj.mobilePhone"
        type="digit"
        label="联系电话"
        placeholder="便于快递员联系送卡"
        size="large"
        maxlength="11"
        :rules="checkoutRuleObj.mobilePhoneRule"
      />
      <van-field
        v-model="messageObj.sendCityName"
        is-link
        readonly
        label="地市"
        placeholder="请选择地市"
        size="large"
        :rules="checkoutRuleObj.sendCityCodeRule"
        @click="checkCity"
      />
      <van-popup v-model="showCity" round position="bottom">
        <van-cascader
          title="请选择地市"
          :options="bjAddress"
          :field-names="cityFieldNames"
          @close="showCity = false"
          active-color="#1989fa"
          @finish="onFinishCity"
        />
      </van-popup>
      <van-field
        v-model="messageObj.sendDistrictName"
        is-link
        readonly
        label="区县"
        placeholder="请选择区县"
        size="large"
        :rules="checkoutRuleObj.sendDistrictCodeRule"
        @click="checkCounty"
      />
      <van-popup v-model="showCounty" round position="bottom" :style="{ paddingBottom: (aiBottom + qxBottom) + 'rem' }">
        <van-cascader
          title="请选择区县"
          :options="bjCountyAddress"
          :field-names="countyFieldNames"
          @close="showCounty = false"
          active-color="#1989fa"
          @finish="onFinishCounty"
        />
      </van-popup>
      <van-field
        v-model.trim="messageObj.address"
        label="送卡地址"
        placeholder="请填写详细地址"
        type="textarea"
        rows="1"
        maxlength="50"
        autosize
        :rules="checkoutRuleObj.addressRule"
      />
    </section>
    <section class="agreement">
      <div
        :class="['agreement-check', checked ? 'agreement-yes' : '']"
        @click="checkAgreement"
      ></div>
      <div class="text">
        请您查看并同意<span @click="lookDutyNotification"
          >《中国移动用户入网协议》</span
        ><br><span @click="lookDutyNotification">《实名制信息安全责任告知书》</span>
      </div>
    </section>
    <!-- 中国移动用户入网协议 -->
    <van-popup v-model="showNotification" round position="bottom" :style="{ height: '80%',background: '#ffffff', paddingBottom: aiBottom + 'rem' }" closeable>
      <div class="user-notification" v-html="userDutyNotification"></div>
    </van-popup>
    <div class="submit" @click="submit">勾选协议，立即提交</div>
  </van-form>
</template>

<script>
import { checkoutRuleObj, cityFieldNames, countyFieldNames, userDutyNotification } from '../config/index'
import { getExpress } from '../../../api/index'
import { multiTrack } from '../../../utils/utils'
import CHANNEL from '@/utils/channel'
export default {
  props: {
    menu: {
      type: Number,
      default: -1
    },
    aiBottom: {
      type: Number,
      default: 0
    }
  },
  data() {
    return {
      messageObj: {
        certificateName: '', // 证件姓名
        certificateNumber: '', // 证件号码
        goodsProvinceCode: '100', // 省份编码（产品所在省份: 北京）
        goodsCityCode: '100', // 城市编码（产品所在编码：北京）
        sendProvinceCode: '100', // 省份编码：北京
        sendCityCode: '', // 城市编码
        sendCityName: '', // 城市名称
        sendDistrictCode: '', // 区县编码
        sendDistrictName: '', // 区县名称
        address: '', // 详细收件地址
        mobilePhone: '' // 手机号（快递手机号）
      },
      bjAddress: null, // 北京地址信息
      bjCountyAddress: null, // 北京区县地址信息
      showCity: false, // 展示选择地市弹窗
      showCounty: false, // 展示选择区县的弹窗
      checked: false, // 是否同意协议
      cityFieldNames,
      countyFieldNames,
      isApp: CHANNEL.isAPP(),
      showNotification: false, // 展示用户协议书
      userDutyNotification, // 用户协议书
      checkoutRuleObj, // input表单输入规则校验
      qxBottom: 40 / 75
    }
  },
  mounted() {
    this.$loading.show()
    this.queryExpress()
  },
  methods: {
    // 勾选和取消勾选协议
    checkAgreement() {
      this.checked = !this.checked
    },
    // 实名制信息安全责任告知书
    lookDutyNotification() {
      multiTrack('P00000063361', '中国移动APP_动感地带芒果卡_主页_查看并同意协议')
      this.showNotification = true
    },
    // 点击地市输入框
    checkCity() {
      this.showCity = true
    },
    // 选择对应的地市
    onFinishCity({ selectedOptions }) {
      this.showCity = false
      this.messageObj.sendCityName = selectedOptions[0].cityName
      this.messageObj.sendCityCode = selectedOptions[0].cityId
    },
    // 点击区县输入框
    checkCounty() {
      if (this.messageObj.sendCityCode === '') {
        this.$toast('请先选择地市')
        return
      }
      this.showCounty = true
    },
    // 选择对应的区县
    onFinishCounty({ selectedOptions }) {
      this.showCounty = false
      this.messageObj.sendDistrictName = selectedOptions[0].districtName
      this.messageObj.sendDistrictCode = selectedOptions[0].districtId
    },
    // 提交表单
    submit() {
      multiTrack('P00000063362', '中国移动APP_动感地带芒果卡_主页_提交')
      if (this.menu === -1) {
        this.$toast('请选择芒果卡套餐')
        return
      }
      this.$refs.form
        .validate()
        .then(() => {
          if (!this.checked) {
            this.$toast('请勾选协议~')
            return false
          }
          this.$emit('submit', this.messageObj)
        })
        .catch((err) => {
          this.$toast(`${err[0].message}`)
        })
    },
    // 获取北京各地地址信息
    queryExpress() {
      getExpress().then((res) => {
        res.data.forEach((item) => {
          if (item.provinceName === '北京') {
            this.bjAddress = item.citys
            this.bjCountyAddress = item.citys[0].distrcts
            return false
          }
        })
      })
        .finally(() => {
          this.$loading.hide()
        })
    }
  }
}
</script>

<style lang="scss" scoped>
.identify {
  width: 100vw;
  height: 400px;
  background-color: #ffffff;
  margin-top: 24px;
  overflow: hidden;
  &__title {
    margin: 40px 26px 20px;
    text-align: left;
    font-size: 40px;
    font-weight: bold;
    color: #000000;
  }
  &__text {
    margin-left: 26px;
    margin-bottom: 60px;
    text-align: left;
    font-size: 30px;
    color: #666666;
  }
}
.address {
  position: relative;
  width: 100vw;
  min-height: 400px;
  background-color: #ffffff;
  margin-top: 24px;
  overflow: hidden;
  &__title {
    margin: 40px 26px 20px;
    text-align: left;
    font-size: 40px;
    font-weight: bold;
    color: #000000;
  }
  &__text {
    margin-left: 26px;
    margin-bottom: 60px;
    text-align: left;
    font-size: 30px;
    color: #ffa458;
  }
}
.agreement {
  position: relative;
  width: 600px;
  margin: 40px 50px 20px 100px;
  &-check {
    position: absolute;
    top: 0px;
    left: -34px;
    width: 40px;
    height: 40px;
    background: url('../image/icon-not.png') no-repeat center center;
    background-size: 100% 100%;
  }
  &-yes {
    background: url('../image/icon-check.png') no-repeat center center;
    background-size: 100% 100%;
  }
  .text {
    font-size: 30px;
    color: #bdbdbd;
    text-align: center;
    line-height: 42px;
    span {
      color: #32abec;
    }
  }
}
.user-notification {
  padding: 0 12px 30px;
  font-size: 24px;
  color: #333333;
  text-align: left;
  line-height: 30px;
  ::v-deep .big-title {
    text-align: center;
    padding: 30px 0;
    font-size: 30px;
    font-weight: bold;
  }
  ::v-deep .title {
    font-size: 28px;
    font-weight: bold;
    padding: 10px 0;
  }
  ::v-deep .sub-title {
    font-size: 26px;
    font-weight: bold;
    line-height: 44px;
  }
  ::v-deep .bold {
    font-weight: bold;
  }
  ::v-deep div {
    display: flex;
    justify-content: start;
    margin: 20px 0;
  }
}
.submit {
  margin: 0 auto;
  width: 690px;
  height: 100px;
  background: linear-gradient(180deg, #1ecdfe, #1595ff);
  border-radius: 54px;
  font-size: 36px;
  font-weight: 600;
  color: #ffffff;
  text-align: center;
  line-height: 100px;
}
</style>
