<template>
  <div v-if="showfailPopup" class="wrap">
    <div class="main" :style="`top: calc(50% - ${aiBottom}rem)`">
      <div class="title">温馨提示</div>
      <div class="text">下单失败，请稍后重新尝试<br />如有问题请联系<span>10086</span></div>
      <div class="line"></div>
      <div class="back" @click="back">我知道了</div>
    </div>
  </div>
</template>

<script>
import { multiTrack } from '../../../utils/utils'
export default {
  props: {
    showfailPopup: {
      type: Boolean,
      default: false
    },
    aiBottom: {
      type: Number,
      default: 0
    }
  },
  methods: {
    back() {
      multiTrack('P00000063365', '中国移动APP_动感地带芒果卡_下单失败页_关闭')
      this.$emit('update:showfailPopup', false)
    }
  }
}
</script>

<style lang="scss" scoped>
.wrap {
  position: fixed;
  width: 100vw;
  height: 100vh;
  top: 0;
  left: 0;
  bottom: 0;
  right: 0;
  background-color: rgba(0, 0, 0 , .7);
  z-index: 11111;
  .main {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 580px;
    height: 340px;
    background: #ffffff;
    border-radius: 40px;
    padding-top: 44px;
    .title {
      font-size: 32px;
      font-weight: bold;
      color: #000000;
    }
    .text {
      margin-top: 30px;
      font-size: 28px;
      color: #666666;
      line-height: 42px;
      span {
        color: #19ABFE;
      }
    }
    .line {
      margin-top: 40px;
      width: 100%;
      height: 2px;
      background-color: #f2f2f2;
    }
    .back {
      margin-top: 34px;
      width: 100%;
      font-size: 28px;
      color: #19abfe;
    }
  }
}
</style>
