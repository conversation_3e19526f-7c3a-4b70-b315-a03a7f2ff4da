import Vue from 'vue'
import Router from 'vue-router'

Vue.use(Router)

export default new Router({
  mode: 'hash',
  routes: [{
    path: '/',
    redirect: '/index'
  },
  {
    path: '/index',
    name: 'index',
    component: (resolve) => require(['../views/index.vue'], resolve)
  },
  {
    path: '/mango-card',
    name: 'mangoCard',
    component: (resolve) => require(['../views/mango-card/index.vue'], resolve)
  },
  {
    path: '/redirect',
    name: 'redirect',
    component: (resolve) => require(['../views/redirect.vue'], resolve)
  }
  ]
})
