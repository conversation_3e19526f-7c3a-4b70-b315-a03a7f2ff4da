import CryptoJS from 'crypto-js'
import Webtrends1 from '@/utils/webtrends'

export function encrypt(params) {
  const key = CryptoJS.enc.Utf8.parse('asiainfo_ebiz_num_prettyNum')
  const encrypted = CryptoJS.TripleDES.encrypt(params, key, {
    mode: CryptoJS.mode.ECB,
    padding: CryptoJS.pad.Pkcs7 // 填充方式
  })
  const encryptData = encrypted.toString()
  // // console.log('加密后的密文：', encryptData)
  return encryptData
}

export function decrypt(params) {
  const key = CryptoJS.enc.Utf8.parse('asiainfo_ebiz_num_prettyNum')
  const decrypted = CryptoJS.TripleDES.decrypt(
    {
      ciphertext: CryptoJS.enc.Base64.parse(params)
    },
    key,
    {
      mode: CryptoJS.mode.ECB
      // padding: CryptoJS.pad.Pkcs7 这地方不用配置，我因为配置结果解密java返回的信息老是报错，看别人的代码加了，解析自已加密的没问题
    }
  )
  const decryptedData = decrypted.toString(CryptoJS.enc.Utf8)
  // // console.log('解密后的信息：', decryptedData)
  return decryptedData
}
// 玩法说明
export function multiTrack(event, envName, { type, nextUrl, markId } = {}) {
  Webtrends1.multiTrack(event, envName, { type, nextUrl, markId })
}
// 设置手机号码
export function setUserId(bjMobile, jtMobile) {
  Webtrends1.setUserId(bjMobile, jtMobile)
}
