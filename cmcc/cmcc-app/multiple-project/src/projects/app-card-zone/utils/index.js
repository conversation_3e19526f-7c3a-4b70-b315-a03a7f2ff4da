import {
  getSetShareParams
} from '@/api/common.js'

export function dateFormat(value, format, timestr) {
  if (typeof value === 'string' && value.indexOf('-') > -1) {
    value = value.replace(/-/g, '/')
  }
  var t = new Date(value)
  var o = {
    'M+': t.getMonth() + 1, // month
    'd+': t.getDate(), // day
    'h+': t.getHours(), // hour
    'm+': t.getMinutes(), // minute
    's+': t.getSeconds(), // second
    'q+': Math.floor((t.getMonth() + 3) / 3), // quarter
    S: t.getMilliseconds() // millisecond
  }
  if (/(y+)/.test(format)) {
    format = format.replace(RegExp.$1, (t.getFullYear() + '').substr(4 - RegExp.$1.length))
  }
  for (var k in o) {
    if (new RegExp('(' + k + ')').test(format)) {
      format = format.replace(
        RegExp.$1,
        RegExp.$1.length === 1
          ? o[k]
          : o[k].toString().length === 1
            ? ('0' + o[k]).substr(('' + o[k]).length)
            : o[k].toString().substr(0)
      )
    }
  }
  return format
}

export function getSetShareParamsWX() {
  const wx = window.wx
  return new Promise((resolve, reject) => {
    getSetShareParams()
      .then((data) => {
        wx.config({
          debug: false,
          appId: data.appId,
          timestamp: data.timestamp,
          nonceStr: data.noncestr,
          signature: data.signature,
          jsApiList: ['updateAppMessageShareData', 'updateTimelineShareData'],
          openTagList: ['wx-open-launch-weapp', 'wx-open-launch-app', 'wx-open-subscribe']
        })
        resolve()
      })
      .catch(() => {})
  })
}
