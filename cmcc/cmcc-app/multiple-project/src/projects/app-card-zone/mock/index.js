// import Mock from 'mockjs'

// Mock.mock(RegExp('/app/queryAppDefaultNum'), () => {
//   return {
//     data: [{
//       key: '66',
//       nums: ['13819665523'],
//       tag: '吉祥靓号'
//     },
//     {
//       key: '88',
//       nums: ['13998889615', '13898889615', '13988889616'],
//       tag: '吉祥靓号'
//     },
//     {
//       key: '',
//       nums: ['13819195523', '13998889615', '13898889615', '13988889616', '13819665523', '13811359617'],
//       tag: '移动精选'
//     }
//     ],
//     result: 0
//   }
// })

// Mock.mock(RegExp('/app/queryAppCustomNumber'), (params) => {
//   // console.log(params, '======params') // 520_521_1314_ssm_sr_AAA_msm
//   //  ssm == 相同尾号  sr == 生日  AAA == 三连 msm == 相同号段
//   let list = []
//   const query = JSON.parse(params.body)
//   // console.log(String(query.q).split('_'))
//   if (query.q) {
//     list = String(query.q)
//       .split('_')
//       .map((q) => {
//         if (q === '520') {
//           return {
//             key: '520',
//             nums: ['13652001422', '13652001122', '13652001122'],
//             tag: '心动号'
//           }
//         } else if (q === '521') {
//           return {
//             key: '521',
//             nums: ['13652141422', '13652141122', '13613521122'],
//             tag: '心动号'
//           }
//         } else if (q === '1314') {
//           return {
//             key: '1314',
//             nums: ['13613141422', '13613141122', '13613141122'],
//             tag: '心动号'
//           }
//         } else if (q === 'sr') {
//           return {
//             key: '1217',
//             nums: [],
//             tag: '生日号'
//           }
//         } else if (q === 'ssm') {
//           return {
//             key: '9615',
//             nums: ['13998889615', '13898889615'],
//             tag: '相同尾号'
//           }
//         } else if (q === 'msm') {
//           return {
//             key: '1135',
//             nums: ['13811359617'],
//             tag: '相同号段'
//           }
//         } else if (q === 'AAA') {
//           return {
//             key: 'AAA',
//             nums: ['13877759617'],
//             tag: '三连号'
//           }
//         } else if (q === 'AAAA') {
//           return {
//             key: 'AAAA',
//             nums: ['13877779617'],
//             tag: '四连号'
//           }
//         } else if (q === '188') {
//           return {
//             key: '188',
//             nums: ['13818889617', '13818899617'],
//             tag: '稀有靓号'
//           }
//         } 
//         else if (q === '1352') {
//           return {
//             key: '1352',
//             nums: ['13813529617', '13813529616', '13813529615'],
//             tag: '稀有靓号'
//           }
//         } 
//         else if (q === '139') {
//           return {
//             key: '139',
//             nums: ['13913529617', '13913529616', '13913529615','13913529618', '13913529619'],
//             tag: '稀有靓号'
//           }
//         } 
//         else if (q === '168') {
//           return {
//             key: '168',
//             nums: ['16877779617'],
//             tag: '发达号'
//           }
//         } else if (q === '188') {
//           return {
//             key: '188',
//             nums: ['18877779617'],
//             tag: '发达号'
//           }
//         } else if (q === '668') {
//           return {
//             key: '668',
//             nums: ['1896689617'],
//             tag: '发达号'
//           }
//         } else if (q === '988') {
//           return {
//             key: '988',
//             nums: ['1898889617'],
//             tag: '发达号'
//           }
//         } else if (q === '2023') {
//           return {
//             key: '2023',
//             nums: ['1412202322', '1412202322'],
//             tag: '祈福号'
//           }
//         } else if (q === '23') {
//           return {
//             key: '23',
//             nums: ['1412652322', '1412652322', '1412652322', '1412652322'],
//             tag: '祈福号'
//           }
//         } else if (q === '211') {
//           return {
//             key: '211',
//             nums: ['1412202211', '1412202211', '1412202211', '1412202211'],
//             tag: '学霸号'
//           }
//         } else if (q === '985') {
//           return {
//             key: '985',
//             nums: ['1412652985', '1412652985'],
//             tag: '学霸号'
//           }
//         } else if (q === '88') {
//           return {
//             key: '88',
//             nums: ['1412652988', '1412652988'],
//             tag: '芒果专属'
//           }
//         } else if (q === 'sr1') {
//           return {
//             key: 'sr1',
//             replaceKey: '1998',
//             nums: ['1412651998', '1412651998'],
//             tag: '出生年份'
//           }
//         } else if (q === 'sr2') {
//           return {
//             key: 'sr2',
//             replaceKey: '0821',
//             nums: ['1412650821', '1412650821'],
//             tag: '生日号'
//           }
//         }
//         return {
//           key: 'q',
//           nums: [],
//           tag: ''
//         }
//       })
//     // console.log(list)
//   }
//   return {
//     data: list,
//     result: 0
//   }
// })

// // 获取芒果卡数据
// Mock.mock(RegExp('/app/queryMangoNumbers'), (params) => {
//   return {
//     data: [
//       // {
//       //   key: '88',
//       //   nums: [
//       //     {
//       //       checkCode: 'cTN6R9ejXCZSY2j3ttPzTQPy6MRiQBJzywDoYWEHA8vsghu8VVh2SBLqp6J+wZt7TSypdeZj/6+B+K/o6Getqw==',
//       //       number: '13931542188'
//       //     },
//       //     {
//       //       checkCode: 'cTN6R9ejXCZSY2j3ttPzTQPy6MRiQBJzywDoYWEHA8vsghu8VVh2SBLqp6J+wZt7TSypdeZj/6+B+K/o6Getqw=',
//       //       number: '13938842123'
//       //     },
//       //     {
//       //       checkCode: 'cTN6R9ejXCZSY2j3ttPzTQPy6MRiQBJzywDoYWEHA8vsghu8VVh2SBLqp6J+wZt7TSypdeZj/6+B+K/o6Getqw',
//       //       number: '13932142883'
//       //     },
//       //     {
//       //       checkCode: 'cTN6R9ejXCZSY2j3ttPzTQPy6MRiQBJzywDoYWEHA8vsghu8VVh2SBLqp6J+wZt7TSypdeZj/6+B+K/o6Getq',
//       //       number: '13988142233'
//       //     },
//       //     {
//       //       checkCode: 'cTN6R9ejXCZSY2j3ttPzTQPy6MRiQBJzywDoYWEHA8vsghu8VVh2SBLqp6J+wZt7TSypdeZj/6+B+K/o6Getqw==',
//       //       number: '14931542188'
//       //     },
//       //     {
//       //       checkCode: 'cTN6R9ejXCZSY2j3ttPzTQPy6MRiQBJzywDoYWEHA8vsghu8VVh2SBLqp6J+wZt7TSypdeZj/6+B+K/o6Getqw=',
//       //       number: '15938842123'
//       //     },
//       //     {
//       //       checkCode: 'cTN6R9ejXCZSY2j3ttPzTQPy6MRiQBJzywDoYWEHA8vsghu8VVh2SBLqp6J+wZt7TSypdeZj/6+B+K/o6Getqw',
//       //       number: '16932142883'
//       //     },
//       //     {
//       //       checkCode: 'cTN6R9ejXCZSY2j3ttPzTQPy6MRiQBJzywDoYWEHA8vsghu8VVh2SBLqp6J+wZt7TSypdeZj/6+B+K/o6Getq',
//       //       number: '17988142233'
//       //     },
//       //     {
//       //       checkCode: 'cTN6R9ejXCZSY2j3ttPzTQPy6MRiQBJzywDoYWEHA8vsghu8VVh2SBLqp6J+wZt7TSypdeZj/6+B+K/o6Getqw==',
//       //       number: '14931542288'
//       //     },
//       //     {
//       //       checkCode: 'cTN6R9ejXCZSY2j3ttPzTQPy6MRiQBJzywDoYWEHA8vsghu8VVh2SBLqp6J+wZt7TSypdeZj/6+B+K/o6Getqw=',
//       //       number: '15938842124'
//       //     },
//       //     {
//       //       checkCode: 'cTN6R9ejXCZSY2j3ttPzTQPy6MRiQBJzywDoYWEHA8vsghu8VVh2SBLqp6J+wZt7TSypdeZj/6+B+K/o6Getqw',
//       //       number: '16932142885'
//       //     },
//       //     {
//       //       checkCode: 'cTN6R9ejXCZSY2j3ttPzTQPy6MRiQBJzywDoYWEHA8vsghu8VVh2SBLqp6J+wZt7TSypdeZj/6+B+K/o6Getq',
//       //       number: '17988142236'
//       //     }
//       //   ],
//       //   tag: '芒果专属',
//       //   q: '304_0304_923_0923_224_0224_816_0816_615_0615_416_0416_1212_66_520_521_sr_sy',
//       //   skey: ''
//       // },
//       {
//         key: '304',
//         nums: [
//           {
//             checkCode: 'jcpR9T/XydXMzOiuIP6QAqu4MQyEx8vyFq4N2BSX5/sghu8VVh2SBLqp6J+wZt7TSypdeZj/6+B+K/o6Getqw==',
//             number: '15167613046'
//           },
//           {
//             checkCode: 'cpR9T/XydXMzOiuIP6QAqu4MQyEx8vyFq4N2BSX5/sghu8VVh2SBLqp6J+wZt7TSypdeZj/6+B+K/o6Getqw==',
//             number: '14766693048'
//           }
//         ],
//         tag: '爱豆专属',
//         q: '304_0304_923_0923_224_0224_816_0816_615_0615_416_0416_1212_66_520_521_sr_sy',
//         skey: ''
//       },
//       {
//         key: '0304',
//         nums: [
//           {
//             checkCode: 'jcpR9T/XydXMzOiuIP6QAqu4MQyEx8vyFq4N2BSX5/sghu8VVh2SBLqp6J+wZt7TSypdeZj/6+B+K/o6Getqw==',
//             number: '15167603046'
//           },
//           {
//             checkCode: 'cpR9T/XydXMzOiuIP6QAqu4MQyEx8vyFq4N2BSX5/sghu8VVh2SBLqp6J+wZt7TSypdeZj/6+B+K/o6Getqw==',
//             number: '14766603048'
//           }
//         ],
//         tag: '芒果专属',
//         q: '304_0304_923_0923_224_0224_816_0816_615_0615_416_0416_1212_66_520_521_sr_sy',
//         skey: ''
//       },
//       {
//         key: '923',
//         nums: [
//           {
//             checkCode: 'jcpR9T/XydXMzOiuIP6QAqu4MQyEx8vyFq4N2BSX5/sghu8VVh2SBLqp6J+wZt7TSypdeZj/6+B+K/o6Getqw==',
//             number: '15167609236'
//           },
//           {
//             checkCode: 'cpR9T/XydXMzOiuIP6QAqu4MQyEx8vyFq4N2BSX5/sghu8VVh2SBLqp6J+wZt7TSypdeZj/6+B+K/o6Getqw==',
//             number: '14766609238'
//           }
//         ],
//         tag: '芒果专属',
//         q: '304_0304_923_0923_224_0224_816_0816_615_0615_416_0416_1212_66_520_521_sr_sy',
//         skey: ''
//       },
//       {
//         key: '0923',
//         nums: [
//           {
//             checkCode: 'jcpR9T/XydXMzOiuIP6QAqu4MQyEx8vyFq4N2BSX5/sghu8VVh2SBLqp6J+wZt7TSypdeZj/6+B+K/o6Getqw==',
//             number: '15167609236'
//           },
//           {
//             checkCode: 'cpR9T/XydXMzOiuIP6QAqu4MQyEx8vyFq4N2BSX5/sghu8VVh2SBLqp6J+wZt7TSypdeZj/6+B+K/o6Getqw==',
//             number: '14766609238'
//           }
//         ],
//         tag: '芒果专属',
//         q: '304_0304_923_0923_224_0224_816_0816_615_0615_416_0416_1212_66_520_521_sr_sy',
//         skey: ''
//       },
//       {
//         key: '224',
//         nums: [
//           {
//             checkCode: 'jcpR9T/XydXMzOiuIP6QAqu4MQyEx8vyFq4N2BSX5/sghu8VVh2SBLqp6J+wZt7TSypdeZj/6+B+K/o6Getqw==',
//             number: '15167602246'
//           },
//           {
//             checkCode: 'cpR9T/XydXMzOiuIP6QAqu4MQyEx8vyFq4N2BSX5/sghu8VVh2SBLqp6J+wZt7TSypdeZj/6+B+K/o6Getqw==',
//             number: '14766602248'
//           }
//         ],
//         tag: '芒果专属',
//         q: '304_0304_923_0923_224_0224_816_0816_615_0615_416_0416_1212_66_520_521_sr_sy',
//         skey: ''
//       },
//       {
//         key: '0224',
//         nums: [
//           {
//             checkCode: 'jcpR9T/XydXMzOiuIP6QAqu4MQyEx8vyFq4N2BSX5/sghu8VVh2SBLqp6J+wZt7TSypdeZj/6+B+K/o6Getqw==',
//             number: '15167602246'
//           },
//           {
//             checkCode: 'cpR9T/XydXMzOiuIP6QAqu4MQyEx8vyFq4N2BSX5/sghu8VVh2SBLqp6J+wZt7TSypdeZj/6+B+K/o6Getqw==',
//             number: '14766602248'
//           }
//         ],
//         tag: '芒果专属',
//         q: '304_0304_923_0923_224_0224_816_0816_615_0615_416_0416_1212_66_520_521_sr_sy',
//         skey: ''
//       },
//       {
//         key: '816',
//         nums: [
//           {
//             checkCode: 'jcpR9T/XydXMzOiuIP6QAqu4MQyEx8vyFq4N2BSX5/sghu8VVh2SBLqp6J+wZt7TSypdeZj/6+B+K/o6Getqw==',
//             number: '15167608166'
//           },
//           {
//             checkCode: 'cpR9T/XydXMzOiuIP6QAqu4MQyEx8vyFq4N2BSX5/sghu8VVh2SBLqp6J+wZt7TSypdeZj/6+B+K/o6Getqw==',
//             number: '14766608168'
//           }
//         ],
//         tag: '芒果专属',
//         q: '304_0304_923_0923_224_0224_816_0816_615_0615_416_0416_1212_66_520_521_sr_sy',
//         skey: ''
//       },
//       {
//         key: '0816',
//         nums: [
//           {
//             checkCode: 'jcpR9T/XydXMzOiuIP6QAqu4MQyEx8vyFq4N2BSX5/sghu8VVh2SBLqp6J+wZt7TSypdeZj/6+B+K/o6Getqw==',
//             number: '15167608166'
//           },
//           {
//             checkCode: 'cpR9T/XydXMzOiuIP6QAqu4MQyEx8vyFq4N2BSX5/sghu8VVh2SBLqp6J+wZt7TSypdeZj/6+B+K/o6Getqw==',
//             number: '14766608168'
//           }
//         ],
//         tag: '芒果专属',
//         q: '304_0304_923_0923_224_0224_816_0816_615_0615_416_0416_1212_66_520_521_sr_sy',
//         skey: ''
//       },
//       {
//         key: '615',
//         nums: [
//           {
//             checkCode: 'jcpR9T/XydXMzOiuIP6QAqu4MQyEx8vyFq4N2BSX5/sghu8VVh2SBLqp6J+wZt7TSypdeZj/6+B+K/o6Getqw==',
//             number: '15167606156'
//           },
//           {
//             checkCode: 'cpR9T/XydXMzOiuIP6QAqu4MQyEx8vyFq4N2BSX5/sghu8VVh2SBLqp6J+wZt7TSypdeZj/6+B+K/o6Getqw==',
//             number: '14766606158'
//           }
//         ],
//         tag: '芒果专属',
//         q: '304_0304_923_0923_224_0224_816_0816_615_0615_416_0416_1212_66_520_521_sr_sy',
//         skey: ''
//       },
//       {
//         key: '0615',
//         nums: [
//           {
//             checkCode: 'jcpR9T/XydXMzOiuIP6QAqu4MQyEx8vyFq4N2BSX5/sghu8VVh2SBLqp6J+wZt7TSypdeZj/6+B+K/o6Getqw==',
//             number: '15167606156'
//           },
//           {
//             checkCode: 'cpR9T/XydXMzOiuIP6QAqu4MQyEx8vyFq4N2BSX5/sghu8VVh2SBLqp6J+wZt7TSypdeZj/6+B+K/o6Getqw==',
//             number: '14766606158'
//           }
//         ],
//         tag: '芒果专属',
//         q: '304_0304_923_0923_224_0224_816_0816_615_0615_416_0416_1212_66_520_521_sr_sy',
//         skey: ''
//       },
//       {
//         key: '416',
//         nums: [
//           {
//             checkCode: 'jcpR9T/XydXMzOiuIP6QAqu4MQyEx8vyFq4N2BSX5/sghu8VVh2SBLqp6J+wZt7TSypdeZj/6+B+K/o6Getqw==',
//             number: '15167604166'
//           },
//           {
//             checkCode: 'cpR9T/XydXMzOiuIP6QAqu4MQyEx8vyFq4N2BSX5/sghu8VVh2SBLqp6J+wZt7TSypdeZj/6+B+K/o6Getqw==',
//             number: '14766604168'
//           }
//         ],
//         tag: '芒果专属',
//         q: '304_0304_923_0923_224_0224_816_0816_615_0615_416_0416_1212_66_520_521_sr_sy',
//         skey: ''
//       },
//       {
//         key: '0416',
//         nums: [
//           {
//             checkCode: 'jcpR9T/XydXMzOiuIP6QAqu4MQyEx8vyFq4N2BSX5/sghu8VVh2SBLqp6J+wZt7TSypdeZj/6+B+K/o6Getqw==',
//             number: '15167604166'
//           },
//           {
//             checkCode: 'cpR9T/XydXMzOiuIP6QAqu4MQyEx8vyFq4N2BSX5/sghu8VVh2SBLqp6J+wZt7TSypdeZj/6+B+K/o6Getqw==',
//             number: '14766604168'
//           }
//         ],
//         tag: '芒果专属',
//         q: '304_0304_923_0923_224_0224_816_0816_615_0615_416_0416_1212_66_520_521_sr_sy',
//         skey: ''
//       },
//       {
//         key: '1212',
//         nums: [
//           {
//             checkCode: 'jcpR9T/XydXMzOiuIP6QAqu4MQyEx8vyFq4N2BSX5/sghu8VVh2SBLqp6J+wZt7TSypdeZj/6+B+K/o6Getqw==',
//             number: '15167612126'
//           },
//           {
//             checkCode: 'cpR9T/XydXMzOiuIP6QAqu4MQyEx8vyFq4N2BSX5/sghu8VVh2SBLqp6J+wZt7TSypdeZj/6+B+K/o6Getqw==',
//             number: '14766612128'
//           }
//         ],
//         tag: '芒果专属',
//         q: '304_0304_923_0923_224_0224_816_0816_615_0615_416_0416_1212_66_520_521_sr_sy',
//         skey: ''
//       },
//       {
//         key: '66',
//         nums: [
//           // {
//           //   checkCode: 'jcpR9T/XydXMzOiuIP6QAqu4MQyEx8vyFq4N2BSX5/sghu8VVh2SBLqp6J+wZt7TSypdeZj/6+B+K/o6Getqw==',
//           //   number: '15167612266'
//           // },
//           {
//             checkCode: 'cpR9T/XydXMzOiuIP6QAqu4MQyEx8vyFq4N2BSX5/sghu8VVh2SBLqp6J+wZt7TSypdeZj/6+B+K/o6Getqw==',
//             number: '14766691228'
//           }
//         ],
//         tag: '芒果专属',
//         q: '304_0304_923_0923_224_0224_816_0816_615_0615_416_0416_1212_66_520_521_sr_sy',
//         skey: ''
//       },
//       {
//         key: '520',
//         nums: [
//           {
//             checkCode: 'jcpR9T/XydXMzOiuIP6QAqu4MQyEx8vyFq4N2BSX5/sghu8VVh2SBLqp6J+wZt7TSypdeZj/6+B+K/o6Getqw==',
//             number: '15167520266'
//           },
//           {
//             checkCode: 'cpR9T/XydXMzOiuIP6QAqu4MQyEx8vyFq4N2BSX5/sghu8VVh2SBLqp6J+wZt7TSypdeZj/6+B+K/o6Getqw==',
//             number: '14766520228'
//           }
//         ],
//         tag: '芒果专属',
//         q: '304_0304_923_0923_224_0224_816_0816_615_0615_416_0416_1212_66_520_521_sr_sy',
//         skey: ''
//       },
//       {
//         key: '521',
//         nums: [
//           {
//             checkCode: 'jcpR9T/XydXMzOiuIP6QAqu4MQyEx8vyFq4N2BSX5/sghu8VVh2SBLqp6J+wZt7TSypdeZj/6+B+K/o6Getqw==',
//             number: '15167521266'
//           },
//           {
//             checkCode: 'cpR9T/XydXMzOiuIP6QAqu4MQyEx8vyFq4N2BSX5/sghu8VVh2SBLqp6J+wZt7TSypdeZj/6+B+K/o6Getqw==',
//             number: '14766521228'
//           }
//         ],
//         tag: '芒果专属',
//         q: '304_0304_923_0923_224_0224_816_0816_615_0615_416_0416_1212_66_520_521_sr_sy',
//         skey: ''
//       },
//       {
//         key: '1228',
//         nums: [
//           {
//             checkCode: 'jcpR9T/XydXMzOiuIP6QAqu4MQyEx8vyFq4N2BSX5/sghu8VVh2SBLqp6J+wZt7TSypdeZj/6+B+K/o6Getqw==',
//             number: '15167612286'
//           },
//           {
//             checkCode: 'cpR9T/XydXMzOiuIP6QAqu4MQyEx8vyFq4N2BSX5/sghu8VVh2SBLqp6J+wZt7TSypdeZj/6+B+K/o6Getqw==',
//             number: '14768291228'
//           },
//           {
//             checkCode: 'pR9T/XydXMzOiuIP6QAqu4MQyEx8vyFq4N2BSX5/sghu8VVh2SBLqp6J+wZt7TSypdeZj/6+B+K/o6Getqw==',
//             number: '18318412281'
//           },
//           {
//             checkCode: 'R9T/XydXMzOiuIP6QAqu4MQyEx8vyFq4N2BSX5/sghu8VVh2SBLqp6J+wZt7TSypdeZj/6+B+K/o6Getqw==',
//             number: '15810541228'
//           },
//           {
//             checkCode: 'pR9T/XydXMzOiuIP6QAqu4MQyEx8vyFq4N2BSX5/sghu8VVh2SBLqp6J+wZt7TSypdeZj/6+B+K/o6Getqw==',
//             number: '18318412282'
//           },
//           {
//             checkCode: 'R9T/XydXMzOiuIP6QAqu4MQyEx8vyFq4N2BSX5/sghu8VVh2SBLqp6J+wZt7TSypdeZj/6+B+K/o6Getqw==',
//             number: '15810531228'
//           }
//         ],
//         tag: '生日号',
//         q: '304_0304_923_0923_224_0224_816_0816_615_0615_416_0416_1212_66_520_521_sr_sy',
//         skey: ''
//       },
//       {
//         key: '1979',
//         nums: [
//           // {
//           //   checkCode: 'jcpR9T/XydXMzOiuIP6QAqu4MQyEx8vyFq4N2BSX5/sghu8VVh2SBLqp6J+wZt7TSypdeZj/6+B+K/o6Getqw==',
//           //   number: '11979612286'
//           // },
//           // {
//           //   checkCode: 'cpR9T/XydXMzOiuIP6QAqu4MQyEx8vyFq4N2BSX5/sghu8VVh2SBLqp6J+wZt7TSypdeZj/6+B+K/o6Getqw==',
//           //   number: '14197991228'
//           // },
//           // {
//           //   checkCode: 'pR9T/XydXMzOiuIP6QAqu4MQyEx8vyFq4N2BSX5/sghu8VVh2SBLqp6J+wZt7TSypdeZj/6+B+K/o6Getqw==',
//           //   number: '18318197981'
//           // },
//           // {
//           //   checkCode: 'R9T/XydXMzOiuIP6QAqu4MQyEx8vyFq4N2BSX5/sghu8VVh2SBLqp6J+wZt7TSypdeZj/6+B+K/o6Getqw==',
//           //   number: '15197941228'
//           // }
//         ],
//         tag: '生日号',
//         q: '304_0304_923_0923_224_0224_816_0816_615_0615_416_0416_1212_66_520_521_sr_sy',
//         skey: ''
//       }
//     ],
//     result: 0
//   }
// })

// Mock.mock(RegExp('/app/search'), () => {
//   return {
//     result: 0,
//     data: {
//       keyword: '123',
//       numbers: ['18123232321', '18123000001', '18123000002', '18123000003', '18123000004', '18123000005', '18123000006']
//     }
//   }
// })

// Mock.mock(RegExp('/app/search'), () => {
//   return {
//     result: 0,
//     data: {
//       keyword: '123',
//       numbers: ['18123232321', '18123000001', '18123000002', '18123000003', '18123000004', '18123000005', '18123000006']
//     }
//   }
// })

// Mock.mock(RegExp('/app/operate_unifyH5'), () => {
//   return {
//     result: 0,
//     isBj: 1,
//     region: [{
//       keyword: 'actoptimization2022_lunbo',
//       block: [{
//         title: '运管可配置',
//         url: 'go-shake',
//         imgurl: 'https://gimg2.baidu.com/image_search/src=http%3A%2F%2Fimg.jj20.com%2Fup%2Fallimg%2Ftp09%2F21042G4331941H-0-lp.jpg&refer=http%3A%2F%2Fimg.jj20.com&app=2002&size=f9999,10000&q=a80&n=0&g=0n&fmt=jpeg?sec=1642907302&t=ef3e736d97226da1b5efee8b16b60e7d'
//       }, {
//         title: '运管可配置',
//         url: 'https://st.bj.chinamobile.com:7443/shortAct/act121/index.html&&&https://sc.bj.chinamobile.com/shortAct/act121/index.html',
//         imgurl: 'https://gimg2.baidu.com/image_search/src=http%3A%2F%2Fimg.jj20.com%2Fup%2Fallimg%2Ftp09%2F21042G4331941H-0-lp.jpg&refer=http%3A%2F%2Fimg.jj20.com&app=2002&size=f9999,10000&q=a80&n=0&g=0n&fmt=jpeg?sec=1642907302&t=ef3e736d97226da1b5efee8b16b60e7d'
//       }]
//     }, {
//       keyword: 'actoptimization2022_banner',
//       block: [{
//         title: '运管可配置',
//         url: 'https://st.bj.chinamobile.com:7443/shortAct/act121/index.html&&&https://sc.bj.chinamobile.com/shortAct/act121/index.html',
//         imgurl: 'https://gimg2.baidu.com/image_search/src=http%3A%2F%2Fimg.jj20.com%2Fup%2Fallimg%2Ftp09%2F21042G4331941H-0-lp.jpg&refer=http%3A%2F%2Fimg.jj20.com&app=2002&size=f9999,10000&q=a80&n=0&g=0n&fmt=jpeg?sec=1642907302&t=ef3e736d97226da1b5efee8b16b60e7d'
//       }, {
//         title: '运管可配置',
//         url: 'https://st.bj.chinamobile.com:7443/shortAct/act121/index.html&&&https://sc.bj.chinamobile.com/shortAct/act121/index.html',
//         imgurl: 'https://gimg2.baidu.com/image_search/src=http%3A%2F%2Fimg.jj20.com%2Fup%2Fallimg%2Ftp09%2F21042G4331941H-0-lp.jpg&refer=http%3A%2F%2Fimg.jj20.com&app=2002&size=f9999,10000&q=a80&n=0&g=0n&fmt=jpeg?sec=1642907302&t=ef3e736d97226da1b5efee8b16b60e7d'
//       }]
//     }]
//   }
// })

// Mock.setup({
//   timeout: 200
// })
