import request from '@/utils/request'
import CHANNEL from '@/utils/channel'
import { getQueryString } from '@/utils/utils'
// 根据默认号码
export function queryDefaultNum(params = {}) {
  let url = '/app/queryAppDefaultNum'
  if (CHANNEL.isAPP()) {
    url = '/app/queryDefaultNum'
  }
  return request({
    url,
    method: 'get',
    params,
    encrypt: 2
  })
}
// 根据参数查询定制号码信息
export function queryCustomNumber(params = {}) {
  let url = '/app/queryAppCustomNumber'
  if (CHANNEL.isAPP()) {
    url = '/app/queryCustomNumber'
  }
  return request({
    url,
    method: 'get',
    params
  })
}

// 查询芒果卡号码
export function queryMangoNumber(params = {}) {
  let url = '/app/queryMangoNumbers'
  if (getQueryString('mock') === 'mock') {
    url = '/questionTmpAct/assistant/mock/queryMangoNumbers'
  }
  return request({
    url,
    method: 'get',
    params
  })
}

// 查询快递地址信息
export function getExpress(params = {}) {
  return request({
    url: '/app/getExpress',
    method: 'get',
    params
  })
}

// 提交芒果卡订单
export function orderMangoCard(params = {}) {
  const token = sessionStorage.getItem('userToken')
  return request({
    url: `/app/orderMangoCard?token=${token}`,
    method: 'post',
    data: params,
    encrypt: 1
  })
}
// export function orderMangoCard(params = {}) {
//   return request({
//     url: '/app/orderMangoCard',
//     method: 'get',
//     params
//   })
// }

// 查询号码前缀
// 接口地址: /app/qureyNumPrefix
// 根据参数查询定制号码信息
export function qureyNumPrefix(params = {}) {
  let url = '/app/qureyAppNumPrefix'
  if (CHANNEL.isAPP()) {
    url = '/app/qureyNumPrefix'
  }
  return request({
    url,
    method: 'get',
    params
  })
}

export function searchNum(params = {}) {
  let url = '/app/searchAppNum'
  if (CHANNEL.isAPP()) {
    url = '/app/searchNum'
  }
  return request({
    url,
    method: 'get',
    params
  })
}
export function exactSearchNum(params = {}) {
  const url = '/app/precisSearch'
  return request({
    url,
    method: 'get',
    params
  })
}
