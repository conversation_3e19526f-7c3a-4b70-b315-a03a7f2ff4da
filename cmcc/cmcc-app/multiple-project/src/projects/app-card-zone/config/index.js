/*
 * @Author: zhen<PERSON><PERSON><PERSON> zheng<PERSON><PERSON>@aspirecn.com
 * @Date: 2023-03-24 19:08:31
 * @LastEditors: zhengwen<PERSON> <EMAIL>
 * @LastEditTime: 2025-04-18 15:37:58
 * @FilePath: \multiple-project\src\projects\app-card-zone\config\index.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
export const menuList = [{
        url: require('../assets/images/menu/xhrw.png'),
        name: '携号入网',
        appHref: 'https://app.10086.cn/cmcc-app/transferNet/verification.html',
        wxHref: 'https://app.10086.cn/cmcc-app/transferNet/verification.html'
    },
    {
        url: require('../assets/images/menu/xkjh.png'),
        name: '万能副卡',
        pageId: 'charge',
        appHref: 'https://service.bj.10086.cn/m/num/num/wnfk/showFontPage.action',
        wxHref: 'https://service.bj.10086.cn/m/num/num/wnfk/showFontPage.action'
    },
    {
        url: require('../assets/images/menu/spkf.png'),
        name: '视频客服',
        pageId: 'vedio-service',
        // appHref: 'https://wx.10086.cn/website/page/broadTroubleRemoval/index?cscd=2A6620117A53EDEEE96CF6F60085BE6D&sceneEntry=250',
        wxHref: 'https://wx.10086.cn/wxpullapp/bj/cmcc_transfer/loading-transfer/index.html?pageName=video&redirect=true'

    },
    {
        url: require('../assets/images/menu/rare.png'),
        name: '稀有靓号',
        pageId: 'rare-number',
        appHref: 'https://service.bj.10086.cn/m/num/num/order/jx/showFontPage.action?channelName=jtapp&sourceId=JT',
        wxHref: 'https://service.bj.10086.cn/m/num/num/order/jx/showFontPage.action?sourceId=WT'

    },
    {
        url: require('../assets/images/menu/ddwl.png'),
        name: '订单物流',
        appHref: 'https://service.bj.10086.cn/m/num/member/simcardOrder/showSearchNew.action',
        wxHref: 'https://service.bj.10086.cn/m/num/member/simcardOrder/showSearchNew.action'

    }
]