<template>
  <header v-if="isWX && misdnmask && info" class="wx-information">
    <div class="wx-information__main">
      <div class="main-head" :style="`background-image: url(${info.avatarUrl || info.headimgurl || defaultImg})`" @click="otherPhoneLogin"></div>
      <div class="main-name">
         <span class="main-name__misdnmask">{{ misdnmask }}</span>
         <span class="main-name__change-number" @click="otherPhoneLogin">更换</span>
      </div>
    </div>
  </header>
</template>
<script>
import CHANNEL from '@/utils/channel'
const isWx = CHANNEL.isWX()
export default {
  name: 'WxInformation',
  props: {
    info: {
      type: Object,
      default: () => {
        const wxinfo = sessionStorage.getItem('wxinfo') || '{}'
        if (isWx && wxinfo && String(wxinfo) !== '[object Object]') {
          return JSON.parse(wxinfo)
        } else {
          return {}
        }
      }
    },
    misdnmask: {
      type: String,
      default: sessionStorage.getItem('misdnmask')
    }
  },
  data() {
    return {
      userName: '',
      userImg: '',
      defaultImg: require('./image/avatar.png'),
      isWX: isWx
    }
  },
  methods: {
    otherPhoneLogin() {
      window['loca' + 'tion'].href = `${window.location.origin}/activityUnifyLogin/index.html?titleName=&pi=&backUrl=${encodeURIComponent(window.ACT_PATH)}`
    }
  }
}
</script>
<style lang="scss" scope>
.wx-information {
  position: absolute;
  height: 16vw;
  width: 100%;
  padding-left: 10px;
  padding-bottom: 3vw;
  top: 0;
  display: flex;
  align-items: center;
  z-index: 2;
  &__main {
    float: left;
    display: flex;
    align-items: center;
    position: relative;
    height: 9vw;
    .main-head {
      width: 66px;
      height: 66px;
      background-repeat: no-repeat;
      background-position: center center;
      background-size: cover;
      background-color: #ffffff;
      z-index: 10;
      border-radius: 50%;
    }
    .main-name {
      display: flex;
      flex-direction: column;
      color: #ffffff;
      font-size: 21px;
      align-items: center;
      &__misdnmask {
        margin-bottom: 5px;
      }
      &__change-number {
        display: block;
        padding: 5px 20px;
        border: solid #ffffff 1px;
        border-radius: 18px;
      }
    }
  }
}
</style>
