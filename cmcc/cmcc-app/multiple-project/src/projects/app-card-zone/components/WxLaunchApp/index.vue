<template>
  <wx-open-launch-app
    v-if="wechatState"
    style="
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      display: block;
      -webkit-tap-highlight-color: transparent;
    "
    :extinfo="hxappPath"
    appid="wxa48f0b9e1ed8f680"
  >
    <script type="text/wxtag-template">
      <style>
          .wx-btn {
              width: 300px;
              height: 87px;
              display: block;
              color: #fff;
              opacity: 0;
              -webkit-tap-highlight-color: transparent;
          }
      </style>
      <button
          class="wx-btn"
      >小程序</button>
    </script>
  </wx-open-launch-app>
</template>

<script>
import CHANNEL from '@/utils/channel'
export default {
  name: 'WxLaunchApp',
  props: {
    launch: Function,
    error: Function,
    hxappPath: {
      type: String,
      default: 'bjcmcc://bjcmcc00001'
    }
  },
  data() {
    return {
      wechatState: false // 控制是否显示组件
    }
  },
  mounted() {
    // 获取wx-open-launch-app dome
    const launchBtn = this.$el
    if (!launchBtn) {
      return
    }
    //
    launchBtn.addEventListener('launch', (e) => {
      this.launch && this.launch(e)
    })
    //
    launchBtn.addEventListener('error', (e) => {
      this.error && this.error(e)
    })
  },
  created() {
    if (CHANNEL.isWX()) {
      // 微信环境下才显示
      this.wechatState = true
    }
  }
}
</script>
