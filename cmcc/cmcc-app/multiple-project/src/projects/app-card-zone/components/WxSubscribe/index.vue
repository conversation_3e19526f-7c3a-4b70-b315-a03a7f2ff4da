<template>
  <wx-open-subscribe
    v-if="wechatState"
    style="
      width: 100%;
      height: 100%;
      display: block;
      -webkit-tap-highlight-color: transparent;
    "
    id="launch-btn__dy"
    :template="templateId"
  >
    <script type="text/wxtag-template" slot="style">
      <style>
      .subscribe-btn {
          width: 300px;
          height: 87px;
          display: block;
          color: #fff;
          opacity: 0;
          -webkit-tap-highlight-color: transparent
      }
      </style>
    </script>
    <script type="text/wxtag-template">
      <button class="subscribe-btn wx-btn">
      一次性模版消息订阅
      </button>
    </script>
  </wx-open-subscribe>
</template>

<script>
import CHANNEL from '@/utils/channel'
export default {
  name: 'WxSubscribe',
  props: {
    templateId: {
      type: String,
      required: true
    },
    success: Function,
    error: Function
  },
  data() {
    return {
      wechatState: false // 控制是否显示组件
    }
  },
  mounted() {
    // 获取wx-open-subscribe dome
    const launchBtn = document.getElementById('launch-btn__dy')
    if (launchBtn) {
      launchBtn.addEventListener('success', (res) => {
        // // console.log(res.detail, '成功回调')
        this.success && this.success(res)
      })
      launchBtn.addEventListener('error', (res) => {
        // // console.log(res.detail, '错误回调')
        this.error && this.error(res)
      })
    }
  },
  created() {
    if (CHANNEL.isWX()) {
      // 微信环境下才显示
      this.wechatState = true
    }
  }
}
</script>
