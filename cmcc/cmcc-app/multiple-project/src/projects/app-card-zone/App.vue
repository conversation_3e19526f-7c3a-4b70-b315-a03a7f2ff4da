<template>
  <div id="app">
    <router-view />
  </div>
</template>

<script>
// import { getQueryString } from '@/utils/utils'
// import CHANNEL from '@/utils/channel'
import imgList from './utils/img'
import Webtrends1 from '@/utils/webtrends'
import { mapMutations } from 'vuex'
import { getBottomNavigationHandlerHeight } from '../../../../../../bjapp-model/vue2/js/jt-app-ability'
export default {
  name: 'App',
  data() {
    const userToken = sessionStorage.getItem('userToken')
    return {
      userToken,
      imgList
      // showView: false // 显示隐藏页面
    }
  },
  mounted() {
    this.preloading(imgList)
    Webtrends1.setGeneralProps()
    getBottomNavigationHandlerHeight().then(res => {
      this.UPDATE_AIBOTTOM(res)
    })
  },
  methods: {
    ...mapMutations(['UPDATE_AIBOTTOM']),
    preloading(arr) {
      return new Promise((resolve, reject) => {
        const all = arr
        const imgTotal = all.length
        const img = []
        let index = 0
        for (let i = 0; i < imgTotal; i++) {
          img[i] = new Image()
          img[i].src = all[i]
          img[i].onload = () => {
            index++
            if (index === imgTotal) {
              resolve()
            }
          }
        }
      })
    }
  }
}
</script>

<style lang="scss">
@import "./mixins/common.scss";
#app {
  font-family: "Avenir", Helvetica, Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  max-width: 1080px;
  margin: 0 auto;
}
</style>
