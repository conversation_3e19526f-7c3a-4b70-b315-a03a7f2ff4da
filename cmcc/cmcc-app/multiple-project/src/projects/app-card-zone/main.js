import '@/utils/amfe-flexible'
import Vue from 'vue'
import './utils/activity'
import Loading from '@/components/Loading'
import '@/styles/reset.css'
import 'vant/es/loading/style'
import 'vant/es/toast/style'
import 'vant/es/icon/style'
import 'vant/es/checkbox/style'
import 'vant/es/tabs/style'
import 'vant/es/tab/style'
import '@/utils/vantUI'
import App from './App'
import router from './router'
// import { Base64 } from '@/utils/base64'
// 项目公共模块
import actMixins from '@/mixins'
import Webtrends1 from '@/utils/webtrends'
import VantLoading from 'vant/es/loading'
import Toast from 'vant/es/toast'
import Icon from 'vant/es/icon'
import Checkbox from 'vant/es/checkbox'
import Tabs from 'vant/es/tabs'
import Tab from 'vant/es/tab'
// import { getQueryString } from '@/utils/utils'
// import { getSetShareParamsWX } from './utils'
// import { judgeLoginAndLogin } from '../../../../../../bjapp-model/vue2/js/login'
import * as jtAbility from '../../../../../../bjapp-model/vue2/js/jt-app-ability'
// import CHANNEL from '../../../../../../bjapp-model/vue2/js/channel'
import store from './store/index'

Vue.mixin(actMixins)
Vue.use(Loading).use(VantLoading).use(Toast).use(Icon).use(Checkbox).use(Tabs).use(Tab)
Vue.prototype.$Webtrends = Webtrends1
window.$APPABILITY = jtAbility

if (process.env.NODE_ENV === 'development') {
  sessionStorage['set' + 'Item']('userToken', 'yus')
  // require('./mock/index')
}
initVue()

function initVue() {
  return new Vue({
    el: '#app',
    router,
    store,
    render: (h) => h(App)
  })
}

// window.onload = () => {
//   Webtrends1.setGeneralProps()
//   Webtrends1.gdpCommonTrack('pageview', 'H5PageShow')
// }

