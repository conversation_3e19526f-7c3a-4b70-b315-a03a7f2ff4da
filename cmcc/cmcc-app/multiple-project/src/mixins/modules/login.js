/**
 * Created by linsang on 2020/10/27.
 */
import {
  authBase,
  wxLogin
} from '@/api/common.js'
import {
  getQueryString
} from '@/utils/utils'
export default {
  data() {
    return {
      userToken: '',
      defaultHeadImage: '',
      wxinfo: {
        avatarUrl: '',
        headimgurl: '',
        nickname: '',
        maskMisdn: ''
      }
    }
  },
  methods: {
    // 微信授权登录流程
    wxAuthBase() {
      // 通过分享链接进入后，code和state都会改变
      const code = this.$route.query.code || getQueryString('code') || ''
      const state =
        this.$route.query.state || getQueryString('state') || ''

      const params = {
        code: code,
        state: state
      }
      return new Promise((resolve, reject) => {
        authBase(params)
          .then(res => {
            if (res.errcode === 0) {
              // console.log(res.openid, 'res.openid')
              sessionStorage['set' + 'Item']('openid', res.openid)
              wxLogin({
                openid: res.openid
              })
                .then(res => {
                  const result = String(res.result)
                  if (result === '0') {
                    sessionStorage['set' + 'Item'](
                      'userToken',
                      res.token
                    )
                    // if (res.datas.mobile && window._tag.setMobile) {
                    //   window._tag.setMobile(res.datas.mobile)
                    // }
                    if (res.datas.wxinfo) {
                      this.wxinfo = res.datas.wxinfo
                      sessionStorage['set' + 'Item'](
                        'wxinfo',
                        JSON.stringify(this.wxinfo)
                      )
                    }
                    resolve(res)
                  } else {
                    reject(new Error(false))
                  }
                })
                .catch(() => {
                  reject(new Error(false))
                })
            } else {
              reject(new Error(false))
            }
          })
          .catch((e) => {
            reject(e)
          })
      })
    },
    wxLogin(openid) {
      const params = {
        openid: openid
      }
      return new Promise((resolve, reject) => {
        wxLogin(params)
          .then(res => {
            const result = String(res.result)
            if (result === '0') {
              // console.log(res, 'res')
              resolve(res)
            } else {
              reject(res)
            }
          })
          .catch(e => {
            reject(e)
          })
      })
    }
  }
}
