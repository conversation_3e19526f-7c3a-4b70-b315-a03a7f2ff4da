export default {
  methods: {
    preloading(arr) {
      return new Promise((resolve, reject) => {
        const all = arr
        const imgTotal = all.length
        const img = []
        let index = 0
        for (let i = 0; i < imgTotal; i++) {
          img[i] = new Image()
          img[i].src = all[i]
          img[i].onload = () => {
            index++
            if (index === imgTotal) {
              resolve()
            }
          }
        }
      })
    }
  }
}
