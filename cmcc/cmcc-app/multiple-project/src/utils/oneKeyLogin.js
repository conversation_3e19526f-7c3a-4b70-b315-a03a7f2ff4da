import {
  getSign,
  uniTokenValidate,
  wxUnBound
} from '@/api/common'

const APPID = '300011877563'
const VERSION = '1.2'
const YDRZ = window.ywAuth
// 获取网络类型（可选）
const connection = YDRZ.getConnection(APPID)
// cellular 数据流量、unknown 未知、wifi
// console.log(connection, 'connection')

const KEY = {
  getSign: function () {
    return new Promise((resolve, reject) => {
      const sign = KEY.sign()
      getSign({
        preSign: sign,
        delToken: true
      })
        .then(res => {
          resolve(res)
        })
        .catch(err => {
          reject(err)
        })
    })
  },
  volidToken: function (token, userInformation) {
    return new Promise((resolve, reject) => {
      const openid = sessionStorage.getItem('openid')
      wxUnBound({
        openid: openid || '',
        token: sessionStorage.getItem('userToken') || sessionStorage.getItem('userTokenLoginOut') || ''
      }).finally(() => {
        uniTokenValidate({
          token: token,
          userInformation: decodeURIComponent(userInformation),
          delChannel: true,
          openid: openid
        })
          .then(res => {
            resolve(res)
          })
          .catch(err => {
            reject(err)
          })
      })
    })
  },
  // 获取签名
  sign: function () {
    return YDRZ.getSign({
      appid: APPID,
      version: VERSION
    })
  },
  // 获取token
  getTokenInfo: function (data) {
    return new Promise((resolve, reject) => {
      YDRZ.getTokenInfo({
        data: {
          version: VERSION,
          appId: APPID,
          sign: data,
          openType: '0',
          expandParams: '',
          isTest: ''
        },
        success: function (res) {
          // console.log(res, 'res')
          resolve(res)
        },
        error: function (err) {
          // console.log(err, 'err')
          reject(err)
        }
      })
    })
  }
}
// 一键：获取加密sign 发送我方后台进行加密返回
export async function getOneKeyToken() {
  try {
    const signData = await KEY.getSign()
    if (Number(signData.result) === 0) {
      const {
        token,
        userInformation
      } = await KEY.getTokenInfo(signData.sign)
      const data = await KEY.volidToken(token, userInformation)
      if (data.result === 0 || data.result === '0') {
        return data
      } else {
        return false
      }
    } else {
      return false
    }
  } catch (error) {
    return false
  }
}
