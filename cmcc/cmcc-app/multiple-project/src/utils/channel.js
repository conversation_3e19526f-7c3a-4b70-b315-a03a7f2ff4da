/**
 * Created by linsang on 2019/8/10.
 */
import { getQueryString } from './utils'
const UA = window.navigator.userAgent.toLowerCase()
const iswxbro = UA.indexOf('micromessenger') > -1
const queryChannel = getQueryString('channel')
const origin = window.location.host
const CHANNEL = {
  // 生产环境,12065端口为业务项目现网端口，活动一般用不到
  isMaster() {
    if (window.location.port === '' || window.location.port === '12065') {
      return true
    } else {
      return false
    }
  },
  // 灰度环境
  isGray() {
    return !this.isMaster()
  },
  // 手厅侧，中国移动北京APP且是北京移动用户（window.isBjUser在接口返回，拦截器里配置,main.js初始默认为1，北京移动用户）
  isAPP() {
    try {
      if (window.aspireweb) {
        return true
      } else {
        return false
      }
    } catch (e) {
      return false
    }
  },
  // app版本700+（需要区分700+和690，690异网专区为H5页面，页面跳转方式区分700，700使用app能力跳转）
  isAPP700(ver = 840) {
    if (ver >= 700) {
      return true
    } else {
      return false
    }
  },
  isWXBrowser() {
    if (UA.indexOf('micromessenger') > -1) {
      return true
    } else {
      return false
    }
  },
  // 微信侧
  isWX() {
    if (UA.indexOf('micromessenger') > -1 && getQueryString('mm') === null) {
      return true
    } else {
      return false
    }
  },
  // 图文侧&&微厅侧 图文侧地址栏中带有参数mm，该字段为用户登录令牌
  isTW() {
    if (getQueryString('mm') !== null) {
      return true
    } else {
      return false
    }
  },
  // 异业侧
  isYY() {
    if (getQueryString('channel') === 'SD01' || getQueryString('channel') === 'SD02') {
      return true
    } else {
      return false
    }
  },
  isAndroid() {
    const u = navigator.userAgent
    if (u.indexOf('Android') > -1 || u.indexOf('Adr') > -1) {
      return true
    } else {
      return false
    }
  },
  isIOS() {
    const u = navigator.userAgent
    if (u.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/)) {
      return true
    } else {
      return false
    }
  },
  getAndroidVersion() {
    const ua = navigator.userAgent.toLowerCase()
    const match = ua.match(/android\s([0-9.]*)/)
    // return '8.0.0'
    return match ? match[1] : false
  },
  getIosVersion() {
    const str = navigator.userAgent.toLowerCase()
    const ver = str.match(/cpu iphone os (.*?) like mac os/)
    // return '14_0_1'
    return ver ? ver[1] : false
  },
  getWeixinVersion() {
    const wechatInfo = navigator.userAgent.match(/MicroMessenger\/([\d.]+)/i)
    return wechatInfo[1]
  },
  compareWeixinVer(newVer, baseVer) {
    // console.log('触发1')
    newVer = newVer.split('.')
    baseVer = baseVer.split('.')
    if (Number(newVer[0]) > Number(baseVer[0])) {
      // console.log('w1')
      return true
    } else if (Number(newVer[0]) === Number(baseVer[0])) {
      if (newVer[1] > baseVer[1]) {
        // console.log('w2')
        return true
      } else if (Number(newVer[1]) === Number(baseVer[1])) {
        if (Number(newVer[2]) >= Number(baseVer[2])) {
          // console.log('w3')
          return true
        } else {
          // console.log('w4')
          return false
        }
      } else {
        // console.log('w5')
        return false
      }
    } else {
      // console.log('w6')
      return false
    }
  },
  // 微信唤醒小程序标签兼容
  wxOpenLaunchWeappShow() {
    const weixinVer = this.getWeixinVersion()
    const diffWeixinVer = '7.0.12'
    if (this.isAndroid()) {
      let androidVer = ''
      if (this.getAndroidVersion().indexOf('.') > -1) {
        androidVer = Number(this.getAndroidVersion().split('.')[0])
      } else {
        androidVer = Number(this.getAndroidVersion())
      }
      // let androidVer = Number(this.getAndroidVersion())
      if (androidVer >= 5) {
        if (this.compareWeixinVer(weixinVer, diffWeixinVer)) {
          return true
        } else {
          return false
        }
      } else {
        return false
      }
    } else if (this.isIOS()) {
      const iosVer = this.getIosVersion().split('_')
      // let iosVer = '14_0_1'.split('_')
      const diffIosVer = [10, 3]
      iosVer.forEach(item => {
        item = Number(item)
      })
      // console.log(iosVer[0], diffIosVer[0], typeof iosVer[0], typeof diffIosVer[0])
      if (Number(iosVer[0]) > diffIosVer[0]) {
        // console.log('=1')
        if (this.compareWeixinVer(weixinVer, diffWeixinVer)) {
          // console.log('=2')
          return true
        } else {
          // console.log('=3')
          return false
        }
      } else if (Number(iosVer[0]) === diffIosVer[0]) {
        if (Number(iosVer[1]) >= diffIosVer[1]) {
          // console.log('=4')
          if (this.compareWeixinVer(weixinVer, diffWeixinVer)) {
            // console.log('=7')
            return true
          } else {
            // console.log('=8')
            return false
          }
        } else {
          // console.log('=5')
          return false
        }
      } else {
        // console.log('=6')
        return false
      }
    }
  },
  // 微信唤醒小程序标签兼容
  wxOpenLaunchWeappShow2() {
    const weixinVer = this.getWeixinVersion()
    const diffWeixinVer = '7.0.12'
    if (this.isAndroid()) {
      // let androidVer = Number(this.getAndroidVersion())
      let androidVer = ''
      if (this.getAndroidVersion().indexOf('.') > -1) {
        androidVer = Number(this.getAndroidVersion().split('.')[0])
      } else {
        androidVer = Number(this.getAndroidVersion())
      }
      if (androidVer >= 5) {
        if (this.compareWeixinVer(weixinVer, diffWeixinVer)) {
          return '0'
        } else {
          return '2'
        }
      } else {
        return '1'
      }
    } else if (this.isIOS()) {
      // ios 10_3_1
      // wx  7.0.3
      const iosVer = this.getIosVersion().split('_')
      // let iosVer = '14_0_1'.split('_')
      const diffIosVer = [10, 3]
      iosVer.forEach(item => {
        item = Number(item)
      })
      // console.log(typeof iosVer[0], typeof iosVer[1])
      if (Number(iosVer[0]) > diffIosVer[0]) {
        if (this.compareWeixinVer(weixinVer, diffWeixinVer)) {
          return '0'
        } else {
          return '2'
        }
      } else if (Number(iosVer[0]) === diffIosVer[0]) {
        if (Number(iosVer[1]) >= diffIosVer[1]) {
          if (this.compareWeixinVer(weixinVer, diffWeixinVer)) {
            return '0'
          } else {
            return '2'
          }
        } else {
          return '1'
        }
      } else {
        return '1'
      }
    }
  },
  checkAppVersion(num) {
    const ver = window.$APPABILITY.params.ver || sessionStorage.getItem('ver')
    if (Number(ver) >= num) {
      return true
    } else {
      return false
    }
  },
  isJT() {
    return queryChannel === 'JT' || queryChannel === 'JTWX' || origin === 'h5.bj.10086.cn' || false
  },
  isJTAPP() {
    return ((queryChannel === 'JT' || origin === 'h5.bj.10086.cn') && !iswxbro) || false
  },
  isJTWX() {
    return ((queryChannel === 'JTWX' || origin === 'h5.bj.10086.cn') && iswxbro) || false
  }
}
export default CHANNEL
