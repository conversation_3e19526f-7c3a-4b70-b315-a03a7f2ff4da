import {
  Base64
} from '@/utils/base64'
/**
 * 是否是空json对象
 * @param obj
 * @returns {boolean}
 */
export function isEmptyObject(obj) {
  return !obj || Object.keys(obj).length === 0
}

/**
 * 删除链接上的参数
 *
 */
export function delUrlParam(url, ref) {
  // 如果不包括此参数
  if (url.indexOf(ref) === -1) {
    return url
  }
  var arr_url = url.split('?')
  var base = arr_url[0]
  var arr_param = arr_url[1].split('&')
  var index = -1
  for (var i = 0; i < arr_param.length; i++) {
    var paired = arr_param[i].split('=')
    if (paired[0] === ref) {
      index = i
      break
    }
  }
  if (index === -1) {
    return url
  } else {
    arr_param.splice(index, 1)
    return base + '?' + arr_param.join('&')
  }
}

/**
 * 检验url是否合法
 * @param str_url
 * @returns {boolean}
 */
export function isUrl(strUrl) {
  // ftp的user@
  /* eslint-disable no-useless-escape */
  const strRegex =
    '^((https|http|ftp|rtsp|mms)?://)' +
    "?(([0-9a-z_!~*'().&=+$%-]+: )?[0-9a-z_!~*'().&=+$%-]+@)?" +
    // IP形式的URL- **************
    '(([0-9]{1,3}.){3}[0-9]{1,3}' +
    // 允许IP和DOMAIN（域名）
    '|' +
    // 域名- www.
    "([0-9a-z_!~*'()-]+.)*" +
    // 二级域名
    '([0-9a-z][0-9a-z-]{0,61})?[0-9a-z].' +
    // first level domain- .com or .museum
    '[a-z]{2,6})' +
    // 端口- :80
    '(:[0-9]{1,4})?' +
    // a slash isn't required if there is no file name
    '((/?)|' +
    "(/[0-9a-z_!~*'().;?:@&=+$,%#-]+)+/?)$"
  const re = new RegExp(strRegex)
  return re.test(strUrl)
}

/**
 * 从拼接字段获取pageId,跳转
 * @param url
 * @returns {string}
 */
export function redirectByPageIdOrUrl(url, isNav = false) {
  const pageUrl = concatPagePath(url)
  if (pageUrl !== '') {
    if (isUrl(url)) {
      location.href = pageUrl
    } else {
      if (!isNav) {
        location.hash = pageUrl
      } else {
        location.replace(`#${pageUrl}`)
      }
    }
  }
}

/**
 * 从拼接字段获取pageId
 * @param url
 * @returns {string}
 */
export function concatPagePath(url) {
  if (url) {
    if (isUrl(url)) {
      return url
    } else {
      const urlArr = url.split('://')
      if (urlArr.length > 1) {
        const route = urlArr[1].split('_')
        const pageId = route[route.length - 1]
        return `/page${pageId}`
      }
    }
  }
  return ''
}

/**
 * 获取连接上面参数
 * @param name
 * @returns {*}
 */
export function getQueryString(name) {
  var reg = new RegExp('(^|&)' + name + '=([^&]*)(&|$)', 'i')
  var r = window.location.search.substr(1).match(reg)
  if (r != null) return unescape(r[2])
  return null
}
export function getUrlKey(name) {
  return (
    decodeURIComponent(
      (new RegExp('[?|&]' + name + '=' + '([^&;]+?)(&|#|;|$)').exec(location.href) || ['', ''])[1].replace(/\+/g, '%20')
    ) || null
  )
}

function version(ver) {
  return ver.split('_')[2].replace(/\./g, '')
}

/*
 * 获取时间差（天数）
 * */
export function timeDiff(startTime, endTime) {
  startTime = startTime || new Date()
  endTime = endTime || new Date()
  // ios不支持yyyy-mm-dd格式，转换
  if (
    String(startTime)
      .split('')
      .includes('-')
  ) {
    startTime = new Date(startTime.replace(/-/g, '/'))
  }
  if (
    String(endTime)
      .split('')
      .includes('-')
  ) {
    endTime = new Date(endTime.replace(/-/g, '/'))
  }
  return parseInt((endTime.getTime() - startTime.getTime()) / (1000 * 60 * 60 * 24))
}

/*
 * 获取app参数
 * */
export function getAppInfo() {
  return new Promise((resolve, reject) => {
    try {
      const getAppParams = params => {
        params = params.replace(/,\"ipv6\":\"[^"]+"/i, '')
        const data = JSON.parse(params)
        data.longVer = data.ver
        data.ver = version(data.ver)
        resolve(data)
      }
      window.getTokenParams = getAppParams
      const jsonObject = {
        jsmethodname: 'getTokenParams',
        channelsign: '00001B'
      }
      window.aspireweb.appparamsselect(JSON.stringify(jsonObject))
    } catch (e) {
      // console.log('不是客户端打开')
      reject(e)
    }
  })
}

export function decodeNumber(str) {
  const base = new Base64()
  // console.log(str, '前')
  let result = ''
  const codeTable = ['YM', 'UJ', 'WO', 'DA', 'LN', 'CQ', 'BE', 'PV', 'ZF', 'RK']
  // str = str.replace(/[0-9]/ig, '').toLocaleUpperCase()
  str = base
    .decode(str)
    .replace(/[0-9]/gi, '')
    .toLocaleUpperCase()
  // console.log(str, 'str')
  str.split('').forEach(strItem => {
    codeTable.forEach((item, index) => {
      result += item.includes(strItem) ? index : ''
    })
  })
  // console.log(result, '后')
  return result
}

export function setTitle(val) {
  const shareName = val
  document.title = shareName
  const ua = navigator.userAgent
  if (/\bMicroMessenger\/([\d.]+)/.test(ua) && /ip(hone|od|ad)/i.test(ua)) {
    const i = document.createElement('iframe')
    i.src = '/favicon.ico'
    i.style.display = 'none'
    i.onload = function () {
      setTimeout(function () {
        i.remove()
      }, 9)
    }
    document.body.appendChild(i)
  }
}

export function isIosReload() {
  window.addEventListener('pageshow', (e) => {
    const isReload = sessionStorage.getItem('isReload') || false
    if (e.persisted && String(isReload) === 'true') {
      window.location.reload()
      sessionStorage.removeItem('isReload')
    }
  })
}
