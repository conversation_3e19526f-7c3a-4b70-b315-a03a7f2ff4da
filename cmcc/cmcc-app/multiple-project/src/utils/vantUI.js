// 按业务适合情况引入ui框架，手机ui自定义的比较强；
import Vue from 'vue'
import {
  Toast,
  Button,
  Row,
  Col,
  Field,
  Dialog,
  Loading,
  CountDown,
  Lazyload,
  Progress,
  Grid,
  GridItem,
  CouponCell,
  Popup,
  Card,
  Swipe,
  SwipeItem,
  ActionSheet,
  CouponList,
  Form,
  RadioGroup,
  Radio,
  Switch,
  SwipeCell,
  Collapse,
  CollapseItem,
  Icon,
  NavBar,
  Area,
  Checkbox,
  Image,
  NoticeBar,
  Cascader
} from 'vant'

import 'vant/lib/index.css'

Vue.use(Button)
  .use(Field)
  .use(Dialog)
  .use(Loading)
  .use(CountDown)
  .use(Lazyload)
  .use(Progress)
  .use(Grid)
  .use(GridItem)
  .use(CouponCell)
  .use(CouponList)
  .use(Row)
  .use(Col)
  .use(Popup)
  .use(Card)
  .use(Toast)
  .use(Swipe)
  .use(SwipeItem)
  .use(ActionSheet)
  .use(Form)
  .use(RadioGroup)
  .use(Radio)
  .use(Switch)
  .use(SwipeCell)
  .use(Collapse)
  .use(CollapseItem)
  .use(Icon)
  .use(NavBar)
  .use(Area)
  .use(Checkbox)
  .use(Image)
  .use(NoticeBar)
  .use(Cascader)
Vue.prototype.$toast = Toast
Vue.prototype.$dialog = Dialog
