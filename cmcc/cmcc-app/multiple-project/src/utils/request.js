import axios from 'axios'
import { goLogin } from '../../../../../bjapp-model/vue2/js/login/index'
import transit from '../../../../../bjapp-model/vue3-plus/js/utils/security/transit'
// import AspDialog from '@/components/AspDialog'

// Vue.use(AspDialog)
/* eslint no-undef: "error" */
// create an axios instance
const service = axios.create({
  baseURL: '', // api 的 base_url
  timeout: 10000, // request timeout
  method: 'post',
  headers: {
    'Content-type': 'application/json; charset=utf-8'
  }
})

// request interceptor
service.interceptors.request.use(
  config => {
    // 在发送请求之前做些什么
    if (!config.data) {
      config.data = config.params
    }
    config.data.token = config.data.token || sessionStorage.getItem('userToken') || ''
    /* 一键登录获取sign接口不需要token */
    if (config.data.delToken) {
      delete config.data.delToken
      delete config.data.token
    }
    if(config.encrypt === 2) {
      config.encryptKey = transit.getRandomAesKey()
      Object.assign(config.headers, {
        leaf: transit.encryptRsa(config.encryptKey)
      })
    } else if (config.encrypt) {
      config.encryptKey = transit.getRandomAesKey()
      Object.assign(config.headers, {
        leaf: transit.encryptRsa(config.encryptKey)
      })
      config.data = {
        encrypt: transit.encryptAes(JSON.stringify(config.data), config.encryptKey)
      }
    }
    console.log('config.head', config.head)
    if(config.head) { // 集团封控要求
      Object.assign(config.headers, {
        constid: config.head.constid
      })
    }
    return config
  },
  error => {
    // Do something with request error
    // console.log(error) // for debug
    Promise.reject(error)
  }
)

// response interceptor
service.interceptors.response.use(
  response => {
    let {
      result
    } = response.data
    let errcode = response.data.errcode
    result = String(result)
    errcode = String(errcode)
    if (result === '0' || errcode === '0') {
      // 检测到解密设置。解密返回数据
      if (response.config.encryptKey && response.data.encrypt) {
        response.data.encrypt = JSON.parse(transit.decryptAes(response.data.encrypt, response.config.encryptKey))
      }
      return response.data
    } else if (result === '-99999') {
      sessionStorage.removeItem('userToken')
      sessionStorage.removeItem('isBjUser')
      goLogin()
      return Promise.reject(response.data)
    } else if (result === '-100' || result === '-101') {
      // errorDialog(2)
      sessionStorage['set' + 'Item']('actExpired', true)
      return Promise.reject(response.data)
    } else if (result === '-102') {
      // errorDialog(3)
      sessionStorage['set' + 'Item']('actNoStart', true)
      return Promise.reject(response.data)
    } else {
      return response.data
    }
  },
  /**
   * 下面的注释为通过在response里，自定义code来标示请求状态
   * 当code返回如下情况则说明权限有问题，登出并返回到登录页
   * 如想通过 xmlhttprequest 来状态码标识 逻辑可写在下面error中
   * 以下代码均为样例，请结合自生需求加以修改，若不需要，则可删除
   */
  error => {
    console.log(error, '网络错误')
    const data = {
      result: '-60007',
      msg: '活动太火爆了，稍后再来吧~'
    }
    return Promise.reject(data)
  }
)

export default service
