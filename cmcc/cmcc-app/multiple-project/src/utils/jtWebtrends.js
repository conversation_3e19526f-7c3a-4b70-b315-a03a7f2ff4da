/*
 * @Author: zhen<PERSON><PERSON><PERSON> <EMAIL>
 * @Date: 2023-03-24 19:08:34
 * @LastEditors: zhengwen<PERSON> <EMAIL>
 * @LastEditTime: 2023-12-05 10:35:52
 * @FilePath: \multiple-project\src\utils\webtrends.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import gdpSdkMethods from '../../../../../bjapp-model/sdc/gdp-track'
// 插码统计
const jtWebtrends1 = {
  /**
   * 事件统计
   * @param  {string} name 事件名称
   */
  multiTrack: function (event, envName, { type, nextUrl, markId } = {}) {
    gdpSdkMethods.gdpCommonTrack(type || 'clk', event, envName, nextUrl, markId)
  },
  ...gdpSdkMethods
}

// window.$WEBTRENDS = Webtrends1
export default jtWebtrends1
