<template>
  <wx-open-launch-app
    v-if="wechatState"
    style="
      position: absolute;
      left: 0;
      top: 0;
      width: 100%;
      height: 100%;
      display: block;
      -webkit-tap-highlight-color: transparent;
    "
    :extinfo="hxappPath"
    appid="wxa48f0b9e1ed8f680"
  >
    <script type="text/wxtag-template">
      <style>
      .wx-btn {
      width: 300px;
      height: 87px;
      display: block;
      color: #fff;
      opacity: 0;
      -webkit-tap-highlight-color: transparent;
      }
      </style>
      <button
      class="wx-btn"
      >小程序</button>
    </script>
  </wx-open-launch-app>
</template>

<script>
import Webtrends1 from '@/utils/webtrends'
import CHANNEL from '@/utils/channel'
export default {
  name: 'WxOpenApp',
  props: {
    hxappPath: {
      type: String,
      default: 'bjcmcc://bjcmcc00001'
    },
    wm: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      wechatState: CHANNEL.isWXBrowser(), // 控制是否显示组件,
      timer: null
    }
  },
  destroyed() {
    clearTimeout(this.timer)
  },
  mounted() {
    const _this = this
    const launchBtn = this.$el
    if (!launchBtn) {
      return
    }
    launchBtn.addEventListener('launch', (e) => {
      _this.wm && Webtrends1.multiTrack(_this.wm)
      _this.timer = setTimeout(() => {
        _this.$emit('launch')
      }, 100)
    })
    launchBtn.addEventListener('error', (e) => {
      _this.wm && Webtrends1.multiTrack(_this.wm)
      _this.timer = setTimeout(() => {
        _this.$emit('error')
      }, 100)
    })
  }
}
</script>
