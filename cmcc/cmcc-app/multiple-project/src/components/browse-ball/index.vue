<template>
  <div
    v-if="show"
    :style="`z-index: ${zIndex};`"
    class="browse-ball"
    @touchstart="handleTouchStart"
    @touchmove="handleTouchMove"
    @touchend="handleTouchEnd"
    @click.stop="goAct"
  >
    <template v-if="showBack">
      <img class="browse-ball__back" src="./img/back.png"/>
    </template>
    <template  v-else>
      <img class="browse-ball__img" src="./img/ball.png" alt="">
      <div class="browse-ball__text">{{ text }}</div>
    </template>
  </div>
</template>

<script>
import Webtrends1 from '@/utils/webtrends'
import CHANNEL from '@/utils/channel'
import request from '@/utils/request'
import { getQueryString } from '@/utils/utils.js'

export default {
  props: {
    wm: String,
    envName: String,
    actKeyword: String,
    zIndex: {
      type: Number,
      default: 10005
    }
  },
  data() {
    return {
      drag: {
        obj: '',
        x: 0,
        y: 0,
        l: 0,
        t: 0,
        isDown: false
      },
      show: false,
      gray: CHANNEL.isGray(), // 是否是灰度环境-链接判断
      text: '浏览10秒得宝箱',
      countTime: 10, // 倒计时时间
      countTimer: null, // 倒计时定时器
      urlTimer: null, // 跳转链接定时器
      completeTask: false, // 是否完成浏览任务
      token: sessionStorage.getItem('userToken'),
      taskActKeyword: getQueryString('taskActKeyword'), // 是否从四月打卡活动跳转过来完成任务
      basePath: getQueryString('basePath'),
      showBack: false
    }
  },
  destroyed() {
    clearInterval(this.countTimer)
    clearTimeout(this.urlTimer)
  },
  mounted() {
    if (this.taskActKeyword) {
      // 页面加载完再调取，避免白屏时候出现
      this.init()
    }
  },
  methods: {
    // 跳转四月打卡活动
    goAct() {
      // 未完成任务
      if (!this.completeTask) return
      // 四月打卡活动活动链接
      const _url = this.gray ? 'https://st.bj.chinamobile.com:7443/shortAct/act104/index.html?channel=JT&backFrom=' + this.actKeyword : 'https://h5.bj.10086.cn/cmcc_activity/act104/index.html?backFrom=' + this.actKeyword
      // 设置插码
      if (this.showBack) {
        Webtrends1.multiTrack('P00000042189', this.envName + '_返回活动')
      } else {
        Webtrends1.multiTrack('P00000042190', this.envName + '_返回领宝箱')
      }
      // 插码设置需要，延迟链接的跳转
      this.urlTimer = setTimeout(() => {
        window['loca' + 'tion'].href = _url
      }, 200)
    },
    // 初始化活动状态
    init() {
      request.get(`/${this.basePath}/getTaskStatus/JT/${this.basePath}`, { params: { actkeyword: this.actKeyword } }).then(res => {
        const { result, isBj } = res || {}
        // 活动在推广且用户未完成打卡
        if (Number(result) === 0 && isBj) {
          this.show = true
          this.setCountDown()
        } else if (Number(result) === -100010) {
          this.show = true
          this.showBack = true
          this.completeTask = true
        }
      })
    },
    // 设置浏览倒计时
    setCountDown() {
      this.countTimer = setInterval(() => {
        // 浏览器结束，调设置任务接口设置用户状态
        if (this.countTime <= 0) {
          clearInterval(this.countTimer)
          this.setTaskStatus()
          return
        }
        this.countTime--
        this.text = '浏览' + this.countTime + '秒得宝箱'
      }, 1000)
    },
    // 设置任务完成状态
    setTaskStatus() {
      request.get(`/${this.basePath}/setTaskStatus/JT/${this.basePath}`, { params: { actkeyword: this.actKeyword } }).then(res => {
        const { result, errmsg } = res || {}
        // 0: 设置成功; 其他：报错误提示，并且隐藏
        if (Number(result) === 0) {
          this.text = '<返回领宝箱'
          this.completeTask = true
        } else {
          this.showErrToast(errmsg)
        }
      }).catch(err => {
        this.showErrToast(err.errmsg)
      })
    },
    // 展示错误提示
    showErrToast(errmsg) {
      this.show = false
      this.$toast(errmsg || '活动太火爆了，请稍后再试')
    },
    // 悬浮球监听函数1
    handleTouchStart(event) {
      this.drag.obj = this.$el
      // 获取x坐标和y坐标
      this.drag.x = event.clientX || event.changedTouches[0].clientX
      this.drag.y = event.clientY || event.changedTouches[0].clientY
      // 获取左部和顶部的偏移量
      this.drag.l = this.drag.obj.offsetLeft
      this.drag.t = this.drag.obj.offsetTop
      // 设置新的坐标
      this.x = this.drag.l
      this.y = this.drag.t
      // 开关打开
      this.drag.obj.style.cursor = 'move'
    },
    // 悬浮球监听函数2
    handleTouchMove(e) {
      e.preventDefault()
      // 获取x和y
      const nx = e.clientX || e.changedTouches[0].clientX
      const ny = e.clientY || e.changedTouches[0].clientY
      // 计算移动后的左偏移量和顶部的偏移量
      let nl = nx - (this.drag.x - this.drag.l)
      let nt = ny - (this.drag.y - this.drag.t)
      nl = (nl / window.innerWidth) * 100
      nt = (nt / window.innerHeight) * 100
      this.drag.obj.style.left = nl + 'vw'
      this.drag.obj.style.top = nt + 'vh'
    },
    // 悬浮球监听函数3
    handleTouchEnd() {
      // 开关关闭
      this.drag.obj.style.cursor = 'default'
    }
  }
}
</script>

<style lang="scss" scoped>
.browse-ball {
  width: fit-content;
  height: fit-content;
  position: fixed;
  top: 183px;
  left: 0;
  border-radius: 50%;
  z-index: 50;
  width: 165px;
  height: 133px;
  text-align: center;
  &__img {
    width: 100px;
    height: 100px;
  }
  &__back {
    width: 165px;
  }
  &__text {
    width: 165px;
    height: 45px;
    line-height: 45px;
    background-color: #b41c13;
    border-radius: 22px;
    font-size: 19px;
    color: #ffffff;
    border: solid 2px #ffffff;
  }
}
</style>
