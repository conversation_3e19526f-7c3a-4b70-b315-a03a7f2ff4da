<template>
  <div class="noticeDialog" v-if="showNotice && block.title !== 'no'" :style="`z-index:${zIndex}`">
    <div class="noticeDialog__content">
      <img class="noticeDialog__content__img" :src="block.imgurl" alt="">
      <div class="noticeDialog__content__look" @click="toDetails()">点击了解详情&gt;</div>
      <div class="noticeDialog__content__close" @click="close()"></div>
    </div>
  </div>
</template>

<script>
import Webtrends1 from '@/utils/webtrends'
import { newWebview } from '../../../../../../bjapp-model/vue2/js/jt-app-ability'
export default {
  props: {
    zIndex: {
      type: Number,
      default: 9999
    },
    block: {
      type: Object,
      default() {
        return {}
      }
    },
    ckwm: {
      type: Object,
      default() {
        return {}
      }
    },
    gbwm: {
      type: Object,
      default() {
        return {}
      }
    }
  },
  data() {
    return {
      showNotice: true
    }
  },
  methods: {
    toDetails() {
      Webtrends1.multiTrack(this.ckwm.event, this.ckwm.eventName, { nextUrl: this.block.url })
      setTimeout(() => {
        if (this.block.url) {
          this.showNotice = false
          newWebview(this.block.url)
        }
      }, 200)
    },
    close() {
      Webtrends1.multiTrack(this.gbwm.event, this.gbwm.eventName)
      setTimeout(() => {
        this.showNotice = false
      }, 200)
    }
  }
}
</script>

<style lang="scss" scoped>
.noticeDialog {
  width: 100%;
  position: fixed;
  top: 0;
  left: 0;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  &__content {
    width: 620px;
    height: 675px;
    position: relative;
    &__img {
      width: 620px;
      height: 675px;
      display: block;
    }
    &__look {
      width: 290px;
      height: 70px;
      font-size: 30px;
      color: #fff;
      background-color: rgba(0, 0, 0, 0.3);
      border-radius: 70px;
      position: absolute;
      text-align: center;
      line-height: 70px;
      bottom: 50px;
      left: 50%;
      z-index: 1;
      transform: translateX(-50%);
    }
    &__close {
      width: 85px;
      height: 85px;
      border-radius: 50%;
      background: url(./image/icon-close.png) center center no-repeat;
      background-size: 100% 100%;
      margin: 0 auto;
      margin-top: 60px;
    }
  }
}
</style>
