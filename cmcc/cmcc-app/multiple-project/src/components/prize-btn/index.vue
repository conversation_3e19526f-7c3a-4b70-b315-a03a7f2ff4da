<template>
  <div class="prize-button" @click="goPrize">
    <wx-open-app
      v-if="showGoApp"
      :idx="`launch-btn__appIndex__${item.listId}`"
      :hxapp-path="hxappPath"
      :wm="item.wm"
      @launch="goTransform()"
      @error="goTransform()"
    />
  </div>
</template>

<script>
import Webtrends1 from '@/utils/webtrends'
import { prizeTypeList } from './utils/prizeType'
import wxOpenApp from '@/components/wx-open-app'
import { newWebview } from '../../../../../../bjapp-model/vue2/js/jt-app-ability'
import CHANNEL from '@/utils/channel'
export default {
  name: 'PrizeButton',
  components: {
    wxOpenApp
  },
  props: {
    item: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      prizeTypeList: prizeTypeList,
      timer: null,
      type: 'type-' + String(this.item.prizeType)
    }
  },
  computed: {
    showGoApp() {
      return (
        this.item.showGoApp || this.prizeTypeList[this.type].showGoApp || false
      )
    },
    hxappPath() {
      return (
        this.item.hxappPath || this.prizeTypeList[this.type].hxappPath || ''
      )
    }
  },
  destroyed() {
    clearTimeout(this.timer)
  },
  methods: {
    goPrize() {
      // console.log(this.item)
      if (this.item.wm) {
        const codeArr = this.item.wm.split(',')
        Webtrends1.multiTrack(codeArr[0], codeArr[1])
      }
      this.timer = setTimeout(() => {
        if (this.showGoApp) {
          this.goUrlCheckChannel(
            this.prizeTypeList[this.type].appUrl,
            this.prizeTypeList[this.type].transformUrl
          )
        } else if (String(this.item.prizeType) === '6') {
          if (this.item.url) {
            window['loca' + 'tion'].href = this.item.url
          }
        }
      }, 100)
      this.$emit('click')
    },
    goUrlCheckChannel(url, transformUrl) {
      if (CHANNEL.isJTAPP() || CHANNEL.isGray()) {
        // window.$APPABILITY.viewCall(url)
        newWebview(url)
      } else {
        window['loca' + 'tion'].href = transformUrl
      }
    },
    goTransform() {
      window['loca' + 'tion'].href =
        this.item.transformUrl || this.prizeTypeList[this.type].transformUrl
    }
  }
}
</script>

<style lang="scss" scoped>
.prize-button {
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
}
</style>
