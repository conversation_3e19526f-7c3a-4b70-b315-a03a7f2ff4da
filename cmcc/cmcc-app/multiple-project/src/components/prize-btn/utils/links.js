import CHANNEL from '@/utils/channel'
// let appOrigin = 'https://h5.bj.10086.cn'
let actOrigin = 'https://h5.bj.10086.cn'
if (CHANNEL.isGray()) {
  // appOrigin = 'http://www.mobilebj.cn:7080'
  actOrigin = 'https://st.bj.chinamobile.com:7443'
}

const ENERGYURL = actOrigin + '/cmcc_vact/my-energy/index.html'
const ENERGYTRANSFOR = 'http://sc.bj.chinamobile.com/activity/loading/loading.html?backurl=' + encodeURIComponent(ENERGYURL)
// 集团卡券中心
const COPUPONURL = 'https://touch.10086.cn/i/mobile/mycoupons.html?c=11&e=99'
const COUPONTRANSFOR = 'http://sc.bj.chinamobile.com/activity/loading/loading.html?backurl=' + encodeURIComponent(COPUPONURL)

export {
  ENERGYURL,
  COPUPONURL,
  COUPONTRANSFOR,
  ENERGYTRANSFOR
}
