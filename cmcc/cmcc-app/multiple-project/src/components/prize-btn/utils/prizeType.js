import * as commonLinks from './links'
export const prizeTypeList = {
  'type-1': {
    desc: '流量卡券',
    alias: 'flow-coupon',
    btnName: '去使用',
    hxappPath: 'bjcmcc://bjcmcc01014?url=' + commonLinks.COPUPONURL,
    appUrl: commonLinks.COPUPONURL,
    transformUrl: commonLinks.COUPONTRANSFOR,
    showGoApp: true
  }, // 流量卡券
  'type-2': {
    desc: '实物名单',
    alias: 'shiwu-name',
    btnName: '去查看'
  }, // 实物名单
  'type-3': {
    desc: '实物快递',
    alias: 'shiwu-get',
    btnName: '去查看'
  }, // 实物快递
  'type-4': {
    desc: '三方券码',
    alias: 'other-channel-code',
    btnName: '去使用'
  }, // 三方券码
  'type-5': {
    desc: '谢谢惠顾',
    alias: 'no-prize'
  }, // 谢谢惠顾
  'type-6': {
    desc: '业务奖励',
    alias: 'business-prize',
    btnName: '去查看'
  }, // 业务奖励
  'type-7': {
    desc: '金币奖励',
    alias: 'gold-coins',
    btnName: '去看看'
  }, // 金币奖励
  'type-8': {
    desc: '实物卡券',
    alias: 'shiwu-coupon',
    btnName: '去使用',
    hxappPath: 'bjcmcc://bjcmcc01014?url=' + commonLinks.COPUPONURL,
    appUrl: commonLinks.COPUPONURL,
    transformUrl: commonLinks.COUPONTRANSFOR,
    showGoApp: true
  }, // 实物卡券
  'type-9': {
    desc: '抽奖机会',
    alias: 'prize-draw',
    btnName: '去抽奖'
  }, // 抽奖机会
  'type-12': {
    desc: '能量类型',
    alias: 'energy',
    btnName: '去使用',
    hxappPath: 'bjcmcc://bjcmcc01014?url=' + commonLinks.ENERGYURL,
    appUrl: commonLinks.ENERGYURL,
    transformUrl: commonLinks.ENERGYTRANSFOR,
    showGoApp: true
  } // 能量类型
}
