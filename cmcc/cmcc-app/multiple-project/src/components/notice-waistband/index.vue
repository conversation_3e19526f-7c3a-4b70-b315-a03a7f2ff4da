<template>
  <div class="waistbandBanner" v-if="waistbandBanner && waistbandBanner.length > 0">
    <img :src="waistbandBanner[0].imgurl" @click="goUrl" alt="">
  </div>
</template>

<script>
import Webtrends1 from '@/utils/webtrends'
import { newWebview } from '../../../../../../bjapp-model/vue2/js/jt-app-ability'
export default {
  props: {
    waistbandBanner: {
      type: Array,
      default() {
        return []
      }
    },
    wm: {
      type: Object,
      default() {
        return []
      }
    }
  },
  methods: {
    goUrl() {
      const event = this.wm.event
      const eventName = this.wm.eventName + '_' +  this.waistbandBanner[0].title
      Webtrends1.multiTrack(event, eventName, { nextUrl: this.waistbandBanner[0].url })
      setTimeout(() => {
        if (this.waistbandBanner.length > 0 && this.waistbandBanner[0].url) {
          newWebview(this.waistbandBanner[0].url)
        }
      }, 200)
    }
  }
}
</script>

<style lang="scss" scoped>
.waistbandBanner {
  margin-top: 10px;
  display: block;
  width: 100%;
  img {
    width: 100%;
    height: auto;
  }
}
</style>
