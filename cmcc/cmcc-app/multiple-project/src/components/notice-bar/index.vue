<template>
  <div class="topBar" v-show="showBar && text !== 'no'" :style="`z-index:${zIndex}`">
    <van-notice-bar
      left-icon="volume-o"
      :text="text"
      scrollable
      mode="closeable"
      @close="closeBar"
    />
  </div>
</template>

<script>

export default {
  props: {
    zIndex: {
      type: Number,
      default: 12
    },
    text: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      showBar: true
    }
  },
  mounted() {
    // 已经关闭，切换页面再回来，还是不显示
    if (sessionStorage.getItem('topBar') === 'false') {
      this.showBar = false
    }
  },
  methods: {
    closeBar() {
      sessionStorage['set' + 'Item']('topBar', 'false')
    }
  }
}
</script>

<style lang="scss" scoped>
.topBar {
  width: 100%;
  position: fixed;
  top: 0;
  left: 0;
  ::v-deep {
    .van-notice-bar__left-icon::before {
      display: none;
    }
    .van-notice-bar__left-icon {
      width: 29px;
      height: 26px;
      padding: 0;
      background: url(./image/volume.png) center center no-repeat;
      background-size: 100% 100%;
      min-width: auto;
      margin-right: 10px;
    }
    .van-notice-bar {
      background-color: #fff4d5;
      color: #fb6f32;
      font-size: 26px;
      height: 75px;
    }
    .van-notice-bar__right-icon::before {
      background-color: #fdb284;
      padding: 4px;
      border-radius: 50%;
      color: #fee8db;
    }
  }
}
</style>