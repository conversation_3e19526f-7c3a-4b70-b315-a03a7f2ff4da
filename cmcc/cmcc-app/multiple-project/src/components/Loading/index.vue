<template>
  <div v-if="show || wait" class="loading-model" :class="{'wait': wait}">
    <div>
      <van-loading type="spinner" color="#1989fa" />
      <p class="text">
        {{ text }}
      </p>
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      show: false,
      wait: false,
      waitHandler: null,
      text: '加载中，请稍候'
    }
  }
}
</script>

<style lang="scss" scoped>
.loading-model {
  position: fixed;
  left: 0;
  top: 0;
  right: 0;
  bottom: 0;
  z-index: 9999;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  &.wait {
    opacity: 0;
  }
  > div {
    text-align: center;
    /deep/ {
      .van-loading {
        margin: 0 auto;
      }
    }
  }
  .text {
    display: block;
    width: 100%;
    color: rgb(25, 137, 250);
    font-size: 30px;
    margin-top: 20px;
  }
}
.loading {
  width: 252px;
  height: 280px;
  border-radius: 100%;
  border: 5px #ffffff solid;
  border-right-color: #87ceeb;
  // animation: loading 1s linear infinite;
  margin: 0 auto;
}

@keyframes loading {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
</style>
