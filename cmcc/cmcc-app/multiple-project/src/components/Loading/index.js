/**
 * Created by linsang on 2019/9/30.
 */
import LoadingComponents from './index.vue'

const Loading = {}
Loading.install = (Vue) => {
  const ToastConstructor = Vue.extend(LoadingComponents)
  const instance = new ToastConstructor()
  instance.$mount(document.createElement('div'))
  document.body.appendChild(instance.$el)
  Vue.prototype.$loading = {
    show(text) {
      instance.show = true
      instance.text = text
    },
    hide() {
      instance.show = false
    },
    waitShow(time) {
      if (instance.waitHandler) {
        return true
      } else {
        instance.wait = true
        instance.waitHandler = setTimeout(() => {
          instance.wait = false
          instance.show = true
        }, time || 500)
        return false
      }
    },
    waitClose() {
      clearTimeout(instance.waitHandler)
      instance.waitHandler = null
      instance.wait = false
      instance.show = false
    }
  }
}

export default Loading
