<template>
  <div
    v-if="floatData.url && show"
    id="win"
    class="floatWin"
    @touchstart="handleTouchStart"
    @touchmove="handleTouchMove"
    @touchend="handleTouchEnd"
  >
    <img
      class="float-img"
      :src="floatData.imgurl"
      alt=""
      @click.stop="goAllWayUrl(floatData)"
    />
    <div class="close" @click="close"></div>
  </div>
</template>

<script>
import { newWebview } from '../../../../../../bjapp-model/vue2/js/jt-app-ability'
import Webtrends1 from '@/utils/webtrends'
export default {
  props: {
    floatData: {
      type: Object,
      default: () => {
        return {}
      }
    },
    floatClickWm: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  data() {
    return {
      drag: {
        obj: '',
        x: 0,
        y: 0,
        l: 0,
        t: 0,
        isDown: false
      },
      show: true
    }
  },
  methods: {
    // 跳转链接
    goAllWayUrl() {
      Webtrends1.multiTrack(this.floatClickWm.event, this.floatClickWm.eventName, { nextUrl: this.floatData.url })
      setTimeout(() => {
        if (this.floatData.url) {
          newWebview(this.floatData.url)
        }
      }, 200)
    },
    // 悬浮球监听函数1
    handleTouchStart(event) {
      this.drag.obj = this.$el
      // 获取x坐标和y坐标
      this.drag.x = event.clientX || event.changedTouches[0].clientX
      this.drag.y = event.clientY || event.changedTouches[0].clientY

      // 获取左部和顶部的偏移量
      this.drag.l = this.drag.obj.offsetLeft
      this.drag.t = this.drag.obj.offsetTop

      // 设置新的坐标
      this.x = this.drag.l
      this.y = this.drag.t

      // 开关打开
      this.drag.obj.style.cursor = 'move'
    },
    // 悬浮球监听函数2
    handleTouchMove(e) {
      e.preventDefault()
      // 获取x和y
      const nx = e.clientX || e.changedTouches[0].clientX
      const ny = e.clientY || e.changedTouches[0].clientY
      // const maxWidth = window.innerWidth - this.drag.obj.offsetWidth
      // const maxHeight = window.innerHeight - this.drag.obj.offsetHeight
      // 计算移动后的左偏移量和顶部的偏移量
      let nl = nx - (this.drag.x - this.drag.l)
      let nt = ny - (this.drag.y - this.drag.t)
      // if (nl < 5) {
      //     nl = nx
      // }
      // if (nt < 5) {
      //     nt = ny
      // }
      // if (!(nl <= 0 || nl >= maxWidth)) {
      nl = (nl / window.innerWidth) * 100
      nt = (nt / window.innerHeight) * 100
      this.drag.obj.style.left = nl + 'vw'
      // }
      // if (!(ny >= maxHeight || ny <= 0)) {
      this.drag.obj.style.top = nt + 'vh'
      // }
    },
    // 悬浮球监听函数3
    handleTouchEnd() {
      // 开关关闭
      this.drag.obj.style.cursor = 'default'
    },
    close() {
      this.show = false
    }
  }
}
</script>

<style lang="scss" scoped>
.floatWin {
  width: fit-content;
  height: fit-content;
  position: fixed;
  top: 67vh;
  left: 79vw;
  border-radius: 50%;
  z-index: 999;
  .float-img {
    width: 120px;
    height: 120px;
  }
  .close {
    width: 44px;
    height: 44px;
    border-radius: 44px;
    background: url('./img/close-float.jpg') no-repeat center center;
    background-size: 100% 100%;
    margin-left: 40px;
    margin-top: 10px;
  }
}
</style>
