export function dateFormat(value, format) {
  if (typeof value === 'string') {
    value = value.replace(/-/g, '/')
  }
  var t = new Date(value)
  var o = {
    'M+': t.getMonth() + 1, // month
    'd+': t.getDate(), // day
    'h+': t.getHours(), // hour
    'm+': t.getMinutes(), // minute
    's+': t.getSeconds(), // second
    'q+': Math.floor((t.getMonth() + 3) / 3), // quarter
    S: t.getMilliseconds() // millisecond
  }
  if (/(y+)/.test(format)) {
    format = format.replace(RegExp.$1, (t.getFullYear() + '').substr(4 - RegExp.$1.length))
  }
  for (var k in o) {
    if (new RegExp('(' + k + ')').test(format)) {
      format = format.replace(RegExp.$1, RegExp.$1.length === 1 ? o[k] : ('00' + o[k]).substr(('' + o[k]).length))
    }
  }
  return format
}

export function replaceFlowSuffix(value) {
  return value.replace('全国通用流量', '')
}

export function mbToGb(mbValue) {
  if (mbValue === 0) {
    return mbValue + 'MB'
  }
  if (mbValue && mbValue > 0 && typeof mbValue === 'number') {
    if (mbValue >= 1024) {
      mbValue = parseFloat((mbValue / 1024).toFixed(2)) + 'GB'
    } else {
      mbValue = mbValue + 'MB'
    }
    return mbValue
  } else {
    return '0MB'
  }
}
export function timeFormat(time) {
  var y = time.split('T')[0].split('-')[0]
  var m = time.split('T')[0].split('-')[1]
  var d = time.split('T')[0].split('-')[2]
  var h = time.split('T')[1].split(':')[0]
  var mm = time.split('T')[1].split(':')[1]
  if (h === 0 && mm === 0) {
    return `${y}-${m}-${d}`
  } else {
    return `${y}-${m}-${d} ${h}:${mm}`
  }
}
